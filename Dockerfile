# Stage 1: Build the application
FROM node:22-alpine AS builder

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Install @nestjs/cli as a development dependency
RUN npm install @nestjs/cli

# Copy the rest of the application code
COPY . .

# Build the application using npx
RUN npx nest build

# Stage 2: Create the final image
FROM node:22-alpine

# Set the working directory
WORKDIR /app

# Copy the built application from the builder stage
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/src ./src
COPY --from=builder /app/public ./public
COPY --from=builder /app/package*.json ./

# Install production dependencies and ts-node
RUN npm install --only=production
RUN npm install ts-node

# Expose the application port
EXPOSE 3000

# Start the application
CMD ["sh", "-c", "npm install --only=production && ./node_modules/.bin/ts-node ./node_modules/typeorm/cli migration:run -d ./src/core/databases/typeorm.config.ts && node ./dist/src/main"]