import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable, lastValueFrom } from 'rxjs';
import { RedisService } from '../redis.service';
import Redis from 'ioredis';

@Injectable()
export class UseCacheInterceptor implements NestInterceptor {
  private redisClient: Redis;

  constructor(private readonly redisService: RedisService) {
    this.redisClient = this.redisService.getClient('COMMON_CACHE_NAME');
  }

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    // Expired time at seconds
    const expiredTimenumber = 60 * 60 * 1;

    const request = context.switchToHttp().getRequest();
    const key = this.generateCacheKey(request);

    const cachedValue = await this.getCachedData(key);

    if (cachedValue) {
      return new Observable((observer) => {
        observer.next(cachedValue);
        observer.complete();
      });
    }

    const response = await lastValueFrom(next.handle());

    if (key.startsWith('GET_/video/suggest')) {
      await this.cacheData(key, response, 60);
    } else {
      await this.cacheData(key, response, expiredTimenumber);
    }

    return new Observable((observer) => {
      observer.next(response);
      observer.complete();
    });
  }

  private generateCacheKey(request: any): string {
    return `${request.method}_${request.url}`;
  }

  private async getCachedData(key: string): Promise<any | null> {
    const cachedValue = await this.redisClient.get(key);
    return cachedValue ? JSON.parse(cachedValue) : null;
  }

  private async cacheData(
    key: string,
    data: any,
    expriedTime: number,
  ): Promise<void> {
    await this.redisClient.set(key, JSON.stringify(data), 'EX', expriedTime);
  }
}
