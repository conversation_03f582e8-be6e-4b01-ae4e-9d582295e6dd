import { Provider } from '@nestjs/common';
// import Redis = require('ioredis');
// import * as Redis from 'ioredis';
import Redis from 'ioredis';
import { REDIS_CLIENT, REDIS_MODULE_OPTIONS } from './redis.constants';
import { RedisModuleAsyncOptions, RedisModuleOptions } from './redis.interface';
import { v4 as uuidv4 } from 'uuid';
// import { default as Redis } from 'ioredis';
// import { default as Redis } from 'ioredis';
// const redis = new Redis();

export class RedisClientError extends Error {}
export interface RedisClient {
  defaultKey: string;
  clients: Map<string, Redis>;
  size: number;
}
// const redis = new Redis();
async function getClient(options: RedisModuleOptions): Promise<Redis> {
  const { url } = options;
  if (!url) {
    throw new RedisClientError('Redis URL is required');
  }
  const client = new Redis(url)
    .on('connect', () => console.log('RedisCache connected!!'))
    .on('ready', () => console.log('RedisCache ready to receive commands!!'))
    .on('end', () => console.log('RedisCache close connection!!'))
    .on('error', (err) => console.log(' RedisCache @@@error: ', err));

  return client;
}

export const createClient = (): Provider => ({
  provide: REDIS_CLIENT,
  useFactory: async (
    options: RedisModuleOptions | RedisModuleOptions[],
  ): Promise<RedisClient> => {
    const clients = new Map<string, Redis>();
    let defaultKey = uuidv4();
    if (Array.isArray(options)) {
      await Promise.all(
        options.map(async (o) => {
          const key = o.name || defaultKey;
          if (clients.has(key)) {
            throw new RedisClientError(
              `${o.name || 'default'} client is exists`,
            );
          }
          clients.set(key, await getClient(o));
        }),
      );
    } else {
      if (options.name && options.name.length !== 0) {
        defaultKey = options.name;
      }
      clients.set(defaultKey, await getClient(options));
    }

    return {
      defaultKey,
      clients,
      size: clients.size,
    };
  },
  inject: [REDIS_MODULE_OPTIONS],
});

export const createAsyncClientOptions = (options: RedisModuleAsyncOptions) => ({
  provide: REDIS_MODULE_OPTIONS,
  useFactory: options.useFactory as (
    ...args: any[]
  ) => Promise<RedisModuleOptions | RedisModuleOptions[]>,
  inject: options.inject,
});
