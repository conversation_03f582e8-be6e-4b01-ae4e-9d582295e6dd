import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Pool utilization metrics aggregated data schema
 */
@Schema({ 
  collection: 'pool_utilization_metrics',
  timestamps: true
})
export class PoolUtilizationMetrics extends Document {
  @Prop({ required: true, index: true })
  timeframe: string; // 'hourly' | 'daily' | 'weekly' | 'monthly'

  @Prop({ required: true, index: true })
  timestamp: Date;

  @Prop({ required: true })
  totalUniqueQuestionsInPool: number;

  @Prop({ required: true })
  uniqueQuestionsUsed: number;

  @Prop({ required: true })
  utilizationRate: number;

  @Prop({ type: Object })
  subjectBreakdown: Record<string, {
    totalQuestions: number;
    usedQuestions: number;
    utilizationRate: number;
  }>;

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const PoolUtilizationMetricsSchema = SchemaFactory.createForClass(PoolUtilizationMetrics);

// Create compound indexes
PoolUtilizationMetricsSchema.index({ timeframe: 1, timestamp: -1 });
PoolUtilizationMetricsSchema.index({ timestamp: -1 });
