import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * Question reuse frequency metrics schema
 */
@Schema({ 
  collection: 'question_reuse_metrics',
  timestamps: true
})
export class QuestionReuseMetrics extends Document {
  @Prop({ required: true, index: true })
  timeframe: string; // 'hourly' | 'daily' | 'weekly' | 'monthly'

  @Prop({ required: true, index: true })
  timestamp: Date;

  @Prop({ required: true })
  averageReuseFrequency: number;

  @Prop([{
    questionId: { type: String, required: true },
    usageCount: { type: Number, required: true },
    questionType: { type: String, required: true },
    subject: { type: String, required: true }
  }])
  mostReusedQuestions: Array<{
    questionId: string;
    usageCount: number;
    questionType: string;
    subject: string;
  }>;

  @Prop({ type: Object })
  reuseDistribution: Record<string, number>; // usage count -> number of questions

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const QuestionReuseMetricsSchema = SchemaFactory.createForClass(QuestionReuseMetrics);

// Create compound indexes
QuestionReuseMetricsSchema.index({ timeframe: 1, timestamp: -1 });
QuestionReuseMetricsSchema.index({ timestamp: -1 });
