import { IsOptional, <PERSON><PERSON><PERSON><PERSON>tring, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export enum TimeframeEnum {
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly'
}

export enum EventTypeEnum {
  QUESTION_SELECTION = 'question_selection',
  QUESTION_GENERATION = 'question_generation',
  VALIDATION_ATTEMPT = 'validation_attempt',
  CACHE_INTERACTION = 'cache_interaction',
  WORKSHEET_GENERATION = 'worksheet_generation',
  DISTRIBUTION_ANALYSIS = 'distribution_analysis'
}

export class MonitoringQueryDto {
  @ApiPropertyOptional({
    description: 'Start date for the query range (ISO string)',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for the query range (ISO string)',
    example: '2024-01-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Timeframe for aggregated metrics',
    enum: TimeframeEnum,
    example: TimeframeEnum.DAILY
  })
  @IsOptional()
  @IsEnum(TimeframeEnum)
  timeframe?: TimeframeEnum;

  @ApiPropertyOptional({
    description: 'Filter by specific event type',
    enum: EventTypeEnum,
    example: EventTypeEnum.QUESTION_SELECTION
  })
  @IsOptional()
  @IsEnum(EventTypeEnum)
  eventType?: EventTypeEnum;

  @ApiPropertyOptional({
    description: 'Filter by user ID',
    example: 'user-123'
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Filter by worksheet ID',
    example: 'worksheet-456'
  })
  @IsOptional()
  @IsString()
  worksheetId?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of events to return',
    example: 1000,
    minimum: 1,
    maximum: 10000
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10000)
  @Transform(({ value }) => parseInt(value))
  limit?: number;
}

export class DashboardMetricsQueryDto {
  @ApiPropertyOptional({
    description: 'Start date for the metrics range (ISO string)',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for the metrics range (ISO string)',
    example: '2024-01-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Timeframe for aggregated metrics',
    enum: TimeframeEnum,
    example: TimeframeEnum.DAILY,
    default: TimeframeEnum.DAILY
  })
  @IsOptional()
  @IsEnum(TimeframeEnum)
  timeframe?: TimeframeEnum = TimeframeEnum.DAILY;
}

export class CacheMetricsQueryDto extends DashboardMetricsQueryDto {
  @ApiPropertyOptional({
    description: 'Cache type to filter by',
    example: 'question_pool',
    enum: ['question_pool', 'worksheet_document', 'query_cache']
  })
  @IsOptional()
  @IsString()
  cacheType?: string;
}
