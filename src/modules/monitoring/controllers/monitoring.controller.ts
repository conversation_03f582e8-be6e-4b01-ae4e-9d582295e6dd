import { 
  Controller, 
  Get, 
  Query, 
  UseGuards, 
  Logger,
  BadRequestException 
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiQuery 
} from '@nestjs/swagger';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/role.decorator';
import { EUserRole } from '../../user/dto/create-user.dto';
import { PoolMonitoringService } from '../services/pool-monitoring.service';
import { 
  MonitoringQueryDto, 
  DashboardMetricsQueryDto, 
  CacheMetricsQueryDto,
  TimeframeEnum 
} from '../dto/monitoring-query.dto';
import { 
  DashboardMetricsResponseDto,
  PoolUtilizationResponseDto,
  QuestionReuseResponseDto,
  GenerationTimeResponseDto,
  ValidationResponseDto,
  CacheResponseDto
} from '../dto/monitoring-response.dto';

@ApiTags('Monitoring & Analytics')
@Controller('admin/monitoring')
@UseGuards(AuthGuard, RoleGuard)
@Roles(EUserRole.ADMIN)
@ApiBearerAuth()
export class MonitoringController {
  private readonly logger = new Logger(MonitoringController.name);

  constructor(
    private readonly poolMonitoringService: PoolMonitoringService
  ) {}

  @Get('dashboard')
  @ApiOperation({ 
    summary: 'Get comprehensive dashboard metrics for question pool monitoring',
    description: 'Returns aggregated metrics including pool utilization, question reuse, generation times, validation rates, and cache performance. Requires admin role.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Dashboard metrics successfully retrieved',
    type: DashboardMetricsResponseDto
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid query parameters' })
  async getDashboardMetrics(
    @Query() query: DashboardMetricsQueryDto
  ): Promise<DashboardMetricsResponseDto> {
    try {
      const { startDate, endDate, timeframe } = this.validateAndParseQuery(query);

      this.logger.log(`Getting dashboard metrics for ${timeframe} from ${startDate.toISOString()} to ${endDate.toISOString()}`);

      const metrics = await this.poolMonitoringService.getDashboardMetrics(
        startDate,
        endDate,
        timeframe
      );

      return metrics as DashboardMetricsResponseDto;
    } catch (error) {
      this.logger.error(`Error getting dashboard metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('pool-utilization')
  @ApiOperation({ 
    summary: 'Get pool utilization metrics',
    description: 'Returns metrics about how effectively the question pool is being utilized'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Pool utilization metrics',
    type: PoolUtilizationResponseDto
  })
  async getPoolUtilization(
    @Query() query: DashboardMetricsQueryDto
  ): Promise<PoolUtilizationResponseDto> {
    try {
      const { startDate, endDate, timeframe } = this.validateAndParseQuery(query);

      const metrics = await this.poolMonitoringService.calculatePoolUtilization(
        startDate,
        endDate,
        timeframe
      );

      return metrics as PoolUtilizationResponseDto;
    } catch (error) {
      this.logger.error(`Error getting pool utilization: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('question-reuse')
  @ApiOperation({ 
    summary: 'Get question reuse frequency metrics',
    description: 'Returns metrics about how frequently questions are being reused'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Question reuse metrics',
    type: QuestionReuseResponseDto
  })
  async getQuestionReuse(
    @Query() query: DashboardMetricsQueryDto
  ): Promise<QuestionReuseResponseDto> {
    try {
      const { startDate, endDate, timeframe } = this.validateAndParseQuery(query);

      const metrics = await this.poolMonitoringService.calculateQuestionReuse(
        startDate,
        endDate,
        timeframe
      );

      return metrics as QuestionReuseResponseDto;
    } catch (error) {
      this.logger.error(`Error getting question reuse: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('generation-times')
  @ApiOperation({ 
    summary: 'Get generation time comparison metrics',
    description: 'Returns comparison of generation times between pool selection and AI generation'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Generation time comparison metrics',
    type: GenerationTimeResponseDto
  })
  async getGenerationTimes(
    @Query() query: DashboardMetricsQueryDto
  ): Promise<GenerationTimeResponseDto> {
    try {
      const { startDate, endDate, timeframe } = this.validateAndParseQuery(query);

      const metrics = await this.poolMonitoringService.calculateGenerationTimeComparison(
        startDate,
        endDate,
        timeframe
      );

      return metrics as GenerationTimeResponseDto;
    } catch (error) {
      this.logger.error(`Error getting generation times: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('validation-metrics')
  @ApiOperation({ 
    summary: 'Get validation success rate metrics',
    description: 'Returns metrics about content validation success rates and issue breakdown'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Validation metrics',
    type: ValidationResponseDto
  })
  async getValidationMetrics(
    @Query() query: DashboardMetricsQueryDto
  ): Promise<ValidationResponseDto> {
    try {
      const { startDate, endDate, timeframe } = this.validateAndParseQuery(query);

      const metrics = await this.poolMonitoringService.calculateValidationMetrics(
        startDate,
        endDate,
        timeframe
      );

      return metrics as ValidationResponseDto;
    } catch (error) {
      this.logger.error(`Error getting validation metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('cache-metrics')
  @ApiOperation({ 
    summary: 'Get cache performance metrics',
    description: 'Returns cache hit/miss ratios and performance metrics for specified cache type'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Cache performance metrics',
    type: CacheResponseDto
  })
  @ApiQuery({ 
    name: 'cacheType', 
    required: false, 
    description: 'Cache type to filter by',
    enum: ['question_pool', 'worksheet_document', 'query_cache']
  })
  async getCacheMetrics(
    @Query() query: CacheMetricsQueryDto
  ): Promise<CacheResponseDto> {
    try {
      const { startDate, endDate, timeframe } = this.validateAndParseQuery(query);
      const cacheType = query.cacheType || 'question_pool';

      const metrics = await this.poolMonitoringService.calculateCacheMetrics(
        startDate,
        endDate,
        cacheType,
        timeframe
      );

      return metrics as CacheResponseDto;
    } catch (error) {
      this.logger.error(`Error getting cache metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('events')
  @ApiOperation({ 
    summary: 'Get raw monitoring events',
    description: 'Returns raw monitoring events for detailed analysis. Use with caution as this can return large datasets.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Raw monitoring events',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          eventId: { type: 'string' },
          type: { type: 'string' },
          timestamp: { type: 'string', format: 'date-time' },
          userId: { type: 'string' },
          worksheetId: { type: 'string' },
          eventData: { type: 'object' }
        }
      }
    }
  })
  async getEvents(@Query() query: MonitoringQueryDto) {
    try {
      const startDate = query.startDate ? new Date(query.startDate) : new Date(Date.now() - 24 * 60 * 60 * 1000);
      const endDate = query.endDate ? new Date(query.endDate) : new Date();
      const limit = query.limit || 1000;

      const events = await this.poolMonitoringService.getEvents(
        query.eventType,
        startDate,
        endDate,
        query.userId,
        query.worksheetId,
        limit
      );

      return events;
    } catch (error) {
      this.logger.error(`Error getting events: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate and parse query parameters
   */
  private validateAndParseQuery(query: DashboardMetricsQueryDto) {
    const now = new Date();
    const defaultStartDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); // 7 days ago

    const startDate = query.startDate ? new Date(query.startDate) : defaultStartDate;
    const endDate = query.endDate ? new Date(query.endDate) : now;
    const timeframe = query.timeframe || TimeframeEnum.DAILY;

    // Validate date range
    if (startDate >= endDate) {
      throw new BadRequestException('Start date must be before end date');
    }

    // Validate date range is not too large
    const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysDiff > 365) {
      throw new BadRequestException('Date range cannot exceed 365 days');
    }

    return { startDate, endDate, timeframe };
  }
}
