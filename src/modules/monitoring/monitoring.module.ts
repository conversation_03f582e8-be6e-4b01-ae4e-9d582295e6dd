import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PoolMonitoringService } from './services/pool-monitoring.service';
import { MonitoringController } from './controllers/monitoring.controller';

// Import schemas
import { MonitoringEvent, MonitoringEventSchema } from './schemas/monitoring-event.schema';
import { PoolUtilizationMetrics, PoolUtilizationMetricsSchema } from './schemas/pool-utilization-metrics.schema';
import { QuestionReuseMetrics, QuestionReuseMetricsSchema } from './schemas/question-reuse-metrics.schema';
import { GenerationTimeMetrics, GenerationTimeMetricsSchema } from './schemas/generation-time-metrics.schema';
import { ValidationMetrics, ValidationMetricsSchema } from './schemas/validation-metrics.schema';
import { CacheMetrics, CacheMetricsSchema } from './schemas/cache-metrics.schema';

// Import other modules that we might need
import { AuthModule } from '../auth/auth.module';
import { UserModule } from '../user/user.module';
import { QuestionPoolModule } from '../question-pool/question-pool.module';

@Module({
  imports: [
    // Register MongoDB schemas
    MongooseModule.forFeature([
      { name: MonitoringEvent.name, schema: MonitoringEventSchema },
      { name: PoolUtilizationMetrics.name, schema: PoolUtilizationMetricsSchema },
      { name: QuestionReuseMetrics.name, schema: QuestionReuseMetricsSchema },
      { name: GenerationTimeMetrics.name, schema: GenerationTimeMetricsSchema },
      { name: ValidationMetrics.name, schema: ValidationMetricsSchema },
      { name: CacheMetrics.name, schema: CacheMetricsSchema },
    ]),
    
    // Import auth modules for role-based access control
    forwardRef(() => AuthModule),
    forwardRef(() => UserModule),

    // Import question pool module for accessing question counts
    forwardRef(() => QuestionPoolModule),
  ],
  controllers: [MonitoringController],
  providers: [PoolMonitoringService],
  exports: [PoolMonitoringService], // Export for use in other modules
})
export class MonitoringModule {}
