/**
 * Interface for file metadata
 */
export interface FileMetadata {
  fileId: string;
  originalName: string;
  filename: string;
  mimeType: string;
  size: number;
  path: string;
  description?: string;
  category?: string;
  tags?: string[];
  uploadedBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Options for file upload
 */
export interface FileUploadOptions {
  description?: string;
  category?: string;
  tags?: string[];
  userId?: string;
}

/**
 * Result of file upload operation
 */
export interface FileUploadResult {
  success: boolean;
  file: FileMetadata | null;
  message?: string;
}

/**
 * Result of file query operation
 */
export interface FileQueryResult {
  files: FileMetadata[];
  total: number;
  page: number;
  limit: number;
}

/**
 * Options for file query
 */
export interface FileQueryOptions {
  page?: number;
  limit?: number;
  category?: string;
  tags?: string[];
  uploadedBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}