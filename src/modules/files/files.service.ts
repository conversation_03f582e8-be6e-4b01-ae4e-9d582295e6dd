import { Injectable, Logger, NotFoundException, BadRequestException, InternalServerErrorException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { createReadStream, createWriteStream, promises as fsPromises } from 'fs';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import * as mime from 'mime-types';
import sanitize from 'sanitize-filename';
import { File } from './schemas/file.schema';
import {
  FileMetadata,
  FileUploadOptions,
  FileUploadResult,
  FileQueryOptions,
  FileQueryResult
} from './interfaces/file.interface';
import { FileUpdateDto } from './dto/file-update.dto';
import { ConfigService } from '@nestjs/config';
import {Readable} from "node:stream";

@Injectable()
export class FilesService {
  private readonly logger = new Logger(FilesService.name);
  private readonly uploadDir: string;
  private readonly maxFileSize: number;
  private readonly allowedMimeTypes: string[];

  constructor(
    @InjectModel(File.name) private fileModel: Model<File>,
    private configService: ConfigService,
  ) {
    // Get configuration from environment variables or use defaults
    this.uploadDir = this.configService.get<string>('FILE_UPLOAD_PATH') || join(process.cwd(), 'uploads');
    this.maxFileSize = this.configService.get<number>('MAX_FILE_SIZE') || 104857600; // 100MB default
    this.allowedMimeTypes = this.configService.get<string>('ALLOWED_MIME_TYPES')?.split(',') || [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv',
    ];

    // Ensure upload directory exists
    this.ensureUploadDirExists();
  }

  /**
   * Ensure the upload directory exists
   */
  private async ensureUploadDirExists(): Promise<void> {
    try {
      await fsPromises.access(this.uploadDir);
    } catch (error) {
      // Directory doesn't exist, create it
      await fsPromises.mkdir(this.uploadDir, { recursive: true });
      this.logger.log(`Created upload directory: ${this.uploadDir}`);
    }
  }

  /**
   * Validate file before upload
   * @param file The file to validate
   */
  private validateFile(file: Express.Multer.File): void {
    // Check file size
    if (file.size > this.maxFileSize) {
      throw new BadRequestException(`File size exceeds the limit of ${this.maxFileSize / 1024 / 1024}MB`);
    }

    // Check file type
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(`File type ${file.mimetype} is not allowed`);
    }
  }

  /**
   * Generate a safe filename
   * @param originalName The original filename
   * @returns A safe filename
   */
  private generateSafeFilename(originalName: string): string {
    const fileExt = mime.extension(mime.lookup(originalName) || 'application/octet-stream');
    const sanitizedName = sanitize(originalName.replace(/\.[^/.]+$/, '')); // Remove extension before sanitizing
    const uniqueId = uuidv4();
    return `${sanitizedName}-${uniqueId}.${fileExt}`;
  }

  /**
   * Upload a single file
   * @param file The file to upload
   * @param options Upload options
   * @returns The upload result
   */
  async uploadFile(file: Express.Multer.File, options?: FileUploadOptions): Promise<FileUploadResult> {
    try {
      // Validate file
      this.validateFile(file);

      // Generate a safe filename
      const fileId = uuidv4();
      const safeFilename = this.generateSafeFilename(file.originalname);
      const filePath = join(this.uploadDir, safeFilename);

      // Create a write stream to save the file
      const writeStream = createWriteStream(filePath);

      // Create a buffer from the file buffer
      const buffer = Buffer.from(file.buffer);

      // Write the buffer to the file
      writeStream.write(buffer);
      writeStream.end();

      // Create file metadata
      const fileMetadata: FileMetadata = {
        fileId,
        originalName: file.originalname,
        filename: safeFilename,
        mimeType: file.mimetype,
        size: file.size,
        path: filePath,
        description: options?.description,
        category: options?.category,
        tags: options?.tags,
        uploadedBy: options?.userId,
      };

      // Save file metadata to database
      const newFile = new this.fileModel(fileMetadata);
      await newFile.save();

      return {
        success: true,
        file: fileMetadata,
      };
    } catch (error) {
      this.logger.error(`Error uploading file: ${error.message}`, error.stack);
      return {
        success: false,
        file: null,
        message: error.message,
      };
    }
  }

  /**
   * Upload multiple files
   * @param files The files to upload
   * @param options Upload options
   * @returns The upload results
   */
  async uploadFiles(files: Express.Multer.File[], options?: FileUploadOptions): Promise<FileUploadResult[]> {
    const results: FileUploadResult[] = [];

    for (const file of files) {
      const result = await this.uploadFile(file, options);
      results.push(result);
    }

    return results;
  }

  /**
   * Get file by ID
   * @param fileId The file ID
   * @returns The file metadata
   */
  async getFileById(fileId: string): Promise<FileMetadata> {
    const file = await this.fileModel.findOne({ fileId }).exec();
    if (!file) {
      throw new NotFoundException(`File with ID ${fileId} not found`);
    }
    return file.toObject();
  }

  /**
   * Get files with pagination and filtering
   * @param options Query options
   * @returns The query result
   */
  async getFiles(options?: FileQueryOptions): Promise<FileQueryResult> {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};
    if (options?.category) {
      query.category = options.category;
    }
    if (options?.tags && options.tags.length > 0) {
      query.tags = { $in: options.tags };
    }
    if (options?.uploadedBy) {
      query.uploadedBy = options.uploadedBy;
    }

    // Build sort
    const sort: any = {};
    sort[options?.sortBy || 'createdAt'] = options?.sortOrder === 'asc' ? 1 : -1;

    // Execute query
    const [files, total] = await Promise.all([
      this.fileModel.find(query).sort(sort).skip(skip).limit(limit).exec(),
      this.fileModel.countDocuments(query).exec(),
    ]);

    return {
      files: files.map(file => file.toObject()),
      total,
      page,
      limit,
    };
  }

  /**
   * Update file metadata
   * @param fileId The file ID
   * @param updateDto The update data
   * @returns The updated file metadata
   */
  async updateFile(fileId: string, updateDto: FileUpdateDto): Promise<FileMetadata> {
    const file = await this.fileModel.findOne({ fileId }).exec();
    if (!file) {
      throw new NotFoundException(`File with ID ${fileId} not found`);
    }

    // Update fields
    if (updateDto.description !== undefined) {
      file.description = updateDto.description;
    }
    if (updateDto.category !== undefined) {
      file.category = updateDto.category;
    }
    if (updateDto.tags !== undefined) {
      file.tags = updateDto.tags;
    }

    // Save changes
    await file.save();
    return file.toObject();
  }

  /**
   * Delete file
   * @param fileId The file ID
   * @returns True if successful
   */
  async deleteFile(fileId: string): Promise<boolean> {
    const file = await this.fileModel.findOne({ fileId }).exec();
    if (!file) {
      throw new NotFoundException(`File with ID ${fileId} not found`);
    }

    // Delete file from filesystem
    try {
      await fsPromises.unlink(file.path);
    } catch (error) {
      this.logger.error(`Error deleting file from filesystem: ${error.message}`, error.stack);
      // Continue with deletion from database even if file doesn't exist on filesystem
    }

    // Delete file metadata from database
    await file.deleteOne();
    return true;
  }

  /**
   * Create a read stream for a file
   * @param fileId The file ID
   * @returns A read stream for the file
   */
  async createFileReadStream(fileId: string): Promise<{ stream: Readable; file: FileMetadata }> {
    this.logger.log(`Attempting to create read stream for file ID: ${fileId}`);
    const file = await this.fileModel.findOne({ fileId }).exec();
    if (!file) {
      this.logger.warn(`File metadata not found in DB for ID: ${fileId}`);
      throw new NotFoundException(`File with ID ${fileId} not found in database.`);
    }

    this.logger.log(`Found file metadata for ${file.originalName} (ID: ${fileId}). Path: ${file.path}`);

    try {
      // Check if file exists on filesystem
      this.logger.debug(`Checking filesystem access for: ${file.path}`);
      await fsPromises.access(file.path, fsPromises.constants.R_OK); // Check for read access
      this.logger.debug(`Filesystem access confirmed for: ${file.path}`);

      // Create read stream
      const stream = createReadStream(file.path);

      stream.on('error', (err) => {
        this.logger.error(`Error during streaming file ${file.originalName} (ID: ${fileId}): ${err.message}`, err.stack);
        // This error will be handled by the controller or NestJS's StreamableFile
      });

      stream.on('end', () => {
        this.logger.log(`File streaming ended for ${file.originalName} (ID: ${fileId})`);
      });

      this.logger.log(`Successfully created read stream for ${file.originalName} (ID: ${fileId})`);
      return {
        stream,
        file: file.toObject(),
      };
    } catch (error) {
      this.logger.error(`Error accessing or creating stream for file ${file.originalName} (ID: ${fileId}) at path ${file.path}: ${error.message}`, error.stack);
      if (error.code === 'ENOENT') {
        throw new NotFoundException(`File ${file.originalName} (ID: ${fileId}) not found on storage.`);
      } else if (error.code === 'EACCES') {
        throw new ForbiddenException(`Permission denied accessing file ${file.originalName} (ID: ${fileId}).`);
      } else {
        throw new InternalServerErrorException(`Error accessing file ${file.originalName} (ID: ${fileId}): ${error.message}`);
      }
    }
  }

  /**
   * Get file buffer
   * @param fileId The file ID
   * @returns The file buffer
   */
  async getFileBuffer(fileId: string): Promise<Buffer> {
    try {
      // Get file metadata
      const fileMetadata = await this.getFileById(fileId);

      // Check if file exists on filesystem
      await fsPromises.access(fileMetadata.path);

      // Read file as buffer
      const fileBuffer = await fsPromises.readFile(fileMetadata.path);

      this.logger.log(`Successfully read file ${fileMetadata.filename} as buffer`);
      return fileBuffer;
    } catch (error) {
      this.logger.error(`Error reading file as buffer: ${error.message}`, error.stack);
      throw new NotFoundException(`Failed to read file as buffer: ${error.message}`);
    }
  }

  /**
   * Encode a PDF file to base64 (deprecated - use getFileBuffer instead)
   * @param fileId The file ID
   * @returns The base64 encoded string
   * @deprecated Use getFileBuffer instead
   */
  async encodePDFToBase64(fileId: string): Promise<string> {
    this.logger.warn('encodePDFToBase64 is deprecated. Use getFileBuffer instead.');
    const fileBuffer = await this.getFileBuffer(fileId);
    return fileBuffer.toString('base64');
  }

}
