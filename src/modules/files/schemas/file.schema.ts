import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class File extends Document {
  @Prop({ required: true, unique: true })
  fileId: string;

  @Prop({ required: true })
  originalName: string;

  @Prop({ required: true })
  filename: string;

  @Prop({ required: true })
  mimeType: string;

  @Prop({ required: true })
  size: number;

  @Prop({ required: true })
  path: string;

  @Prop()
  description: string;

  @Prop()
  category: string;

  @Prop()
  tags: string[];

  @Prop()
  uploadedBy: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const FileSchema = SchemaFactory.createForClass(File);