import {
  <PERSON>,
  <PERSON>,
  Param,
  Res,
  NotFoundException,
  StreamableFile,
  Logger,
} from '@nestjs/common';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiParam } from '@nestjs/swagger';
import * as fs from 'fs';
import * as path from 'path';
import { createReadStream } from 'fs';
import { ConfigService } from '@nestjs/config';
import { Public } from '../../auth/decorators/public.decorator';

@ApiTags('images')
@Controller('files/images')
export class ImagesController {
  private readonly logger = new Logger(ImagesController.name);
  private readonly imageStoragePath: string;

  constructor(private configService: ConfigService) {
    this.imageStoragePath = this.configService.get<string>('IMAGE_STORAGE_PATH') || 
                           path.join(process.cwd(), 'uploads', 'images');
  }

  @Get(':filename')
  @Public()
  @ApiOperation({ summary: 'Get an image file by filename' })
  @ApiParam({ name: 'filename', description: 'Image filename' })
  async getImage(
    @Param('filename') filename: string,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      const imagePath = path.join(this.imageStoragePath, filename);
      
      // Check if file exists
      if (!fs.existsSync(imagePath)) {
        this.logger.error(`Image file not found: ${imagePath}`);
        throw new NotFoundException(`Image file not found: ${filename}`);
      }
      
      // Determine content type based on file extension
      const ext = path.extname(filename).toLowerCase();
      let contentType = 'application/octet-stream';
      
      if (ext === '.png') contentType = 'image/png';
      else if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';
      else if (ext === '.gif') contentType = 'image/gif';
      else if (ext === '.svg') contentType = 'image/svg+xml';
      
      // Set appropriate headers
      res.set({
        'Content-Type': contentType,
        'Content-Disposition': `inline; filename="${filename}"`,
      });
      
      // Create a read stream
      const fileStream = createReadStream(imagePath);
      
      return new StreamableFile(fileStream);
    } catch (error) {
      this.logger.error(`Error serving image: ${error.message}`, error.stack);
      throw new NotFoundException(`Image not found: ${filename}`);
    }
  }
}
