import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UploadedFile,
  UploadedFiles,
  UseInterceptors,
  BadRequestException,
  NotFoundException,
  HttpStatus,
  Res,
  StreamableFile,
  Logger
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiConsumes, ApiBody, ApiParam, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { Readable } from 'stream'; // Added Readable
import { FilesService } from './files.service';
import { FileUploadDto } from './dto/file-upload.dto';
import { FileUpdateDto } from './dto/file-update.dto';
import { FileQueryDto } from './dto/file-query.dto';
import { BaseResponse } from '../../core/entities/base-response';
import { Public } from '../auth/decorators/public.decorator';

@ApiTags('files')
@Controller('files')
@Public()
export class FilesController {
  private readonly logger = new Logger(FilesController.name); // Added logger instance
  constructor(private readonly filesService: FilesService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a single file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        description: {
          type: 'string',
        },
        category: {
          type: 'string',
        },
        tags: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDto: FileUploadDto,
  ): Promise<BaseResponse<any>> {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const result = await this.filesService.uploadFile(file, {
      description: uploadDto.description,
      category: uploadDto.category,
      tags: uploadDto.tags,
    });

    if (!result.success) {
      throw new BadRequestException(result.message || 'Failed to upload file');
    }

    return new BaseResponse(
      HttpStatus.CREATED,
      'File uploaded successfully',
      result.file,
    );
  }

  @Post('upload/multiple')
  @ApiOperation({ summary: 'Upload multiple files' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        description: {
          type: 'string',
        },
        category: {
          type: 'string',
        },
        tags: {
          type: 'array',
          items: {
            type: 'string',
          },
        },
      },
    },
  })
  @UseInterceptors(FilesInterceptor('files', 10)) // Allow up to 10 files
  async uploadFiles(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() uploadDto: FileUploadDto,
  ): Promise<BaseResponse<any>> {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    const results = await this.filesService.uploadFiles(files, {
      description: uploadDto.description,
      category: uploadDto.category,
      tags: uploadDto.tags,
    });

    const successfulUploads = results.filter(result => result.success);
    const failedUploads = results.filter(result => !result.success);

    return new BaseResponse(
      HttpStatus.CREATED,
      `${successfulUploads.length} files uploaded successfully${
        failedUploads.length > 0 ? `, ${failedUploads.length} failed` : ''
      }`,
      {
        successful: successfulUploads.map(result => result.file),
        failed: failedUploads.map(result => ({
          originalName: result.file?.originalName,
          message: result.message,
        })),
      },
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get files with pagination and filtering' })
  async getFiles(@Query() queryDto: FileQueryDto): Promise<BaseResponse<any>> {
    const result = await this.filesService.getFiles({
      page: queryDto.page,
      limit: queryDto.limit,
      category: queryDto.category,
      tags: queryDto.tags,
      uploadedBy: queryDto.uploadedBy,
      sortBy: queryDto.sortBy,
      sortOrder: queryDto.sortOrder,
    });

    return new BaseResponse(
      HttpStatus.OK,
      'Files retrieved successfully',
      result,
    );
  }

  @Get(':fileId')
  @ApiOperation({ summary: 'Get file metadata by ID' })
  @ApiParam({ name: 'fileId', description: 'File ID' })
  async getFileById(@Param('fileId') fileId: string): Promise<BaseResponse<any>> {
    try {
      const file = await this.filesService.getFileById(fileId);
      return new BaseResponse(
        HttpStatus.OK,
        'File retrieved successfully',
        file,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to retrieve file: ${error.message}`);
    }
  }

  @Get(':fileId/download')
  @ApiOperation({ summary: 'Download file by ID' })
  @ApiParam({ name: 'fileId', description: 'File ID' })
  @Public() // Make this endpoint public if needed
  async downloadFile(
      @Param('fileId') fileId: string,
      @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    try {
      const { stream, file } = await this.filesService.createFileReadStream(fileId);

      // Set appropriate headers
      res.set({
        'Content-Type': file.mimeType,
        'Content-Disposition': `attachment; filename="${encodeURIComponent(file.originalName)}"`,
        'Content-Length': file.size.toString(),
      });

      // Now the stream is properly typed as Readable
      return new StreamableFile(stream);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to download file: ${error.message}`);
    }
  }

  @Get('render/:id')
  @Public()
  @ApiOperation({ summary: 'Render file by ID in browser' })
  @ApiParam({ name: 'id', description: 'File ID', type: 'string' })
  @ApiResponse({ status: HttpStatus.OK, description: 'File rendered successfully.' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'File not found.' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Failed to render file.' })
  async renderFile(
      @Param('id') fileId: string,
      @Res() res: Response, // Note: removed passthrough
  ): Promise<void> {
    this.logger.log(`Attempting to render file with ID: ${fileId}`);
    try {
      // Get the file metadata first
      const fileMetadata = await this.filesService.getFileById(fileId);
      this.logger.log(`Found file metadata: ${JSON.stringify(fileMetadata)}`);

      // Get the file buffer directly instead of streaming
      const fileBuffer = await this.filesService.getFileBuffer(fileId);
      this.logger.log(`Successfully retrieved file buffer of size: ${fileBuffer.length} bytes`);

      // Set appropriate headers
      res.set({
        'Content-Type': fileMetadata.mimeType,
        'Content-Disposition': `inline; filename="${encodeURIComponent(fileMetadata.originalName)}"`,
        'Content-Length': fileBuffer.length.toString(),
        // Simple cache headers
        'Cache-Control': 'public, max-age=3600',
        // Ensure no caching issues
        'ETag': `"${fileId}"`,
      });

      this.logger.log(`Sending file ${fileMetadata.originalName} directly as buffer`);

      // Send the buffer directly
      res.send(fileBuffer);

    } catch (error) {
      this.logger.error(`Failed to render file ${fileId}: ${error.message}`, error.stack);

      // Don't send headers if response is already sent
      if (!res.headersSent) {
        if (error instanceof NotFoundException) {
          res.status(HttpStatus.NOT_FOUND).json({
            statusCode: HttpStatus.NOT_FOUND,
            message: error.message,
          });
        } else {
          res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            message: `Error rendering file: ${error.message}`,
          });
        }
      }
    }
  }


  @Put(':fileId')
  @ApiOperation({ summary: 'Update file metadata' })
  @ApiParam({ name: 'fileId', description: 'File ID' })
  async updateFile(
    @Param('fileId') fileId: string,
    @Body() updateDto: FileUpdateDto,
  ): Promise<BaseResponse<any>> {
    try {
      const updatedFile = await this.filesService.updateFile(fileId, updateDto);
      return new BaseResponse(
        HttpStatus.OK,
        'File updated successfully',
        updatedFile,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to update file: ${error.message}`);
    }
  }

  @Delete(':fileId')
  @ApiOperation({ summary: 'Delete file' })
  @ApiParam({ name: 'fileId', description: 'File ID' })
  async deleteFile(@Param('fileId') fileId: string): Promise<BaseResponse<any>> {
    try {
      await this.filesService.deleteFile(fileId);
      return new BaseResponse(
        HttpStatus.OK,
        'File deleted successfully',
        null,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete file: ${error.message}`);
    }
  }
}
