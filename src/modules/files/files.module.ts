import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { FilesController } from './files.controller';
import { FilesService } from './files.service';
import { File, FileSchema } from './schemas/file.schema';
import { ImagesController } from './controllers/images.controller';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: File.name, schema: FileSchema },
    ]),
  ],
  controllers: [FilesController, ImagesController],
  providers: [FilesService],
  exports: [FilesService],
})
export class FilesModule {}