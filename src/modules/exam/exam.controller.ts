import { Controller, Post, Get, Body, Param, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { ExamService } from './exam.service';
import { CreateExamDto } from './dto/create-exam.dto';
import { SubmitExamDto } from './dto/submit-exam.dto';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { Roles } from '../auth/decorators/role.decorator';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RoleGuard } from '../auth/guards/role.guard';
import { EUserRole } from '../user/dto/create-user.dto';

@ApiTags('Exams')
@Controller('exams')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class ExamController {
  constructor(private readonly examService: ExamService) {}

  @Post()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Create a new exam' })
  @ApiResponse({ status: 201, description: 'Exam created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden - You already have an unfinished exam for this worksheet' })
  async createExam(@ActiveUser() user: any, @Body() dto: CreateExamDto) {
    const userId = user.sub;
    const userSchoolId = user.schoolId;
    return this.examService.createExam(userId, dto, userSchoolId);
  }

  @Get(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Get exam by ID' })
  @ApiParam({ name: 'id', description: 'Exam ID' })
  @ApiResponse({ status: 200, description: 'Exam retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Exam not found' })
  async getExam(@ActiveUser() user: any, @Param('id') id: string) {
    const userId = user.sub;
    const role = user.role;
    const userSchoolId = user.schoolId;
    return this.examService.getExamById(userId, id, role, userSchoolId);
  }

  @Post(':id/submit')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER, EUserRole.STUDENT)
  @ApiOperation({ summary: 'Submit exam answers' })
  @ApiParam({ name: 'id', description: 'Exam ID' })
  @ApiResponse({ status: 200, description: 'Exam submitted successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - You have already submitted this exam' })
  @ApiResponse({ status: 404, description: 'Exam not found' })
  async submitExam(
    @ActiveUser() user: any,
    @Param('id') id: string,
    @Body() dto: SubmitExamDto,
  ) {
    const userId = user.sub;
    const userSchoolId = user.schoolId;
    return this.examService.submitExam(userId, id, dto, userSchoolId);
  }

  @Get('by-worksheet/:worksheetId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Get exams by worksheet ID' })
  @ApiParam({ name: 'worksheetId', description: 'Worksheet ID' })
  @ApiResponse({ status: 200, description: 'Exams retrieved successfully' })
  async getExamsByWorksheet(@Param('worksheetId') worksheetId: string, @ActiveUser() user: any) {
    const userSchoolId = user.schoolId;
    return this.examService.getExamsByWorksheetId(worksheetId, userSchoolId);
  }
}
