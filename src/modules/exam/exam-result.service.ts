import { Injectable } from '@nestjs/common';
import { Exam } from './entities/exam.entity';

@Injectable()
export class ExamResultService {
  calculateResult(
    exam: Exam,
    answers: { questionIndex: number; userAnswer: string[] }[],
  ) {
    const detail = exam.questions.map((q, idx) => {
      const userAnsObj = answers.find((a) => a.questionIndex === idx);
      const userAnswer = userAnsObj?.userAnswer || [];
      let isCorrect = false;
      
      if (q.type === 'multiple_choice') {
        const sortedUser = [...userAnswer].sort();
        const sortedAns = [...q.answer].sort();
        isCorrect =
          sortedUser.length === sortedAns.length &&
          sortedUser.every((v, i) => v === sortedAns[i]);
      } else if (q.type === 'single_choice') {
        isCorrect = userAnswer.length === 1 && userAnswer[0] === q.answer[0];
      } else if (q.type === 'fill_blank' || q.type === 'fill_in_blank') {
        // For fill in the blank, compare answers case-insensitively and trim whitespace
        if (userAnswer.length > 0 && q.answer.length > 0) {
          const userAnswerTrimmed = userAnswer[0]?.trim().toLowerCase();
          const correctAnswerTrimmed = q.answer[0]?.trim().toLowerCase();
          isCorrect = userAnswerTrimmed === correctAnswerTrimmed;
        }
      } else if (q.type === 'creative_writing') {
        // For creative writing, we can't automatically grade, so we mark as correct if they provided an answer
        // This could be enhanced with AI grading in the future
        isCorrect = userAnswer.length > 0 && userAnswer[0]?.trim().length > 0;
      }
      
      return {
        questionIndex: idx,
        isCorrect,
        userAnswer,
        answer: q.answer,
        explain: q.explain,
        content: q.content,
        type: q.type,
      };
    });
    const correctCount = detail.filter((d) => d.isCorrect).length;
    return {
      total: exam.questions.length,
      correct: correctCount,
      score: correctCount,
      detail,
    };
  }
}
