import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Exam, ExamStatus } from './entities/exam.entity';
import { ExamQuestion } from './entities/exam-question.entity';
import { CreateExamDto } from './dto/create-exam.dto';
import { SubmitExamDto } from './dto/submit-exam.dto';
import { ExamResultService } from './exam-result.service';
import { ExamResult } from './entities/exam-result.entity';
import { EUserRole } from '../user/dto/create-user.dto';

@Injectable()
export class ExamService {
  private readonly logger = new Logger(ExamService.name);

  constructor(
    @InjectRepository(Exam)
    private readonly examRepository: Repository<Exam>,
    @InjectRepository(ExamResult)
    private readonly examResultRepository: Repository<ExamResult>,
    private readonly examResultService: ExamResultService,
  ) {}

  async createExam(userId: string, dto: CreateExamDto, userSchoolId?: string): Promise<Exam> {
    const existing = await this.examRepository.findOne({
      where: {
        userId,
        worksheetId: dto.worksheetId,
        status: ExamStatus.IN_PROGRESS,
      },
    });
    if (existing) {
      throw new ForbiddenException(
        'You already have an unfinished exam for this worksheet.',
      );
    }

    // Log schoolId for debugging
    if (userSchoolId) {
      this.logger.log(`ExamService: Creating exam with schoolId: ${userSchoolId} from authenticated user`);
    } else {
      this.logger.warn(`ExamService: Creating exam without schoolId - user has no associated school`);
    }

    const exam = this.examRepository.create({
      userId,
      worksheetId: dto.worksheetId,
      title: dto.title,
      description: dto.description,
      selectedOptions: dto.selectedOptions,
      status: ExamStatus.IN_PROGRESS,
      questions: dto.questions.map((q, idx) => ({
        ...q,
        index: idx,
        userAnswer: [],
        isCorrect: null,
      })),
      // Associate exam with user's school
      ...(userSchoolId && { schoolId: userSchoolId }),
    });
    await this.examRepository.save(exam);
    return exam;
  }

  async getExamById(
    userId: string,
    examId: string,
    role: EUserRole,
    userSchoolId?: string,
  ): Promise<any> {
    const exam = await this.examRepository.findOne({
      where: { id: examId },
    });
    if (!exam) throw new NotFoundException('Exam not found');

    // Check if user has access to this exam (same school for non-admin users)
    if (userSchoolId && exam.schoolId && userSchoolId !== exam.schoolId && role !== EUserRole.ADMIN) {
      throw new NotFoundException('Exam not found'); // Don't reveal that it exists but user can't access it
    }

    if (role === EUserRole.TEACHER && exam.userId === userId) {
      const results = await this.examResultRepository.find({
        where: { examId },
      });
      return { ...exam, results };
    } else {
      const examResult = await this.examResultRepository.findOne({
        where: { examId, studentId: userId },
      });
      return { ...exam, userResult: examResult || null };
    }
  }

  async submitExam(
    userId: string,
    examId: string,
    dto: SubmitExamDto,
    userSchoolId?: string,
  ): Promise<any> {
    const exam = await this.examRepository.findOne({
      where: { id: examId },
    });
    if (!exam) throw new NotFoundException('Exam not found');

    // Check if user has access to this exam (same school for non-admin users)
    if (userSchoolId && exam.schoolId && userSchoolId !== exam.schoolId) {
      throw new NotFoundException('Exam not found'); // Don't reveal that it exists but user can't access it
    }

    const existedResult = await this.examResultRepository.findOne({
      where: { examId, studentId: userId },
    });
    if (existedResult)
      throw new BadRequestException('You have already submitted this exam.');
    if (exam.status !== ExamStatus.IN_PROGRESS) {
      throw new BadRequestException('Exam is already completed or invalid.');
    }
    const result = this.examResultService.calculateResult(exam, dto.answers);
    const examResult = this.examResultRepository.create({
      examId,
      studentId: userId,
      answers: dto.answers,
      detail: result.detail,
      score: result.score,
      total: result.total,
    });
    await this.examResultRepository.save(examResult);
    return result;
  }

  async getExamsByWorksheetId(worksheetId: string, userSchoolId?: string): Promise<Exam[]> {
    const where: any = { worksheetId };

    // If user has a schoolId, filter by school
    if (userSchoolId) {
      where.schoolId = userSchoolId;
    }

    return this.examRepository.find({ where });
  }
}
