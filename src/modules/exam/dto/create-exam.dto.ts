import {
  IsUUID,
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  Is<PERSON>rray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class SelectedOptionDto {
  @IsString()
  key: string;

  @IsString()
  value: string;
}

class ExamQuestionDto {
  @IsString()
  type: string;

  @IsString()
  content: string;

  @IsArray()
  options: string[];

  @IsArray()
  answer: string[];

  @IsOptional()
  @IsString()
  image?: string;

  @IsOptional()
  @IsString()
  explain?: string;
}

export class CreateExamDto {
  @IsUUID()
  worksheetId: string;

  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SelectedOptionDto)
  selectedOptions: SelectedOptionDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExamQuestionDto)
  questions: ExamQuestionDto[];
}
