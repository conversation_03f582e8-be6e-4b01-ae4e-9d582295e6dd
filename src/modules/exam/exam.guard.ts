import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Exam, ExamStatus } from './entities/exam.entity';

@Injectable()
export class ExamGuard implements CanActivate {
  constructor(
    @InjectRepository(Exam)
    private readonly examRepository: Repository<Exam>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const userId = request.user?.id || 'mock-user-id';
    const worksheetId = request.body?.worksheetId;
    if (!userId || !worksheetId) {
      throw new ForbiddenException('User or worksheet information is missing.');
    }
    const existing = await this.examRepository.findOne({
      where: {
        userId,
        worksheetId,
        status: ExamStatus.IN_PROGRESS,
      },
    });
    if (existing) {
      throw new ForbiddenException(
        'You already have an unfinished exam for this worksheet.',
      );
    }
    return true;
  }
}
