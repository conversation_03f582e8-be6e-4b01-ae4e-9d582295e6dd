import { Entity, Column } from 'typeorm';
import BaseEntity from 'src/core/entities/base-entity';

@Entity('exam_questions')
export class ExamQuestion extends BaseEntity {
  @Column()
  examId: string;

  @Column()
  index: number;

  @Column({ length: 32 })
  type: string;

  @Column()
  content: string;

  @Column({ type: 'jsonb', nullable: true })
  options?: any;

  @Column({ type: 'jsonb', nullable: true })
  answer?: any;

  @Column({ type: 'jsonb', nullable: true })
  userAnswer?: any;

  @Column({ nullable: true })
  isCorrect?: boolean;

  @Column({ nullable: true })
  image?: string;

  @Column({ nullable: true })
  explain?: string;
}
