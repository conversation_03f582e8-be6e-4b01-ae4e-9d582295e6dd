import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionService } from '../permission.service';
import { REQUIRED_FEATURES_KEY } from '../decorators/requires-feature.decorator';
import { REQUIRED_ACCESS_KEY } from '../decorators/requires-access.decorator';
import { UserContext } from '../../auth/services/rbac.service';

@Injectable()
export class PermissionGuard implements CanActivate {
  private readonly logger = new Logger(PermissionGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly permissionService: PermissionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;
    const endpoint = `${request.method} ${request.route?.path || request.url}`;

    // User must be authenticated (should be handled by AuthGuard first)
    if (!user) {
      this.logger.warn(`Permission check failed: No user context for ${endpoint}`);
      return false;
    }

    // Get required features and access permissions from decorators
    const requiredFeatures = this.reflector.get<string[]>(
      REQUIRED_FEATURES_KEY,
      context.getHandler(),
    );
    
    const requiredAccess = this.reflector.get<string[]>(
      REQUIRED_ACCESS_KEY,
      context.getHandler(),
    );

    // If no permission requirements, allow access
    if ((!requiredFeatures || requiredFeatures.length === 0) && 
        (!requiredAccess || requiredAccess.length === 0)) {
      return true;
    }

    try {
      // Check required features
      if (requiredFeatures && requiredFeatures.length > 0) {
        for (const feature of requiredFeatures) {
          const hasFeature = await this.permissionService.hasFeature(user.sub, feature);
          if (!hasFeature) {
            this.logger.warn(
              `Permission denied: User ${user.sub} lacks required feature '${feature}' for ${endpoint}`
            );
            return false;
          }
        }
      }

      // Check required access permissions
      if (requiredAccess && requiredAccess.length > 0) {
        for (const accessKey of requiredAccess) {
          const hasAccess = await this.permissionService.hasAccess(user.sub, accessKey);
          if (!hasAccess) {
            this.logger.warn(
              `Permission denied: User ${user.sub} lacks required access '${accessKey}' for ${endpoint}`
            );
            return false;
          }
        }
      }

      this.logger.debug(
        `Permission granted: User ${user.sub} has all required permissions for ${endpoint}`
      );
      return true;

    } catch (error) {
      this.logger.error(
        `Permission check error for user ${user.sub} on ${endpoint}: ${error.message}`
      );
      // Deny access on error to be safe
      return false;
    }
  }
}
