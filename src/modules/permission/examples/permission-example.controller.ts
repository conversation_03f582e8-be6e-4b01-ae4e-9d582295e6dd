import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { PermissionGuard } from '../guards/permission.guard';
import { RequiresFeature } from '../decorators/requires-feature.decorator';
import { RequiresAccess } from '../decorators/requires-access.decorator';
import { PermissionService } from '../permission.service';
import { ActiveUser, ActiveUserData } from '../../auth/decorators/active-user.decorator';

/**
 * Example controller demonstrating how to use the PermissionService and guards
 * This controller shows different patterns for checking subscription-based permissions
 */
@ApiTags('Permission Examples')
@ApiBearerAuth()
@Controller('permission-examples')
@UseGuards(AuthGuard, PermissionGuard) // Apply both auth and permission guards
export class PermissionExampleController {
  constructor(private readonly permissionService: PermissionService) {}

  /**
   * Example endpoint that requires a specific feature
   * Only users with 'exam_creation' feature can access this
   */
  @Get('create-exam')
  @RequiresFeature('exam_creation')
  @ApiOperation({ summary: 'Create exam (requires exam_creation feature)' })
  @ApiResponse({ status: 200, description: 'Exam creation allowed' })
  @ApiResponse({ status: 403, description: 'Feature not available in subscription' })
  async createExam() {
    return { message: 'Exam creation is available for your subscription' };
  }

  /**
   * Example endpoint that requires multiple features
   * User must have both features to access
   */
  @Get('advanced-worksheet')
  @RequiresFeature('basic_worksheets', 'student_management')
  @ApiOperation({ summary: 'Advanced worksheet management (requires multiple features)' })
  @ApiResponse({ status: 200, description: 'Advanced worksheet features available' })
  @ApiResponse({ status: 403, description: 'Required features not available' })
  async advancedWorksheet() {
    return { message: 'Advanced worksheet management is available' };
  }

  /**
   * Example endpoint that requires access permissions
   * Only users with admin panel access can use this
   */
  @Get('admin-dashboard')
  @RequiresAccess('adminPanel')
  @ApiOperation({ summary: 'Admin dashboard (requires admin panel access)' })
  @ApiResponse({ status: 200, description: 'Admin dashboard accessible' })
  @ApiResponse({ status: 403, description: 'Admin panel access not available' })
  async adminDashboard() {
    return { message: 'Admin dashboard is accessible' };
  }

  /**
   * Example endpoint that combines feature and access requirements
   */
  @Get('advanced-analytics')
  @RequiresFeature('student_management')
  @RequiresAccess('analytics', 'advancedReports')
  @ApiOperation({ summary: 'Advanced analytics (requires feature and access)' })
  @ApiResponse({ status: 200, description: 'Advanced analytics available' })
  @ApiResponse({ status: 403, description: 'Required permissions not available' })
  async advancedAnalytics() {
    return { message: 'Advanced analytics are available' };
  }

  /**
   * Example endpoint that manually checks permissions in the controller
   * This shows how to use PermissionService directly for complex logic
   */
  @Get('my-permissions')
  @ApiOperation({ summary: 'Get current user permissions' })
  @ApiResponse({ status: 200, description: 'User permissions retrieved' })
  async getMyPermissions(@ActiveUser() user: ActiveUserData) {
    // Get all user permissions
    const permissions = await this.permissionService.getUserPermissions(user.sub);
    
    // Check specific limits
    const studentLimit = await this.permissionService.getLimit(user.sub, 'maxStudents');
    const worksheetLimit = await this.permissionService.getLimit(user.sub, 'maxWorksheets');
    
    // Check if user has active subscription
    const hasActiveSubscription = await this.permissionService.hasActiveSubscription(user.sub);

    return {
      permissions,
      limits: {
        maxStudents: studentLimit,
        maxWorksheets: worksheetLimit,
      },
      hasActiveSubscription,
    };
  }

  /**
   * Example endpoint that checks usage against limits
   * This demonstrates how to validate current usage against subscription limits
   */
  @Get('check-student-limit')
  @ApiOperation({ summary: 'Check if user can add more students' })
  @ApiResponse({ status: 200, description: 'Limit check result' })
  async checkStudentLimit(@ActiveUser() user: ActiveUserData) {
    // In a real scenario, you would get current student count from database
    const currentStudentCount = 45; // Example current usage
    
    const canAddMore = await this.permissionService.checkLimit(
      user.sub, 
      'maxStudents', 
      currentStudentCount
    );
    
    const limit = await this.permissionService.getLimit(user.sub, 'maxStudents');
    
    return {
      canAddMore,
      currentCount: currentStudentCount,
      limit,
      remaining: limit ? limit - currentStudentCount : 0,
    };
  }

  /**
   * Example endpoint with no permission requirements
   * All authenticated users can access this
   */
  @Get('basic-info')
  @ApiOperation({ summary: 'Basic info (no special permissions required)' })
  @ApiResponse({ status: 200, description: 'Basic information' })
  async basicInfo() {
    return { message: 'This endpoint is available to all authenticated users' };
  }
}
