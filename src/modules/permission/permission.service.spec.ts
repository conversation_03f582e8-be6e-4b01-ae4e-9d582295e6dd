import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { PermissionService } from './permission.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { DEFAULT_PERMISSIONS } from './interfaces/permission.interface';

describe('PermissionService', () => {
  let service: PermissionService;
  let subscriptionService: jest.Mocked<SubscriptionService>;

  const mockSubscriptionWithPackage = {
    id: 'sub-uuid',
    user_id: 'user-123',
    status: 'active',
    package: {
      id: 'pkg-uuid',
      name: 'Premium Package',
      description: 'Premium subscription package',
      permissions: {
        features: ['basic_worksheets', 'exam_creation', 'student_management'],
        limits: {
          maxStudents: 100,
          maxWorksheets: 50,
          maxExams: 25,
        },
        access: {
          adminPanel: true,
          analytics: true,
          advancedReports: false,
        },
      },
    },
  };

  beforeEach(async () => {
    const mockSubscriptionService = {
      findByUserId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PermissionService,
        {
          provide: SubscriptionService,
          useValue: mockSubscriptionService,
        },
      ],
    }).compile();

    service = module.get<PermissionService>(PermissionService);
    subscriptionService = module.get(SubscriptionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getUserPermissions', () => {
    it('should return user permissions when subscription exists', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.getUserPermissions('user-123');

      expect(subscriptionService.findByUserId).toHaveBeenCalledWith('user-123');
      expect(result).toEqual({
        features: ['basic_worksheets', 'exam_creation', 'student_management'],
        limits: {
          maxStudents: 100,
          maxWorksheets: 50,
          maxExams: 25,
        },
        access: {
          adminPanel: true,
          analytics: true,
          advancedReports: false,
        },
        packageName: 'Premium Package',
        subscriptionStatus: 'active',
        hasActiveSubscription: true,
      });
    });

    it('should return default permissions when no subscription found', async () => {
      subscriptionService.findByUserId.mockRejectedValue(new NotFoundException('No subscription found'));

      const result = await service.getUserPermissions('user-123');

      expect(result).toEqual(DEFAULT_PERMISSIONS);
    });

    it('should return default permissions when package has no permissions', async () => {
      const subscriptionWithoutPermissions = {
        ...mockSubscriptionWithPackage,
        package: {
          ...mockSubscriptionWithPackage.package,
          permissions: null,
        },
      };
      subscriptionService.findByUserId.mockResolvedValue(subscriptionWithoutPermissions as any);

      const result = await service.getUserPermissions('user-123');

      expect(result).toEqual(DEFAULT_PERMISSIONS);
    });
  });

  describe('hasFeature', () => {
    it('should return true when user has the feature', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.hasFeature('user-123', 'exam_creation');

      expect(result).toBe(true);
    });

    it('should return false when user does not have the feature', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.hasFeature('user-123', 'advanced_analytics');

      expect(result).toBe(false);
    });
  });

  describe('getLimit', () => {
    it('should return limit value when it exists', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.getLimit('user-123', 'maxStudents');

      expect(result).toBe(100);
    });

    it('should return null when limit does not exist', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.getLimit('user-123', 'nonExistentLimit');

      expect(result).toBeNull();
    });
  });

  describe('hasAccess', () => {
    it('should return true when user has access', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.hasAccess('user-123', 'adminPanel');

      expect(result).toBe(true);
    });

    it('should return false when user does not have access', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.hasAccess('user-123', 'advancedReports');

      expect(result).toBe(false);
    });
  });

  describe('checkLimit', () => {
    it('should return true when under limit', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.checkLimit('user-123', 'maxStudents', 50);

      expect(result).toBe(true);
    });

    it('should return false when at or over limit', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.checkLimit('user-123', 'maxStudents', 100);

      expect(result).toBe(false);
    });

    it('should return false when limit does not exist', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.checkLimit('user-123', 'nonExistentLimit', 10);

      expect(result).toBe(false);
    });
  });

  describe('hasActiveSubscription', () => {
    it('should return true when subscription is active', async () => {
      subscriptionService.findByUserId.mockResolvedValue(mockSubscriptionWithPackage as any);

      const result = await service.hasActiveSubscription('user-123');

      expect(result).toBe(true);
    });

    it('should return false when no subscription', async () => {
      subscriptionService.findByUserId.mockRejectedValue(new NotFoundException('No subscription found'));

      const result = await service.hasActiveSubscription('user-123');

      expect(result).toBe(false);
    });
  });
});
