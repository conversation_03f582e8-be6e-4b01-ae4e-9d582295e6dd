import { <PERSON>tity, Column, ManyToOne } from 'typeorm';
import BaseEntity from '../../../core/entities/base-entity';
import { OptionType } from './option-type.entity';

@Entity('option_values')
export class OptionValue extends BaseEntity {
  @Column()
  label: string;

  @Column()
  value: string;

  @Column({ default: 0 })
  order: number;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => OptionType, (type) => type.values, {
    onDelete: 'CASCADE',
  })
  optionType: OptionType;

  @Column()
  optionTypeId: string;
}
