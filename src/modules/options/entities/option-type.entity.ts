import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>To<PERSON>any } from 'typeorm';
import BaseEntity from '../../../core/entities/base-entity';
import { OptionValue } from './option-value.entity';

@Entity('option_types')
export class OptionType extends BaseEntity {
  @Column()
  key: string;

  @Column()
  label: string;

  @Column()
  description: string;

  @OneToMany(() => OptionValue, (value) => value.optionType)
  values: OptionValue[];
}
