import { <PERSON>tity, Column, ManyToOne, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import BaseEntity from '../../../core/entities/base-entity';
import { OptionValue } from './option-value.entity';

export enum SubjectType {
  PARENT = 'parent',
  CHILD = 'child',
}

@Entity('subjects')
export class Subject extends BaseEntity {
  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: SubjectType,
    default: SubjectType.CHILD,
  })
  type: SubjectType;

  @ManyToOne(() => Subject, (subject) => subject.children, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  parent: Subject;

  @Column({ nullable: true })
  parentId: string;

  @OneToMany(() => Subject, (subject) => subject.parent)
  children: Subject[];

  @ManyToMany(() => OptionValue)
  @JoinTable({
    name: 'subject_topics',
    joinColumn: { name: 'subject_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'topic_id', referencedColumnName: 'id' },
  })
  topics: OptionValue[];

  @ManyToMany(() => OptionValue)
  @JoinTable({
    name: 'subject_grades',
    joinColumn: { name: 'subject_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'grade_id', referencedColumnName: 'id' },
  })
  grades: OptionValue[];
}