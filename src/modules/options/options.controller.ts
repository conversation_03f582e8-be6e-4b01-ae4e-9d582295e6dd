import { Controller, Get, Post, Body, Param, Put, Delete, Query, HttpCode, HttpStatus, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiQuery } from '@nestjs/swagger';
import { OptionsService } from './options.service';
import { OptionType } from './entities/option-type.entity';
import { OptionValue } from './entities/option-value.entity';
import { Subject, SubjectType } from './entities/subject.entity';
import { CreateOptionTypeDto } from './dto/create-option-type.dto';
import { CreateOptionValueDto } from './dto/create-option-value.dto';
import { CreateSubjectDto } from './dto/create-subject.dto';
import { QuerySubjectsByTopicDto } from './dto/query-subjects-by-topic.dto';
import { QuerySubjectHierarchyDto } from './dto/query-subject-hierarchy.dto';
import { SubjectHierarchyItemDto } from './dto/subject-hierarchy.dto';
import { Public } from '../auth/decorators/public.decorator';
@ApiTags('Options')
@Controller('options')
@Public()
export class OptionsController {
  constructor(private readonly optionsService: OptionsService) {}

  @Post('types')
  @ApiOperation({ summary: 'Create a new option type' })
  @ApiBody({ type: CreateOptionTypeDto })
  @ApiResponse({ status: 201, description: 'Option type created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createOptionType(
    @Body() createOptionTypeDto: CreateOptionTypeDto,
  ): Promise<OptionType> {
    return this.optionsService.createOptionType(createOptionTypeDto);
  }

  @Post('values')
  @ApiOperation({ summary: 'Create a new option value' })
  @ApiBody({ type: CreateOptionValueDto })
  @ApiResponse({ status: 201, description: 'Option value created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createOptionValue(
    @Body() createOptionValueDto: CreateOptionValueDto,
  ): Promise<OptionValue> {
    return this.optionsService.createOptionValue(createOptionValueDto);
  }

  @Get('types')
  @ApiOperation({ summary: 'Get all option types' })
  @ApiResponse({ status: 200, description: 'Option types retrieved successfully' })
  findAllOptionTypes(): Promise<OptionType[]> {
    return this.optionsService.findAllOptionTypes();
  }

  @Get('types/by-key/:key')
  @ApiOperation({ summary: 'Get option type by key' })
  @ApiParam({ name: 'key', description: 'Option type key' })
  @ApiResponse({ status: 200, description: 'Option type retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Option type not found' })
  findOptionTypeByKey(@Param('key') key: string): Promise<OptionType> {
    return this.optionsService.findOptionTypeByKey(key);
  }

  @Get('types/:id')
  @ApiOperation({ summary: 'Get option type by ID' })
  @ApiParam({ name: 'id', description: 'Option type ID' })
  @ApiResponse({ status: 200, description: 'Option type retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Option type not found' })
  findOptionTypeById(@Param('id') id: string): Promise<OptionType> {
    return this.optionsService.findOptionTypeById(id);
  }

  @Get('values/by-type/:typeId')
  @ApiOperation({ summary: 'Get option values by type ID' })
  @ApiParam({ name: 'typeId', description: 'Option type ID' })
  @ApiResponse({ status: 200, description: 'Option values retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Option type not found' })
  findOptionValuesByTypeId(
    @Param('typeId') typeId: string,
  ): Promise<OptionValue[]> {
    return this.optionsService.findOptionValuesByTypeId(typeId);
  }

  // Subject endpoints

  @Post('subjects')
  @ApiOperation({ summary: 'Create a new subject' })
  @ApiBody({ type: CreateSubjectDto })
  @ApiResponse({ status: 201, description: 'Subject created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  createSubject(@Body() createSubjectDto: CreateSubjectDto): Promise<Subject> {
    return this.optionsService.createSubject(createSubjectDto);
  }

  @Get('subjects')
  @ApiOperation({ summary: 'Get all subjects' })
  @ApiResponse({ status: 200, description: 'Subjects retrieved successfully' })
  findAllSubjects(): Promise<Subject[]> {
    return this.optionsService.findAllSubjects();
  }

  @Get('subjects/hierarchy')
  @ApiOperation({ summary: 'Get complete subject hierarchy' })
  @ApiQuery({ name: 'topicId', required: false, description: 'Filter by topic ID' })
  @ApiQuery({ name: 'gradeId', required: false, description: 'Filter by grade ID' })
  @ApiResponse({ status: 200, description: 'Subject hierarchy retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Topic not found' })
  getSubjectHierarchy(@Query() query: QuerySubjectHierarchyDto): Promise<SubjectHierarchyItemDto[]> {
    return this.optionsService.buildSubjectHierarchy(query.topicId, query.gradeId);
  }

  @Get('subjects/by-parent/:parentId')
  @ApiOperation({ summary: 'Get subjects by parent ID' })
  @ApiParam({ name: 'parentId', description: 'Parent subject ID' })
  @ApiResponse({ status: 200, description: 'Subjects retrieved successfully' })
  findSubjectsByParentId(@Param('parentId') parentId: string): Promise<Subject[]> {
    return this.optionsService.findSubjectsByParentId(parentId);
  }

  @Get('subjects/root')
  @ApiOperation({ summary: 'Get root subjects (without parent)' })
  @ApiResponse({ status: 200, description: 'Root subjects retrieved successfully' })
  findRootSubjects(): Promise<Subject[]> {
    return this.optionsService.findRootSubjects();
  }

  @Get('subjects/by-topic/:topicId')
  @ApiOperation({ summary: 'Get subjects by topic ID' })
  @ApiParam({ name: 'topicId', description: 'Topic ID' })
  @ApiQuery({ name: 'type', enum: SubjectType, required: false, description: 'Filter by subject type' })
  @ApiResponse({ status: 200, description: 'Subjects retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Topic not found' })
  findSubjectsByTopicId(
    @Param('topicId') topicId: string,
    @Query() query: QuerySubjectsByTopicDto,
  ): Promise<Subject[]> {
    return this.optionsService.findSubjectsByTopicId(topicId, query.type);
  }

  @Get('subjects/:id')
  @ApiOperation({ summary: 'Get subject by ID' })
  @ApiParam({ name: 'id', description: 'Subject ID' })
  @ApiResponse({ status: 200, description: 'Subject retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Subject not found' })
  findSubjectById(@Param('id') id: string): Promise<Subject> {
    return this.optionsService.findSubjectById(id);
  }

  @Put('subjects/:id')
  @ApiOperation({ summary: 'Update a subject' })
  @ApiParam({ name: 'id', description: 'Subject ID' })
  @ApiBody({ type: CreateSubjectDto })
  @ApiResponse({ status: 200, description: 'Subject updated successfully' })
  @ApiResponse({ status: 404, description: 'Subject not found' })
  updateSubject(
    @Param('id') id: string,
    @Body() updateSubjectDto: CreateSubjectDto,
  ): Promise<Subject> {
    return this.optionsService.updateSubject(id, updateSubjectDto);
  }

  @Delete('subjects/:id')
  @ApiOperation({ summary: 'Delete a subject' })
  @ApiParam({ name: 'id', description: 'Subject ID' })
  @ApiResponse({ status: 200, description: 'Subject deleted successfully' })
  @ApiResponse({ status: 404, description: 'Subject not found' })
  deleteSubject(@Param('id') id: string): Promise<void> {
    return this.optionsService.deleteSubject(id);
  }

  @Delete('values/:id')
  @ApiOperation({ summary: 'Delete an option value' })
  @ApiParam({ name: 'id', description: 'Option value ID' })
  @ApiResponse({ status: 204, description: 'Option value deleted successfully' })
  @ApiResponse({ status: 404, description: 'Option value not found' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteOptionValue(@Param('id', new ParseUUIDPipe()) id: string): Promise<void> {
    await this.optionsService.deleteOptionValue(id);
    return;
  }

  @Post('seed-initial-data')
  @ApiOperation({ summary: 'Seed initial option data' })
  @ApiResponse({ status: 201, description: 'Initial data seeded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  seedInitialData() {
    return this.optionsService.seedInitialData();
  }

  @Post('seed-math-subjects')
  @ApiOperation({ summary: 'Seed math subjects data for P5 and P6' })
  @ApiResponse({ status: 201, description: 'Math subjects seeded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  seedMathSubjects() {
    return this.optionsService.seedMathSubjects();
  }

  @Post('seed-koobits-p5-math')
  @ApiOperation({ summary: 'Seed P5 math subjects from KooBits curriculum' })
  @ApiResponse({ status: 201, description: 'KooBits P5 math subjects seeded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  seedKooBitsP5Math() {
    return this.optionsService.seedKooBitsP5Math();
  }

  @Post('seed-p5-math-subjects-detailed')
  @ApiOperation({ summary: 'Seed detailed P5 math subjects with all sub-topics' })
  @ApiResponse({ status: 201, description: 'Detailed P5 math subjects seeded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  seedP5MathSubjectsDetailed() {
    return this.optionsService.seedP5MathSubjectsDetailed();
  }
}
