import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { OptionType } from './entities/option-type.entity';
import { OptionValue } from './entities/option-value.entity';
import { Subject, SubjectType } from './entities/subject.entity';
import { CreateOptionTypeDto } from './dto/create-option-type.dto';
import { CreateOptionValueDto } from './dto/create-option-value.dto';
import { CreateSubjectDto } from './dto/create-subject.dto';
import { SubjectHierarchyItemDto } from './dto/subject-hierarchy.dto';

@Injectable()
export class OptionsService {
  constructor(
    @InjectRepository(OptionType)
    private optionTypeRepository: Repository<OptionType>,
    @InjectRepository(OptionValue)
    private optionValueRepository: Repository<OptionValue>,
    @InjectRepository(Subject)
    private subjectRepository: Repository<Subject>,
  ) {}

  async createOptionType(
    createOptionTypeDto: CreateOptionTypeDto,
  ): Promise<OptionType> {
    const optionType = this.optionTypeRepository.create(createOptionTypeDto);
    return await this.optionTypeRepository.save(optionType);
  }

  async createOptionValue(
    createOptionValueDto: CreateOptionValueDto,
  ): Promise<OptionValue> {
    const optionType = await this.optionTypeRepository.findOne({
      where: { id: createOptionValueDto.optionTypeId },
    });

    if (!optionType) {
      throw new NotFoundException('Option type not found');
    }

    const optionValue = this.optionValueRepository.create(createOptionValueDto);
    return await this.optionValueRepository.save(optionValue);
  }

  async findAllOptionTypes(): Promise<OptionType[]> {
    return await this.optionTypeRepository.find({
      relations: ['values'],
    });
  }

  async findOptionTypeById(id: string): Promise<OptionType> {
    const optionType = await this.optionTypeRepository.findOne({
      where: { id },
      relations: ['values'],
    });

    if (!optionType) {
      throw new NotFoundException('Option type not found');
    }

    return optionType;
  }

  async findOptionTypeByKey(key: string): Promise<OptionType> {
    const optionType = await this.optionTypeRepository.findOne({
      where: { key },
      relations: ['values'],
    });

    if (!optionType) {
      throw new NotFoundException('Option type not found');
    }

    return optionType;
  }

  async findOptionValuesByTypeId(typeId: string): Promise<OptionValue[]> {
    return await this.optionValueRepository.find({
      where: { optionTypeId: typeId },
      order: {
        order: 'ASC',
      },
    });
  }

  async seedInitialData() {
    const SEED_OPTIONS = [
      {
        key: 'grade',
        label: 'Grade',
        description: 'What grade are you teaching?',
        values: ['P1', 'P2', 'P3', 'P4', 'P5', 'P6'],
      },
      {
        key: 'topic',
        label: 'Subject',
        description: 'What subject do you teach?',
        values: ['Mathematics', 'Science', 'Art', 'English', 'Chinese'],
      },
      {
        key: 'level',
        label: 'Level',
        description: 'What level exam do you want to create?',
        values: ['Easy', 'Medium', 'Advanced'],
      },
      {
        key: 'language',
        label: 'Language',
        description: 'What language do you want to practice in?',
        values: ['English', 'Chinese'],
      },
      {
        key: 'question_count',
        label: 'Question Count',
        description: 'How many questions do you want to practice with?',
        values: ['10', '20', '50', '100'],
      },
      {
        key: 'question_type',
        label: 'Question Type',
        description: 'What type of questions do you want to practice?',
        values: [
          'Fill Blank',
          'Single Choice',
          'Multiple Choices',
          'Creative Writing',
        ],
      },
    ];

    for (const option of SEED_OPTIONS) {
      // Skip if already seeded
      const existing = await this.optionTypeRepository.findOne({
        where: { key: option.key },
      });
      if (existing) continue;

      // Create OptionType
      const type = this.optionTypeRepository.create({
        key: option.key,
        label: option.label,
        description: option.description,
      });
      const savedType = await this.optionTypeRepository.save(type);

      // Create OptionValues
      const values = option.values.map((label, index) =>
        this.optionValueRepository.create({
          label,
          value: label.toLowerCase().replace(/ /g, '_'),
          order: index,
          optionTypeId: savedType.id,
        }),
      );

      await this.optionValueRepository.save(values);
    }

    return { success: true, message: 'Seed completed successfully' };
  }

  // Subject methods

  async createSubject(createSubjectDto: CreateSubjectDto): Promise<Subject> {
    const { topicIds, gradeIds, ...subjectData } = createSubjectDto;

    // Create the subject
    const subject = this.subjectRepository.create(subjectData);

    // Save the subject first to get an ID
    const savedSubject = await this.subjectRepository.save(subject);

    // If topicIds are provided, associate topics
    if (topicIds && topicIds.length > 0) {
      const topics = await this.optionValueRepository.find({
        where: { id: In(topicIds) },
      });

      if (topics.length > 0) {
        savedSubject.topics = topics;
      }
    }

    // If gradeIds are provided, associate grades
    if (gradeIds && gradeIds.length > 0) {
      const grades = await this.optionValueRepository.find({
        where: { id: In(gradeIds) },
      });

      if (grades.length > 0) {
        savedSubject.grades = grades;
      }
    }

    // Save again with relationships
    return await this.subjectRepository.save(savedSubject);
  }

  async findAllSubjects(): Promise<Subject[]> {
    return await this.subjectRepository.find({
      relations: ['parent', 'children', 'topics', 'grades'],
    });
  }

  async findSubjectById(id: string): Promise<Subject> {
    const subject = await this.subjectRepository.findOne({
      where: { id },
      relations: ['parent', 'children', 'topics', 'grades'],
    });

    if (!subject) {
      throw new NotFoundException('Subject not found');
    }

    return subject;
  }

  async findSubjectsByParentId(parentId: string): Promise<Subject[]> {
    return await this.subjectRepository.find({
      where: { parentId },
      relations: ['topics', 'grades'],
    });
  }

  async findRootSubjects(): Promise<Subject[]> {
    return await this.subjectRepository.find({
      where: { parentId: IsNull() },
      relations: ['children', 'topics', 'grades'],
    });
  }

  async updateSubject(id: string, updateSubjectDto: CreateSubjectDto): Promise<Subject> {
    const { topicIds, gradeIds, ...subjectData } = updateSubjectDto;

    // Find the subject
    const subject = await this.findSubjectById(id);

    // Update basic fields
    Object.assign(subject, subjectData);

    // If topicIds are provided, update topics
    if (topicIds) {
      const topics = await this.optionValueRepository.find({
        where: { id: In(topicIds) },
      });
      subject.topics = topics;
    }

    // If gradeIds are provided, update grades
    if (gradeIds) {
      const grades = await this.optionValueRepository.find({
        where: { id: In(gradeIds) },
      });
      subject.grades = grades;
    }

    // Save the updated subject
    return await this.subjectRepository.save(subject);
  }

  async deleteSubject(id: string): Promise<void> {
    const subject = await this.findSubjectById(id);
    await this.subjectRepository.softRemove(subject);
  }

  /**
   * Delete an option value by ID
   * @param id The ID of the option value to delete
   * @throws NotFoundException if the option value is not found
   */
  async deleteOptionValue(id: string): Promise<void> {
    const optionValue = await this.optionValueRepository.findOne({
      where: { id }
    });

    if (!optionValue) {
      throw new NotFoundException(`Option value with ID ${id} not found`);
    }

    await this.optionValueRepository.softRemove(optionValue);
  }

  /**
   * Find parent subjects by topic ID
   * @param topicId The ID of the topic to filter by
   * @param type Optional subject type to filter by (parent or child)
   * @returns Array of subjects that belong to the specified topic
   */
  async findSubjectsByTopicId(topicId: string, type?: SubjectType): Promise<Subject[]> {
    // Verify the topic exists
    const topic = await this.optionValueRepository.findOne({
      where: { id: topicId },
    });

    if (!topic) {
      throw new NotFoundException(`Topic with ID ${topicId} not found`);
    }

    // Build the query
    const query = this.subjectRepository
      .createQueryBuilder('subject')
      .innerJoinAndSelect('subject.topics', 'topic')
      .leftJoinAndSelect('subject.grades', 'grade')
      .where('topic.id = :topicId', { topicId });

    // Add type filter if provided
    if (type) {
      query.andWhere('subject.type = :type', { type });
    }

    return await query.getMany();
  }

  /**
   * Build a complete subject hierarchy
   * @param topicId Optional topic ID to filter by
   * @param gradeId Optional grade ID to filter by
   * @returns Hierarchical structure of topics, parent subjects, and child subjects
   */
  async buildSubjectHierarchy(topicId?: string, gradeId?: string): Promise<SubjectHierarchyItemDto[]> {
    // Get all topics (or a specific topic if topicId is provided)
    const topicType = await this.optionTypeRepository.findOne({
      where: { key: 'topic' },
    });

    if (!topicType) {
      throw new NotFoundException('Topic option type not found');
    }

    let topicsQuery = this.optionValueRepository
      .createQueryBuilder('topic')
      .where('topic.optionTypeId = :typeId', { typeId: topicType.id });

    if (topicId) {
      topicsQuery = topicsQuery.andWhere('topic.id = :topicId', { topicId });
    }

    const topics = await topicsQuery.getMany();

    if (topicId && topics.length === 0) {
      throw new NotFoundException(`Topic with ID ${topicId} not found`);
    }

    // Build the hierarchy
    const result: SubjectHierarchyItemDto[] = [];

    for (const topic of topics) {
      // Get parent subjects for this topic
      const parentSubjectsQuery = this.subjectRepository
        .createQueryBuilder('subject')
        .innerJoinAndSelect('subject.topics', 'topic')
        .leftJoinAndSelect('subject.grades', 'grade')
        .where('topic.id = :topicId', { topicId: topic.id })
        .andWhere('subject.type = :type', { type: SubjectType.PARENT });

      // Add grade filter if provided
      if (gradeId) {
        parentSubjectsQuery
          .innerJoin('subject.grades', 'filterGrade')
          .andWhere('filterGrade.id = :gradeId', { gradeId });
      }

      const parentSubjects = await parentSubjectsQuery.getMany();

      // Create topic item
      const topicItem: SubjectHierarchyItemDto = {
        id: topic.id,
        name: topic.label,
        type: 'topic',
        children: [],
      };

      // Add parent subjects to topic
      for (const parentSubject of parentSubjects) {
        // Get child subjects for this parent
        const childSubjectsQuery = this.subjectRepository
          .createQueryBuilder('subject')
          .leftJoinAndSelect('subject.topics', 'topic')
          .leftJoinAndSelect('subject.grades', 'grade')
          .where('subject.parentId = :parentId', { parentId: parentSubject.id });

        // Add grade filter if provided
        if (gradeId) {
          childSubjectsQuery
            .innerJoin('subject.grades', 'filterGrade')
            .andWhere('filterGrade.id = :gradeId', { gradeId });
        }

        const childSubjects = await childSubjectsQuery.getMany();

        // Create parent subject item
        const parentItem: SubjectHierarchyItemDto = {
          id: parentSubject.id,
          name: parentSubject.name,
          type: 'parent',
          description: parentSubject.description,
          children: [], // Initialize as empty array
        };

        // Add child subjects to parent
        for (const childSubject of childSubjects) {
          const childItem: SubjectHierarchyItemDto = {
            id: childSubject.id,
            name: childSubject.name,
            type: 'lesson',
            description: childSubject.description,
            children: [],
          };

          // Ensure children array exists before pushing
          if (!parentItem.children) {
            parentItem.children = [];
          }
          parentItem.children.push(childItem);
        }

        // Ensure children array exists before pushing
        if (!topicItem.children) {
          topicItem.children = [];
        }
        topicItem.children.push(parentItem);
      }

      result.push(topicItem);
    }

    return result;
  }

  async seedMathSubjects() {
    try {
      // Get the Mathematics topic and grade option values
      const topicType = await this.optionTypeRepository.findOne({
        where: { key: 'topic' },
      });

      if (!topicType) {
        throw new Error('Topic option type not found');
      }

      const mathTopic = await this.optionValueRepository.findOne({
        where: {
          optionTypeId: topicType.id,
          value: 'mathematics'
        },
      });

      if (!mathTopic) {
        throw new Error('Mathematics topic not found');
      }

      const gradeType = await this.optionTypeRepository.findOne({
        where: { key: 'grade' },
      });

      if (!gradeType) {
        throw new Error('Grade option type not found');
      }

      const p5Grade = await this.optionValueRepository.findOne({
        where: {
          optionTypeId: gradeType.id,
          value: 'p5'
        },
      });

      const p6Grade = await this.optionValueRepository.findOne({
        where: {
          optionTypeId: gradeType.id,
          value: 'p6'
        },
      });

      if (!p5Grade || !p6Grade) {
        throw new Error('P5 or P6 grade not found');
      }

      // Define parent subjects (main categories) for P5 Math
      const p5ParentSubjects = [
        {
          name: 'Whole Numbers',
          description: 'Operations with whole numbers up to 10 million',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Fractions',
          description: 'Operations with fractions including division and multiplication',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Area of Triangle',
          description: 'Finding area of triangles and composite figures',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Volume',
          description: 'Volume of cube and cuboid',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Ratio',
          description: 'Understanding and solving problems with ratios',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Decimals',
          description: 'Operations with decimals and conversions',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Rate',
          description: 'Understanding and calculating rates',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Percentage',
          description: 'Converting and calculating with percentages',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Average',
          description: 'Finding and using averages in data sets',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Angles',
          description: 'Understanding and calculating angles',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Properties of Triangles',
          description: 'Understanding different types of triangles and their properties',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Parallelogram, Rhombus and Trapezium',
          description: 'Properties of quadrilaterals',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        }
      ];

      // Define parent subjects for P6 Math
      const p6ParentSubjects = [
        {
          name: 'Algebra',
          description: 'Interpreting and solving algebraic expressions and equations',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Fractions',
          description: 'Division of fractions and solving word problems',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Ratio',
          description: 'Solving ratio problems with 3 quantities and changing ratios',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Percentage',
          description: 'Finding percentage increase/decrease and solving word problems',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Speed',
          description: 'Understanding speed in different units and solving word problems',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Circles',
          description: 'Finding area and circumference of circles and composite figures',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Volume',
          description: 'Finding dimensions of cuboids given volume and solving word problems',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Angles',
          description: 'Finding unknown angles in composite figures',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: '2D/3D Shapes',
          description: 'Visualizing and drawing 2D representations of 3D solids',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Data Analysis',
          description: 'Solving word problems using information from pie charts',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        }
      ];

      // Save all parent subjects
      const savedP5Parents: Subject[] = [];
      for (const parentSubject of p5ParentSubjects) {
        // Check if already exists
        const existing = await this.subjectRepository.findOne({
          where: {
            name: parentSubject.name,
            type: SubjectType.PARENT
          },
          relations: ['topics', 'grades']
        });

        if (existing) {
          savedP5Parents.push(existing);
          continue;
        }

        const saved = await this.createSubject(parentSubject);
        savedP5Parents.push(saved);
      }

      const savedP6Parents: Subject[] = [];
      for (const parentSubject of p6ParentSubjects) {
        // Check if already exists
        const existing = await this.subjectRepository.findOne({
          where: {
            name: parentSubject.name,
            type: SubjectType.PARENT
          },
          relations: ['topics', 'grades']
        });

        if (existing) {
          savedP6Parents.push(existing);
          continue;
        }

        const saved = await this.createSubject(parentSubject);
        savedP6Parents.push(saved);
      }

      // Define child subjects for P5 Math
      const p5ChildSubjects = [
        // Whole Numbers
        {
          name: 'Place Values up to 10 Million',
          description: 'Understanding place values in large numbers',
          parentId: savedP5Parents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Rounding Numbers',
          description: 'Rounding numbers to estimate calculations',
          parentId: savedP5Parents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiplication and Division by 10, 100, 1000',
          description: 'Multiplying and dividing by powers of 10',
          parentId: savedP5Parents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Order of Operations',
          description: 'Using PEMDAS to solve expressions',
          parentId: savedP5Parents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // Fractions
        {
          name: 'Addition and Subtraction of Unlike Fractions',
          description: 'Adding and subtracting fractions with different denominators',
          parentId: savedP5Parents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiplication of Fractions',
          description: 'Multiplying proper and improper fractions',
          parentId: savedP5Parents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Addition and Subtraction of Mixed Numbers',
          description: 'Operations with mixed numbers',
          parentId: savedP5Parents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // Area of Triangle
        {
          name: 'Finding Base and Height',
          description: 'Identifying the base and corresponding height of a triangle',
          parentId: savedP5Parents.find(p => p.name === 'Area of Triangle')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Area of Composite Figures',
          description: 'Finding area involving triangles and rectangles',
          parentId: savedP5Parents.find(p => p.name === 'Area of Triangle')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // Volume
        {
          name: 'Volume of Cube',
          description: 'Calculating volume of a cube',
          parentId: savedP5Parents.find(p => p.name === 'Volume')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Volume of Cuboid',
          description: 'Calculating volume of a cuboid',
          parentId: savedP5Parents.find(p => p.name === 'Volume')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Volume of Liquid',
          description: 'Measuring volume of liquid in cubic units',
          parentId: savedP5Parents.find(p => p.name === 'Volume')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        }
      ];

      // Define child subjects for P6 Math
      const p6ChildSubjects = [
        // Algebra
        {
          name: 'Interpreting Algebraic Expressions',
          description: 'Understanding what algebraic expressions represent',
          parentId: savedP6Parents.find(p => p.name === 'Algebra')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Solving Simple Equations',
          description: 'Finding the value of unknown variables',
          parentId: savedP6Parents.find(p => p.name === 'Algebra')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Substitution in Expressions',
          description: 'Evaluating expressions by substituting values',
          parentId: savedP6Parents.find(p => p.name === 'Algebra')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },

        // Fractions
        {
          name: 'Dividing a Whole Number by a Proper Fraction',
          description: 'Understanding and calculating division with fractions',
          parentId: savedP6Parents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Dividing a Proper Fraction by a Proper Fraction',
          description: 'Division between fractions',
          parentId: savedP6Parents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Fraction Word Problems',
          description: 'Solving real-world problems involving fractions',
          parentId: savedP6Parents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },

        // Circles
        {
          name: 'Area of Circle',
          description: 'Calculating the area of a circle using the formula',
          parentId: savedP6Parents.find(p => p.name === 'Circles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Circumference of Circle',
          description: 'Finding the perimeter of a circle',
          parentId: savedP6Parents.find(p => p.name === 'Circles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Semi-circles and Quarter Circles',
          description: 'Finding area and perimeter of parts of circles',
          parentId: savedP6Parents.find(p => p.name === 'Circles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },

        // Speed
        {
          name: 'Speed Conversion',
          description: 'Converting speed between different units',
          parentId: savedP6Parents.find(p => p.name === 'Speed')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Time, Distance and Speed Relationship',
          description: 'Understanding the relationship between time, distance and speed',
          parentId: savedP6Parents.find(p => p.name === 'Speed')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        },
        {
          name: 'Speed Word Problems',
          description: 'Solving complex word problems involving speed',
          parentId: savedP6Parents.find(p => p.name === 'Speed')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p6Grade.id]
        }
      ];

      // Save all child subjects
      for (const childSubject of p5ChildSubjects) {
        // Check if already exists
        const existing = await this.subjectRepository.findOne({
          where: {
            name: childSubject.name,
            parentId: childSubject.parentId
          }
        });

        if (existing) continue;

        await this.createSubject(childSubject);
      }

      for (const childSubject of p6ChildSubjects) {
        // Check if already exists
        const existing = await this.subjectRepository.findOne({
          where: {
            name: childSubject.name,
            parentId: childSubject.parentId
          }
        });

        if (existing) continue;

        await this.createSubject(childSubject);
      }

      return { success: true, message: 'Math subjects seeded successfully' };
    } catch (error) {
      console.error('Error seeding math subjects:', error);
      throw error;
    }
  }
  async seedKooBitsP5Math() {
    // This is a stub method to satisfy the controller reference
    return { success: true, message: 'KooBits P5 math subjects seeding not implemented yet' };
  }

  async seedP5MathSubjectsDetailed() {
    try {
      // Get the Mathematics topic and P5 grade option values
      const topicType = await this.optionTypeRepository.findOne({
        where: { key: 'topic' },
      });

      if (!topicType) {
        throw new Error('Topic option type not found');
      }

      const mathTopic = await this.optionValueRepository.findOne({
        where: {
          optionTypeId: topicType.id,
          value: 'mathematics'
        },
      });

      if (!mathTopic) {
        throw new Error('Mathematics topic not found');
      }

      const gradeType = await this.optionTypeRepository.findOne({
        where: { key: 'grade' },
      });

      if (!gradeType) {
        throw new Error('Grade option type not found');
      }

      const p5Grade = await this.optionValueRepository.findOne({
        where: {
          optionTypeId: gradeType.id,
          value: 'p5'
        },
      });

      if (!p5Grade) {
        throw new Error('P5 grade not found');
      }

      // Define parent subjects (main categories) for P5 Math
      const parentSubjects = [
        {
          name: 'Whole Numbers',
          description: 'Operations with whole numbers up to 10 million',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Four Operations of Whole Numbers',
          description: 'Rounding, estimation, and operations with whole numbers',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Fractions',
          description: 'Division of whole numbers as fractions and operations with fractions',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Four Operations of Fractions',
          description: 'Addition, subtraction, multiplication, and division of fractions',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Area of Triangle',
          description: 'Finding area of triangles and composite figures',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Volume of cube and cuboid',
          description: 'Measuring and calculating volume of 3D shapes',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Ratio',
          description: 'Understanding and solving problems with ratios',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Decimals',
          description: 'Operations with decimals and unit conversions',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Rate',
          description: 'Understanding and calculating rates in various contexts',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Percentage',
          description: 'Converting and calculating with percentages',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Average',
          description: 'Finding and using averages in data sets',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Angles',
          description: 'Understanding and calculating angles in various contexts',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Properties of Triangles',
          description: 'Understanding different types of triangles and their properties',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Parallelogram, Rhombus and Trapezium',
          description: 'Properties and angles of quadrilaterals',
          type: SubjectType.PARENT,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        }
      ];

      // Save all parent subjects
      const savedParents: Subject[] = [];
      for (const parentSubject of parentSubjects) {
        // Check if already exists
        const existing = await this.subjectRepository.findOne({
          where: {
            name: parentSubject.name,
            type: SubjectType.PARENT
          },
          relations: ['topics', 'grades']
        });

        if (existing) {
          savedParents.push(existing);
          continue;
        }

        const saved = await this.createSubject(parentSubject);
        savedParents.push(saved);
      }

      // Define child subjects for each parent
      const childSubjects = [
        // 1. Whole Numbers
        {
          name: 'Count by ten thousands',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Count by hundred thousands',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Write numbers to 1,000,000 in numerals',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Write numbers to 1,000,000 in words',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Count by millions to 10,000,000',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Write numbers to 10,000,000 in numerals',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Write numbers to 10,000,000 in words',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find the value of each digit using a place value chart',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find the expanded form of a 6-digit number',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Identify the place value and value of any digit',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Write numbers to 10 million in expanded form',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Compare numbers to 1,000,000 using greater than and less than',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Compare numbers up to 10,000,000',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Complete number patterns involving numbers to 10,000,000',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find the tens/hundreds/thousands to be added to a number to obtain a given value',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find the difference between two numbers rounded to the nearest tens/hundreds/thousands',
          parentId: savedParents.find(p => p.name === 'Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 2. Four Operations of Whole Numbers
        {
          name: 'Rounding numbers to the nearest hundred or thousand',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Rounding numbers to estimate sums',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Rounding numbers to estimate differences',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Rounding numbers to estimate products',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Estimate quotients using compatible numbers',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply by 10, 100, 1000',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply by tens, hundreds, thousands',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Divide by 10, 100, 1000',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Divide by tens, hundreds, thousands',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Rules for order of operations on numbers (with and without brackets)',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Calculate, involving only addition and subtraction',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Calculate, involving only multiplication and division',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Calculate, involving addition, subtraction, multiplication and division',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Calculate, involving brackets',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Calculate and apply the order of operations',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Estimate the answer and check reasonableness of the calculated answer',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve multi-step problems involving whole numbers',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve word problems using systematic lists',
          parentId: savedParents.find(p => p.name === 'Four Operations of Whole Numbers')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 3. Fractions
        {
          name: 'Division of Whole Numbers as Fractions',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'How Fraction and Division are Related',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Concepts of Fraction and Division (Part-Whole Model)',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express division as fractions',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express division as mixed numbers',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express fractions as decimals',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express division as decimals',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express mixed numbers as decimals',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Add unlike fractions',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Subtract unlike fractions',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply a proper or improper fraction by a whole number',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve word problems involving multiplication of fractions',
          parentId: savedParents.find(p => p.name === 'Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 4. Four Operations of Fractions
        {
          name: 'Add mixed numbers without regrouping',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Add mixed numbers with regrouping',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Subtract mixed numbers without regrouping',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Subtract mixed numbers with regrouping',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express division as fractions and mixed numbers',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply a proper fraction and a whole number',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply an improper fraction and a whole number',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply proper fractions',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply improper fractions by proper fractions',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply an improper fraction by an improper fraction',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply mixed numbers by whole numbers',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express the product of a mixed number and a whole number as a decimal',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Fraction of a Remainder',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve word problems involving multiplying with proper fractions',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve two-step problems involving multiplying with mixed numbers',
          parentId: savedParents.find(p => p.name === 'Four Operations of Fractions')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 5. Area of Triangle
        {
          name: 'Identify the base and corresponding height of a triangle',
          parentId: savedParents.find(p => p.name === 'Area of Triangle')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Calculate the area of a triangle given a base and corresponding height',
          parentId: savedParents.find(p => p.name === 'Area of Triangle')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find area of a triangle',
          parentId: savedParents.find(p => p.name === 'Area of Triangle')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find area involving triangles and rectangles',
          parentId: savedParents.find(p => p.name === 'Area of Triangle')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find area involving triangles',
          parentId: savedParents.find(p => p.name === 'Area of Triangle')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 6. Volume of cube and cuboid
        {
          name: 'Measure volume in cubic units',
          parentId: savedParents.find(p => p.name === 'Volume of cube and cuboid')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Measure volume in cubic centimetres and cubic metres',
          parentId: savedParents.find(p => p.name === 'Volume of cube and cuboid')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find volume of a cuboid by computing the product of its length, breadth and height',
          parentId: savedParents.find(p => p.name === 'Volume of cube and cuboid')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find one dimension of a cuboid knowing its volume and other dimensions',
          parentId: savedParents.find(p => p.name === 'Volume of cube and cuboid')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Volume of liquid in cubic centimetres and cubic metres',
          parentId: savedParents.find(p => p.name === 'Volume of cube and cuboid')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find volume of liquid in a rectangular tank',
          parentId: savedParents.find(p => p.name === 'Volume of cube and cuboid')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 7. Ratio
        {
          name: 'Find ratio (idea of ratio)',
          parentId: savedParents.find(p => p.name === 'Ratio')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find the equivalent ratios',
          parentId: savedParents.find(p => p.name === 'Ratio')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express a ratio in its simplest form',
          parentId: savedParents.find(p => p.name === 'Ratio')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find one quantity given the other quantity and their ratio',
          parentId: savedParents.find(p => p.name === 'Ratio')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find a quantity given the total amount and the ratio',
          parentId: savedParents.find(p => p.name === 'Ratio')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve up to 2-step word problems involving ratio',
          parentId: savedParents.find(p => p.name === 'Ratio')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Ratio of three quantities – Involving common factors',
          parentId: savedParents.find(p => p.name === 'Ratio')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve word problems involving ratio of three quantities',
          parentId: savedParents.find(p => p.name === 'Ratio')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 8. Decimals
        {
          name: 'Multiply by 10, 100, 1000',
          parentId: savedParents.find(p => p.name === 'Decimals')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Multiply decimals by tens, hundreds, thousands',
          parentId: savedParents.find(p => p.name === 'Decimals')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Divide by 10, 100, 1000',
          parentId: savedParents.find(p => p.name === 'Decimals')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Divide decimals by tens, hundreds, thousands',
          parentId: savedParents.find(p => p.name === 'Decimals')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Convert between kilograms and grams',
          parentId: savedParents.find(p => p.name === 'Decimals')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Convert between metres and centimetres',
          parentId: savedParents.find(p => p.name === 'Decimals')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Convert between kilometres and metres',
          parentId: savedParents.find(p => p.name === 'Decimals')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Convert between litres and millilitres',
          parentId: savedParents.find(p => p.name === 'Decimals')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 9. Rate
        {
          name: 'Finding Rates involving Time',
          parentId: savedParents.find(p => p.name === 'Rate')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Finding Rates involving Money',
          parentId: savedParents.find(p => p.name === 'Rate')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Finding Total Amount involving Time',
          parentId: savedParents.find(p => p.name === 'Rate')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Finding Total Amount involving Money',
          parentId: savedParents.find(p => p.name === 'Rate')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Finding Number of Units',
          parentId: savedParents.find(p => p.name === 'Rate')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 10. Percentage
        {
          name: 'Express a part of a whole as a percentage',
          parentId: savedParents.find(p => p.name === 'Percentage')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Finding a percentage part of a whole',
          parentId: savedParents.find(p => p.name === 'Percentage')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express fractions and decimals as percentages',
          parentId: savedParents.find(p => p.name === 'Percentage')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Express percentages as fractions and decimals',
          parentId: savedParents.find(p => p.name === 'Percentage')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Finding a percentage of a quantity',
          parentId: savedParents.find(p => p.name === 'Percentage')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve problems involving discount',
          parentId: savedParents.find(p => p.name === 'Percentage')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve problems involving GST',
          parentId: savedParents.find(p => p.name === 'Percentage')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve problems involving annual interest',
          parentId: savedParents.find(p => p.name === 'Percentage')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 11. Average
        {
          name: 'Find an average in a set of data',
          parentId: savedParents.find(p => p.name === 'Average')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find total value given average and number of data',
          parentId: savedParents.find(p => p.name === 'Average')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find number of data given average and total value',
          parentId: savedParents.find(p => p.name === 'Average')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Solve up to 3-step word problems involving average',
          parentId: savedParents.find(p => p.name === 'Average')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 12. Angles
        {
          name: 'Find vertically opposite angles',
          parentId: savedParents.find(p => p.name === 'Angles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle on a straight line',
          parentId: savedParents.find(p => p.name === 'Angles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle at a point',
          parentId: savedParents.find(p => p.name === 'Angles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle at a point, involving right angles',
          parentId: savedParents.find(p => p.name === 'Angles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 13. Properties of Triangles
        {
          name: 'Properties of isosceles triangle',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Properties of equilateral triangle',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Properties of right-angled triangle',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Properties of acute-angled triangle',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Properties of obtuse-angled triangle',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Identify different types of triangles',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Angle sum of a triangle',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find unknown angles using properties of triangles',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle in a triangle',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle involving an isosceles triangle',
          parentId: savedParents.find(p => p.name === 'Properties of Triangles')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },

        // 14. Parallelogram, Rhombus and Trapezium
        {
          name: 'Properties of parallelogram',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Properties of rhombus',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Properties of trapezium',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Identify and name parallelogram, rhombus and trapezium',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find unknown angles using properties of parallelogram, rhombus and trapezium',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle involving an isosceles triangle & a parallelogram',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle involving an isosceles triangle inside a parallelogram',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle involving a parallelogram',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle involving a square & a rhombus',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle involving a triangle & a parallelogram',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        },
        {
          name: 'Find an angle involving a trapezium',
          parentId: savedParents.find(p => p.name === 'Parallelogram, Rhombus and Trapezium')?.id,
          topicIds: [mathTopic.id],
          gradeIds: [p5Grade.id]
        }
      ];

      // Save all child subjects
      for (const childSubject of childSubjects) {
        // Check if already exists
        const existing = await this.subjectRepository.findOne({
          where: {
            name: childSubject.name,
            parentId: childSubject.parentId
          }
        });

        if (existing) continue;

        await this.createSubject(childSubject);
      }

      return { success: true, message: 'P5 Math subjects seeded successfully' };
    } catch (error) {
      console.error('Error seeding P5 Math subjects:', error);
      throw error;
    }
  }
}