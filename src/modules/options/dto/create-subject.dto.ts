import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { SubjectType } from '../entities/subject.entity';

export class CreateSubjectDto {
  @ApiProperty({
    description: 'Name of the subject',
    example: 'Mathematics',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Description of the subject',
    example: 'Mathematics for primary school students',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Type of subject (parent or child)',
    enum: SubjectType,
    default: SubjectType.CHILD,
    required: false,
  })
  @IsEnum(SubjectType)
  @IsOptional()
  type?: SubjectType;

  @ApiProperty({
    description: 'ID of the parent subject',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  parentId?: string;

  @ApiProperty({
    description: 'IDs of associated topics',
    example: ['123e4567-e89b-12d3-a456-************'],
    required: false,
    isArray: true,
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsOptional()
  topicIds?: string[];

  @ApiProperty({
    description: 'IDs of associated grades',
    example: ['123e4567-e89b-12d3-a456-************'],
    required: false,
    isArray: true,
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @IsOptional()
  gradeIds?: string[];
}