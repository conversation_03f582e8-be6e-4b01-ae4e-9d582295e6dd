import { ApiProperty } from '@nestjs/swagger';

export class SubjectHierarchyItemDto {
  @ApiProperty({
    description: 'ID of the subject',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the subject',
    example: 'Mathematics',
  })
  name: string;

  @ApiProperty({
    description: 'Type of the subject (topic, parent, or lesson)',
    example: 'parent',
    enum: ['topic', 'parent', 'lesson'],
  })
  type: string;

  @ApiProperty({
    description: 'Description of the subject',
    example: 'Mathematics for primary school students',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Children subjects',
    type: [SubjectHierarchyItemDto],
    default: [],
  })
  children: SubjectHierarchyItemDto[];
}
