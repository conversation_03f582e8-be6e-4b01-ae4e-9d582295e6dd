import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOptionTypeDto {
  @ApiProperty({
    description: 'Unique key for the option type',
    example: 'difficulty_level',
  })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({
    description: 'Display label for the option type',
    example: 'Difficulty Level',
  })
  @IsString()
  @IsNotEmpty()
  label: string;

  @ApiProperty({
    description: 'Description of the option type',
    example: 'Difficulty levels for exercises',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'Display order for the option type (optional)',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  order?: number;
}
