import { IsOptional, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class QuerySubjectHierarchyDto {
  @ApiProperty({
    description: 'Topic ID to filter by',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  topicId?: string;

  @ApiProperty({
    description: 'Grade ID to filter by',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  gradeId?: string;
}
