import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpStatus,
  Post,
  Query,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiConsumes, ApiBody } from '@nestjs/swagger';
import {FilesInterceptor} from '@nestjs/platform-express';
import {DocumentsService} from './documents.service';
import {ProcessDocumentResult,} from './interfaces/document.interface';
import {v4 as uuidv4} from 'uuid';
import {BaseResponse} from '../../core/entities/base-response';
import {Public} from '../auth/decorators/public.decorator';

@ApiTags('Documents')
@Controller('documents')
@Public()
export class DocumentsController {
  constructor(private documentsService: DocumentsService) {}

  @Get('/')
  @ApiOperation({ summary: 'Query documents based on search term and optional category' })
  @ApiQuery({ name: 'query', required: true, description: 'Search term to query documents' })
  @ApiQuery({ name: 'category', required: false, description: 'Category to filter documents (optional)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Documents retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Documents retrieved successfully' },
        data: { type: 'string', example: 'Document content...' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async queryDocuments(
    @Query('query') query: string,
    @Query('category') category?: string, // Category is optional - if not provided, all categories will be queried
  ): Promise<BaseResponse<string>> {
    if (!query || query.trim() === '') {
      throw new BadRequestException('Query parameter is required');
    }

    console.log(
      `Executing query: "${query}" with category: ${category || 'all'}`,
    );

    try {
      // Pass undefined as category if not provided to query all categories
      const data = await this.documentsService.queryDocuments(
        query,
        5,
        60000,
        category,
      );

      return new BaseResponse(
        HttpStatus.OK,
        'Documents retrieved successfully',
        data,
      );
    } catch (error) {
      console.error('Error querying documents:', error);
      throw new BadRequestException(
        `Failed to query documents: ${error.message}`,
      );
    }
  }

  @Post('upload')
  @ApiOperation({ summary: 'Upload documents for processing' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        title: {
          type: 'string',
          description: 'Title of the document(s)',
        },
        description: {
          type: 'string',
          description: 'Description of the document(s)',
        },
        category: {
          type: 'string',
          description: 'Category of the document(s)',
        },
      },
    },
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Documents uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Documents uploaded successfully' },
        data: { 
          type: 'object',
          properties: {
            count: { type: 'number', example: 2 },
            results: { 
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  documentId: { type: 'string' },
                  title: { type: 'string' },
                  description: { type: 'string' },
                  fileName: { type: 'string' },
                  mimeType: { type: 'string' },
                  uploadedAt: { type: 'string', format: 'date-time' },
                  category: { type: 'string' }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @UseInterceptors(FilesInterceptor('files', 10)) // Allow up to 10 files
  async uploadDocuments(
    @UploadedFiles() files: Express.Multer.File[],
    @Body() body: { title?: string; description?: string; category?: string },
  ): Promise<
    BaseResponse<{ count: number; results: ProcessDocumentResult[] }>
  > {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files uploaded');
    }

    const results: ProcessDocumentResult[] = [];

    for (const file of files) {
      const documentId = uuidv4();
      const metadata = {
        documentId,
        title: body.title || file.originalname,
        description: body.description || '',
        fileName: file.originalname,
        mimeType: file.mimetype,
        uploadedAt: new Date().toISOString(),
        category: body.category || 'document', // Default category if none provided
      };

      const result = await this.documentsService.processDocument(
        file,
        metadata,
      );
      results.push(result);
    }

    const data = {
      count: files.length,
      results: results,
    };

    return new BaseResponse(
      HttpStatus.OK,
      'Documents uploaded successfully',
      data,
    );
  }
}
