import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsString } from 'class-validator';

/**
 * DTO for batch query operations
 */
export class PatchQueryDto {
  /**
   * Array of query strings to process
   */
  @IsArray()
  @IsString({ each: true })
  queries: string[];

  /**
   * Maximum number of similar chunks to retrieve per query
   * @default 5
   */
  @IsOptional()
  @IsNumber()
  similarityTopK?: number;

  /**
   * Timeout in milliseconds for the entire batch
   * @default 60000 (60 seconds)
   */
  @IsOptional()
  @IsNumber()
  timeoutMs?: number;
}
