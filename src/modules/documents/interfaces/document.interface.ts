import { VectorStoreIndex } from 'llamaindex';

/**
 * Metadata for a document
 */
export interface DocumentMetadata {
  /** Unique identifier for the document */
  documentId: string;
  /** Category of the document (e.g., 'document', 'example') */
  category?: string;
  /** Additional metadata properties */
  [key: string]: unknown;
}

/**
 * Options for processing a document
 */
export interface ProcessDocumentOptions {
  /** Size of each chunk in characters */
  chunkSize?: number;
  /** Number of characters to overlap between chunks */
  chunkOverlap?: number;
  /** Force reprocessing even if document is in cache */
  forceReprocess?: boolean;
}

/**
 * Statistics about the document cache
 */
export interface CacheStats {
  /** Number of cache hits */
  hits: number;
  /** Number of cache misses */
  misses: number;
  /** When the cache was last cleared */
  lastCleared: Date;
  /** Current size of the cache */
  size: number;
  /** List of document IDs in the cache */
  documentIds: string[];
}

export interface TextNode {
  /** Unique identifier for the text node */
  id: string;
  /** Text content of the node */
  text: string;
  /** Metadata associated with the node */
  metadata?: DocumentMetadata;
  /** Image URL if the node contains an image */
  imageUrl?: string;
  /** Exercise format data if the node contains an exercise */
  exerciseFormat?: {
    type: string;
    content: string;
    options?: string[];
    answer?: string[];
  };
  /** Additional properties for the node */
  [key: string]: unknown;
}

/**
 * A source node from a query result
 */
export interface SourceNode {
  node: TextNode;
  score: number;
}

/**
 * Result of a document query
 */
export interface QueryResult {
  /** Response text from the query */
  response: string;
  /** Source nodes that contributed to the response */
  sourceNodes?: SourceNode[];
  /** Additional metadata about the query result */
  metadata?: Record<string, unknown>;
}

/**
 * Error from a batch query
 */
export interface BatchQueryError {
  /** Error message */
  error: string;
}

/**
 * Result of a node query with additional information
 */
export interface NodeResult {
  /** Text content of the node */
  text: string;
  /** Relevance score of the node */
  score: number;
  /** Image information if available */
  image?: {
    /** URL to the image */
    url: string;
    /** Prompt used to generate the image */
    prompt: string;
  };
  /** Exercise format information if available */
  exerciseFormat?: {
    /** Type of exercise */
    type: string;
    /** Content of the exercise */
    content: string;
    /** Options for the exercise */
    options?: string[];
    /** Answer for the exercise */
    answer?: string[];
  };
  /** Error message if there was an error processing the query */
  error?: string;
}

/**
 * Result of processing a document
 */
export interface ProcessDocumentResult {
  /** Unique identifier for the document */
  documentId: string;
  /** Vector index for the document */
  vector: VectorStoreIndex;
  /** Whether the document was retrieved from cache */
  fromCache: boolean;
}
