import {forwardRef, Logger, Module} from '@nestjs/common';
import { DocumentsService } from './documents.service';
import { DocumentsController } from './documents.controller';
import { EmbeddingModule } from '../embedding/embedding.module';
import { VectorDBModule } from '../vector-db/vector-db.module';
import { AiModule } from '../ai/ai.module';
import {BuildPromptModule} from "../build-prompt/build-prompt.module";
import {MultimodalModule} from "../multimodal/multimodal.module";
import {QueryCacheModule} from "../mongodb/query-cache.module";

@Module({
  imports: [
    VectorDBModule.register({ type: 'pinecone' }),
    AiModule,
    forwardRef(() => EmbeddingModule),
    forwardRef(() => BuildPromptModule),
    forwardRef(() => MultimodalModule),
    QueryCacheModule
  ],
  controllers: [DocumentsController],
  providers: [DocumentsService],
  exports: [DocumentsService],
})
export class DocumentsModule {}
