import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AiServiceLogService } from './ai-service-log.service';
import { AiServiceLog } from '../mongodb/schemas/ai-service-log.schema';
import { Public } from '../auth/decorators/public.decorator';

@ApiTags('AI Service Logs')
@Controller('ai-service-logs')
@Public()
export class AiServiceLogController {
  constructor(private readonly aiServiceLogService: AiServiceLogService) {}

  @Get()
  @ApiOperation({ summary: 'Get all AI service logs with optional filtering' })
  @ApiQuery({ name: 'status', required: false, enum: ['success', 'error'] })
  @ApiQuery({ name: 'worksheetId', required: false })
  @ApiQuery({ name: 'startDate', required: false, type: Date })
  @ApiQuery({ name: 'endDate', required: false, type: Date })
  @ApiResponse({
    status: 200,
    description: 'Returns all AI service logs matching the filter criteria',
    type: [AiServiceLog],
  })
  async getAllLogs(
    @Query('status') status?: 'success' | 'error',
    @Query('worksheetId') worksheetId?: string,
    @Query('startDate') startDate?: Date,
    @Query('endDate') endDate?: Date,
  ): Promise<AiServiceLog[]> {
    return this.aiServiceLogService.getLogs({
      status,
      worksheetId,
      startDate,
      endDate,
    });
  }

  @Get('worksheet/:worksheetId')
  @ApiOperation({ summary: 'Get AI service logs by worksheet ID' })
  @ApiParam({ name: 'worksheetId', required: true })
  @ApiResponse({
    status: 200,
    description: 'Returns AI service logs for the specified worksheet',
    type: [AiServiceLog],
  })
  async getLogsByWorksheetId(
    @Param('worksheetId') worksheetId: string,
  ): Promise<AiServiceLog[]> {
    return this.aiServiceLogService.getLogsByWorksheetId(worksheetId);
  }

  @Get('batch/:batchId')
  @ApiOperation({ summary: 'Get AI service log by batch ID' })
  @ApiParam({ name: 'batchId', required: true })
  @ApiResponse({
    status: 200,
    description: 'Returns the AI service log for the specified batch or null if not found',
    type: AiServiceLog,
  })
  async getLogByBatchId(
    @Param('batchId') batchId: string,
  ): Promise<AiServiceLog | null> {
    return this.aiServiceLogService.getLogByBatchId(batchId);
  }
}