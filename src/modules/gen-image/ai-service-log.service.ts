import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AiServiceLog } from '../mongodb/schemas/ai-service-log.schema';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AiServiceLogService {
  private readonly logger = new Logger(AiServiceLogService.name);

  constructor(
    @InjectModel(AiServiceLog.name)
    private aiServiceLogModel: Model<AiServiceLog>,
  ) {}

  /**
   * Logs AI service request and response details
   * @param params Log parameters
   * @returns The created log entry
   */
  async logAiServiceRequest({
    worksheetId,
    status,
    errorMessage,
    errorResponse,
    prompts,
    systemPrompt,
  }: {
    worksheetId?: string;
    status: 'success' | 'error';
    errorMessage?: string;
    errorResponse?: any;
    prompts: string[];
    systemPrompt: string;
  }): Promise<AiServiceLog | null> {
    try {
      const batchId = uuidv4();

      const logEntry = new this.aiServiceLogModel({
        batchId,
        worksheetId,
        status,
        errorMessage,
        errorResponse,
        prompts,
        systemPrompt,
      });

      const savedLog = await logEntry.save();
      this.logger.log(`AI service log created with batchId: ${batchId}`);

      return savedLog;
    } catch (error) {
      this.logger.error(`Error creating AI service log: ${error.message}`, error.stack);
      // We don't want to throw here as logging should not interrupt the main flow
      return null;
    }
  }

  /**
   * Get logs by worksheetId
   * @param worksheetId The worksheet ID to filter by
   * @returns Array of log entries
   */
  async getLogsByWorksheetId(worksheetId: string): Promise<AiServiceLog[]> {
    try {
      return this.aiServiceLogModel.find({ worksheetId }).sort({ createdAt: -1 }).exec();
    } catch (error) {
      this.logger.error(`Error retrieving logs for worksheetId ${worksheetId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all logs with optional filtering
   * @param filter Optional filter criteria
   * @returns Array of log entries
   */
  async getLogs(filter: {
    status?: 'success' | 'error';
    worksheetId?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}): Promise<AiServiceLog[]> {
    try {
      const query: any = {};

      if (filter.status) {
        query.status = filter.status;
      }

      if (filter.worksheetId) {
        query.worksheetId = filter.worksheetId;
      }

      if (filter.startDate || filter.endDate) {
        query.createdAt = {};
        if (filter.startDate) {
          query.createdAt.$gte = filter.startDate;
        }
        if (filter.endDate) {
          query.createdAt.$lte = filter.endDate;
        }
      }

      return this.aiServiceLogModel.find(query).sort({ createdAt: -1 }).exec();
    } catch (error) {
      this.logger.error(`Error retrieving logs with filter: ${JSON.stringify(filter)}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a log by batchId
   * @param batchId The batch ID to find
   * @returns The log entry or null if not found
   */
  async getLogByBatchId(batchId: string): Promise<AiServiceLog | null> {
    try {
      return this.aiServiceLogModel.findOne({ batchId }).exec();
    } catch (error) {
      this.logger.error(`Error retrieving log for batchId ${batchId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}