import {forwardRef, Inject, Injectable, Logger} from '@nestjs/common';
import {AiService} from "../ai/ai.service";
import {ModelConfigService} from "../ai/model-config.service";
import {SocketGateway} from "../socket/socket.gateway";
import {AiServiceLogService} from "./ai-service-log.service";
import OpenAI from "openai";

/**
 * Interface for batch processing statistics
 */
interface BatchStats {
    totalBatches: number;
    successfulBatches: number;
    failedBatches: number;
    fallbacksTriggered: number;
}


@Injectable()
export class GenImageService {
    private readonly logger = new Logger(GenImageService.name);

    constructor(
        private modelConfigService: ModelConfigService,
        @Inject( forwardRef(() => AiService) )
        private aiService: AiService,
        private socketGateway: SocketGateway,
        private aiServiceLogService: AiServiceLogService,
    ) {}

    /**
     * Compresses a prompt by removing redundant words and whitespace
     * while preserving essential diagram specifications and measurements
     * @param prompt The prompt to compress
     * @returns The compressed prompt
     */
    private compressPrompt(prompt: string): string {
        if (!prompt) return prompt;

        try {
            // Remove extra whitespace
            let compressed = prompt.replace(/\s+/g, ' ').trim();

            // Remove redundant phrases and words while preserving essential specifications
            const redundantPhrases = [
                'please create', 'please generate', 'please draw', 'please make',
                'create a', 'generate a', 'draw a', 'make a',
                'the image should', 'the diagram should', 'the illustration should',
                'make sure to', 'ensure that', 'it is important to',
                'the following', 'as follows', 'described below',
            ];

            // Using forEach instead of for loop to iterate over redundant phrases
            redundantPhrases.forEach(phrase => {
                compressed = compressed.replace(new RegExp(phrase, 'gi'), '');
            });

            // Preserve measurements and specifications by not compressing numbers with units
            // This regex avoids compressing patterns like "10px", "5cm", "2.5 inches", etc.
            compressed = compressed.replace(/(\d+(\.\d+)?)\s*(px|cm|mm|in|inch|inches|%|em|rem|pt)/gi, '$1$3');

            // Trim again after all replacements
            compressed = compressed.trim();

            // If compression reduced the size significantly, log it
            const compressionRatio = compressed.length / prompt.length;
            if (compressionRatio < 0.8) {
                this.logger.log(`Prompt compressed by ${Math.round((1 - compressionRatio) * 100)}% (${prompt.length} → ${compressed.length} chars)`);
            }

            return compressed;
        } catch (error) {
            this.logger.warn(`Error compressing prompt: ${error.message}. Using original prompt.`);
            return prompt;
        }
    }

    /**
     * Compresses an array of prompts
     * @param prompts Array of prompts to compress
     * @returns Array of compressed prompts
     */
    private compressBatchPrompts(prompts: string[]): string[] {
        if (!prompts || prompts.length === 0) return prompts;

        return prompts.map(prompt => this.compressPrompt(prompt));
    }

    /**
     * Creates decompression instructions for the system prompt
     * @returns Decompression instructions
     */
    private getDecompressionInstructions(): string {
        return `
        IMPORTANT: The prompts you receive may be compressed to optimize context usage.
        Before processing each prompt:
        1. Expand abbreviated instructions into complete sentences
        2. Restore any implied context or details
        3. Interpret measurements and specifications exactly as provided
        4. If a prompt seems unclear, use your best judgment to interpret the intent based on context
        `;
    }

    /**
     * Generates an SVG image based on user and system prompts
     * @param userPrompt The user prompt describing the image to generate
     * @param systemPrompt The system prompt with instructions for the AI
     * @returns The generated SVG content as a string, or empty string if generation fails
     */
    async generateImage(userPrompt: string, systemPrompt: string): Promise<string> {
        this.logger.log(`Generating SVG image with prompt length: ${userPrompt.length} chars`);
        const aiModel = this.modelConfigService.getImageGenerationModel();

        try {
            // Add specific instructions to prevent overlapping and cutoffs
            const enhancedUserPrompt = `${userPrompt}\n\nIMPORTANT: Ensure NO elements overlap and NOTHING is cut off in the SVG. Use a larger viewBox if needed.`;

            const messages: OpenAI.ChatCompletionMessageParam[] = [
                {
                    role: "system",
                    content: systemPrompt,
                },
                {
                    role: "developer",
                    content: "You are a developer who can generate SVG code. Please respond with only the SVG code. Ensure all elements are properly positioned with no overlaps.",
                },
                {
                    role: "user",
                    content: enhancedUserPrompt,
                }
            ];

            const response = await this.aiService.chatImage(
                aiModel,
                messages
            );

            if (!response || !response.choices || response.choices.length === 0) {
                this.logger.error('Empty or invalid response from AI service');
                return '';
            }

            let svgContent = response.choices[0].message.content as string;
            if (!svgContent) {
                this.logger.error('Empty content in AI response');
                return '';
            }

            // Extract only the SVG code from the response
            let svgMatch = svgContent.match(/<svg[\s\S]*?<\/svg>/);
            if (svgMatch) {
                const extractedSvg = svgMatch[0];
                this.logger.log(`Successfully extracted SVG code, length: ${extractedSvg.length} chars`);
                return this.validateAndFixSvg(extractedSvg);
            }

            // If no SVG tag found, try to clean up the response to remove any markdown code block formatting
            if (svgContent.includes('```')) {
                this.logger.log('Cleaning up markdown code blocks from response');
                // Extract content between triple backticks
                const matches = svgContent.match(/```(?:xml|html|svg)?\n?([\s\S]*?)```/);
                if (matches && matches[1]) {
                    svgContent = matches[1].trim();

                    // Try to extract SVG again from the cleaned content
                    svgMatch = svgContent.match(/<svg[\s\S]*?<\/svg>/);
                    if (svgMatch) {
                        const extractedSvg = svgMatch[0];
                        this.logger.log(`Successfully extracted SVG from code block, length: ${extractedSvg.length} chars`);
                        return this.validateAndFixSvg(extractedSvg);
                    }
                }
            }

            // If still no valid SVG found, check if the content starts with <svg
            if (svgContent.trim().startsWith('<svg')) {
                this.logger.log('Using full response as SVG content');
                return this.validateAndFixSvg(svgContent);
            }

            this.logger.warn('SVG content does not start with <svg tag, response may be invalid');
            return '';
        } catch (error) {
            this.logger.error(`Error generating SVG: ${error.message}`, error.stack);
            return '';
        }
    }

    /**
     * Validates and fixes common issues in SVG code
     * @param svgContent The SVG content to validate and fix
     * @returns The validated and fixed SVG content
     */
    private validateAndFixSvg(svgContent: string): string {
        if (!svgContent || !svgContent.includes('<svg')) {
            return '';
        }

        try {
            // Ensure the SVG has a proper viewBox if missing
            if (!svgContent.includes('viewBox')) {
                this.logger.warn('SVG missing viewBox attribute, adding default');
                svgContent = svgContent.replace('<svg', '<svg viewBox="0 0 500 400"');
            }

            // Ensure the SVG has width and height attributes if missing
            if (!svgContent.includes('width=')) {
                this.logger.warn('SVG missing width attribute, adding default');
                svgContent = svgContent.replace('<svg', '<svg width="100%"');
            }

            if (!svgContent.includes('height=')) {
                this.logger.warn('SVG missing height attribute, adding default');
                svgContent = svgContent.replace('<svg', '<svg height="100%"');
            }

            // Ensure preserveAspectRatio is set correctly
            if (!svgContent.includes('preserveAspectRatio')) {
                this.logger.warn('SVG missing preserveAspectRatio attribute, adding default');
                svgContent = svgContent.replace('<svg', '<svg preserveAspectRatio="xMidYMid meet"');
            }

            return svgContent;
        } catch (error) {
            this.logger.error(`Error validating/fixing SVG: ${error.message}`);
            // Return original content if fixing fails, or empty string if svgContent is undefined
            return svgContent || '';
        }
    }

    /**
     * Generates an SVG image (alias for generateImage for backward compatibility)
     * @param userPrompt The user prompt describing the image to generate
     * @param systemPrompt The system prompt with instructions for the AI
     * @returns The generated SVG content as a string, or empty string if generation fails
     */
    async generateSVG(userPrompt: string, systemPrompt: string): Promise<string> {
        this.logger.log(`Using generateSVG method with prompt length: ${userPrompt.length} chars`);
        // This method is kept for backward compatibility
        return this.generateImage(userPrompt, systemPrompt);
    }

    /**
     * Generates multiple SVG images in batches with optimized throughput
     * @param imagePrompts Array of image prompts
     * @param systemPrompt System prompt for image generation
     * @param worksheetId Optional worksheet ID for progress tracking
     * @param imageCallback Optional callback function to process each image as it's generated
     * @returns Array of SVG images in the same order as the prompts
     */
    async generateBatchImages(
        imagePrompts: string[],
        systemPrompt: string,
        worksheetId?: string,
        imageCallback?: (svgResult: string, batchIndex: number, imageIndex: number) => Promise<void>
    ): Promise<string[]> {
        if (!imagePrompts || imagePrompts.length === 0) {
            this.logger.warn('No image prompts provided for batch generation');
            return [];
        }

        this.logger.log(`Generating batch of ${imagePrompts.length} SVG images`);

        // Add decompression instructions to the system prompt
        const decompressionInstructions = this.getDecompressionInstructions();

        // Create an enhanced system prompt that instructs the AI to handle multiple prompts
        const enhancedSystemPrompt = `${systemPrompt}\n\n${decompressionInstructions}\n\nYou will receive a JSON array of image prompts. For each prompt in the array, generate an SVG image. Return a JSON array of SVG strings in the same order as the input prompts. Each SVG should be a complete, valid SVG code with proper viewBox, width, height, and preserveAspectRatio attributes.`;

        // Process images in batches to optimize throughput
        // Increased from 3 to 10 to improve throughput for worksheets with many images
        const MAX_BATCH_SIZE = 5;
        const results: string[] = [];
        const totalImages = imagePrompts.length;
        let processedCount = 0;

        // Track success/failure rates for batch processing
        const batchStats: BatchStats = {
            totalBatches: 0,
            successfulBatches: 0,
            failedBatches: 0,
            fallbacksTriggered: 0
        };

        // Emit initial progress update if worksheetId is provided
        if (worksheetId) {
            this.socketGateway.emitWorksheetProgress(
                worksheetId,
                0,
                totalImages,
                { imageGenerationStarted: true }
            );
        }

        // Calculate number of batches needed
        const batches = Math.ceil(totalImages / MAX_BATCH_SIZE);
        this.logger.log(`Processing ${totalImages} images in ${batches} batches of max ${MAX_BATCH_SIZE} images each`);

        // Process each batch
        for (let batchIndex = 0; batchIndex < batches; batchIndex++) {
            const start = batchIndex * MAX_BATCH_SIZE;
            const end = Math.min(start + MAX_BATCH_SIZE, totalImages);
            const originalBatchPrompts = imagePrompts.slice(start, end);

            // Apply compression to the batch prompts
            const batchPrompts = this.compressBatchPrompts(originalBatchPrompts);
            const currentBatchSize = batchPrompts.length;

            this.logger.log(`Processing batch ${batchIndex + 1}/${batches} with ${currentBatchSize} prompts`);
            batchStats.totalBatches++;

            // Convert the batch of prompts to a JSON string
            const promptsJson = JSON.stringify(batchPrompts);

            // Create an enhanced user prompt that includes the JSON array of prompts
            const enhancedUserPrompt = `Generate SVG images for the following prompts: ${promptsJson}\n\nIMPORTANT: Respond with a JSON array of SVG strings. Each SVG should ensure NO elements overlap and NOTHING is cut off. Use a larger viewBox if needed. Return ONLY the JSON array of SVG strings, nothing else.`;

            const aiModel = this.modelConfigService.getImageGenerationModel();

            // Track if we need to try a fallback with smaller batch size
            let fallbackNeeded = false;
            let fallbackBatchSize = Math.ceil(currentBatchSize / 2); // Start with half the size

            // Function to process a batch with potential fallback
            const processBatch = async (batchPrompts: string[], attemptNumber: number = 1, isFallback: boolean = false): Promise<string[] | null> => {
                try {
                    // Make an API call with the current batch of prompts
                    const messages: OpenAI.ChatCompletionMessageParam[] = [
                        {
                            role: "system",
                            content: enhancedSystemPrompt,
                        },
                        {
                            role: "developer",
                            content: "You are a developer who can generate multiple SVG images from a JSON array of prompts. Please respond with a JSON array of SVG strings in the same order as the input prompts.",
                        },
                        {
                            role: "user",
                            content: enhancedUserPrompt,
                        }
                    ];

                    const response = await this.aiService.chatImage(
                        aiModel,
                        messages
                    );

                    if (!response || !response.choices || response.choices.length === 0) {
                        const errorMessage = `Empty or invalid response from AI service for batch ${batchIndex + 1}${isFallback ? ' (fallback attempt)' : ''}, attempt ${attemptNumber}`;
                        this.logger.error(errorMessage);

                        // Log the error with detailed information
                        await this.aiServiceLogService.logAiServiceRequest({
                            worksheetId,
                            status: 'error',
                            errorMessage,
                            errorResponse: response,
                            prompts: batchPrompts,
                            systemPrompt: enhancedSystemPrompt,
                        });

                        // If this is already a fallback or the batch is small, don't try again
                        if (isFallback || batchPrompts.length <= 3 || attemptNumber > 1) {
                            // Update batch stats
                            batchStats.failedBatches++;

                            // Add empty strings for this batch
                            return Array(batchPrompts.length).fill('');
                        }

                        // Signal that we need to try a fallback
                        fallbackNeeded = true;
                        return null;
                    }

                    // Batch was successful
                    if (isFallback) {
                        batchStats.fallbacksTriggered++;
                    }
                    batchStats.successfulBatches++;

                    return null; // Continue with normal processing
                } catch (error) {
                    const errorMessage = `Error generating SVGs for batch ${batchIndex + 1}${isFallback ? ' (fallback attempt)' : ''}, attempt ${attemptNumber}: ${error.message}`;
                    this.logger.error(errorMessage, error.stack);

                    // Log the error with detailed information
                    await this.aiServiceLogService.logAiServiceRequest({
                        worksheetId,
                        status: 'error',
                        errorMessage,
                        errorResponse: error,
                        prompts: batchPrompts,
                        systemPrompt: enhancedSystemPrompt,
                    });

                    // If this is already a fallback or the batch is small, don't try again
                    if (isFallback || batchPrompts.length <= 3 || attemptNumber > 1) {
                        // Update batch stats
                        batchStats.failedBatches++;

                        // Add empty strings for this batch
                        return Array(batchPrompts.length).fill('');
                    }

                    // Signal that we need to try a fallback
                    fallbackNeeded = true;
                    return null;
                }
            };

            try {
                // First attempt with the full batch
                const earlyResult = await processBatch(batchPrompts);
                if (earlyResult) {
                    // If we got a result (empty strings), add them and continue to next batch
                    for (let i = 0; i < earlyResult.length; i++) {
                        const emptySvg = earlyResult[i];
                        results.push(emptySvg);

                        // Call the callback for this empty image if provided
                        if (imageCallback) {
                            try {
                                await imageCallback(emptySvg, batchIndex, i);
                            } catch (callbackError) {
                                this.logger.error(`Error in image callback for empty image: ${callbackError.message}`);
                            }
                        }
                    }
                    processedCount += earlyResult.length;
                    continue;
                }

                // If fallback is needed, process in smaller batches
                if (fallbackNeeded) {
                    this.logger.warn(`Batch ${batchIndex + 1} failed with ${batchPrompts.length} prompts, trying fallback with batch size ${fallbackBatchSize}`);

                    // Split the original batch into smaller sub-batches using Array.from instead of for loop
                    const subBatchCount = Math.ceil(originalBatchPrompts.length / fallbackBatchSize);
                    const subBatches: string[][] = Array.from(
                        { length: subBatchCount },
                        (_, i) => originalBatchPrompts.slice(i * fallbackBatchSize, (i + 1) * fallbackBatchSize)
                    );

                    // Process each sub-batch using Promise.all with map instead of for loop
                    await Promise.all(subBatches.map(async (subBatch, subIndex) => {
                        const subBatchPrompts = this.compressBatchPrompts(subBatch);
                        this.logger.log(`Processing fallback sub-batch ${subIndex + 1}/${subBatches.length} with ${subBatchPrompts.length} prompts`);

                        // Try the fallback batch
                        const subResult = await processBatch(subBatchPrompts, 1, true);
                        if (subResult) {
                            // Process each result in the sub-batch
                            await Promise.all(subResult.map(async (result, i) => {
                                results.push(result);

                                // Call the callback if provided
                                if (imageCallback) {
                                    try {
                                        await imageCallback(result, batchIndex, subIndex * fallbackBatchSize + i);
                                    } catch (callbackError) {
                                        this.logger.error(`Error in image callback for fallback sub-batch: ${callbackError.message}`);
                                    }
                                }
                            }));

                            processedCount += subResult.length;
                        }
                    }));

                    // Skip the rest of the normal processing for this batch
                    continue;
                }

                // Continue with normal processing if no fallback was needed
                const messages: OpenAI.ChatCompletionMessageParam[] = [
                    {
                        role: "system",
                        content: enhancedSystemPrompt,
                    },
                    {
                        role: "developer",
                        content: "You are a developer who can generate multiple SVG images from a JSON array of prompts. Please respond with a JSON array of SVG strings in the same order as the input prompts.",
                    },
                    {
                        role: "user",
                        content: enhancedUserPrompt,
                    }
                ];

                const response = await this.aiService.chatImage(
                    aiModel,
                    messages
                );

                if (!response || !response.choices || response.choices.length === 0) {
                    const errorMessage = `Empty or invalid response from AI service for batch ${batchIndex + 1}`;
                    this.logger.error(errorMessage);

                    // Log the error with detailed information
                    await this.aiServiceLogService.logAiServiceRequest({
                        worksheetId,
                        status: 'error',
                        errorMessage,
                        errorResponse: response,
                        prompts: batchPrompts,
                        systemPrompt: enhancedSystemPrompt,
                    });

                    // Add empty strings for this batch and continue with the next batch
                    // Using Array.fill and Promise.all instead of for loop
                    await Promise.all(
                        Array(batchPrompts.length).fill('').map(async (emptySvg, i) => {
                            results.push(emptySvg);

                            // Call the callback for this empty image if provided
                            if (imageCallback) {
                                try {
                                    await imageCallback(emptySvg, batchIndex, i);
                                } catch (callbackError) {
                                    this.logger.error(`Error in image callback for empty image in empty response case: ${callbackError.message}`);
                                }
                            }
                        })
                    );
                    processedCount += batchPrompts.length;
                    batchStats.failedBatches++;
                    continue;
                }

                let content = response.choices[0].message.content as string;
                if (!content) {
                    const errorMessage = `Empty content in AI response for batch ${batchIndex + 1}`;
                    this.logger.error(errorMessage);

                    // Log the error with detailed information
                    await this.aiServiceLogService.logAiServiceRequest({
                        worksheetId,
                        status: 'error',
                        errorMessage,
                        errorResponse: response,
                        prompts: batchPrompts,
                        systemPrompt: enhancedSystemPrompt,
                    });

                    // Add empty strings for this batch and continue with the next batch
                    // Using Array.fill and Promise.all instead of for loop
                    await Promise.all(
                        Array(batchPrompts.length).fill('').map(async (emptySvg, i) => {
                            results.push(emptySvg);

                            // Call the callback for this empty image if provided
                            if (imageCallback) {
                                try {
                                    await imageCallback(emptySvg, batchIndex, i);
                                } catch (callbackError) {
                                    this.logger.error(`Error in image callback for empty image in empty content case: ${callbackError.message}`);
                                }
                            }
                        })
                    );
                    processedCount += batchPrompts.length;
                    continue;
                }

                // Try to parse the response as JSON
                let svgArray: string[] = [];
                try {
                    // First, try to extract JSON array from the response if it's wrapped in markdown or other text
                    const jsonMatch = content.match(/\[\s*".*"\s*\]/s) || content.match(/\[\s*`.*`\s*\]/s);
                    if (jsonMatch) {
                        content = jsonMatch[0];
                    }

                    // Clean up the content if it contains markdown code blocks
                    if (content.includes('```')) {
                        const matches = content.match(/```(?:json)?\n?([\s\S]*?)```/);
                        if (matches && matches[1]) {
                            content = matches[1].trim();
                        }
                    }

                    // Parse the JSON array
                    svgArray = JSON.parse(content);

                    // If it's not an array, wrap it in an array
                    if (!Array.isArray(svgArray)) {
                        this.logger.warn(`Response for batch ${batchIndex + 1} is not an array, wrapping in array`);
                        svgArray = [content];
                    }
                } catch (error) {
                    const errorMessage = `Failed to parse JSON response for batch ${batchIndex + 1}: ${error.message}`;
                    this.logger.error(errorMessage);

                    // If JSON parsing fails, try to extract SVGs directly
                    const svgMatches = content.match(/<svg[\s\S]*?<\/svg>/g);
                    if (svgMatches && svgMatches.length > 0) {
                        this.logger.log(`Extracted ${svgMatches.length} SVGs directly from response for batch ${batchIndex + 1}`);
                        svgArray = svgMatches;

                        // Log partial success (recovered from error)
                        await this.aiServiceLogService.logAiServiceRequest({
                            worksheetId,
                            status: 'success',
                            errorMessage: `JSON parsing failed but recovered ${svgMatches.length} SVGs directly`,
                            prompts: batchPrompts,
                            systemPrompt: enhancedSystemPrompt,
                        });
                    } else {
                        // If no SVGs found, add empty strings for this batch
                        const noSvgErrorMessage = `No SVGs found in response for batch ${batchIndex + 1}`;
                        this.logger.error(noSvgErrorMessage);

                        // Log the error with detailed information
                        await this.aiServiceLogService.logAiServiceRequest({
                            worksheetId,
                            status: 'error',
                            errorMessage: `${errorMessage}. ${noSvgErrorMessage}`,
                            errorResponse: { content, error: error.message },
                            prompts: batchPrompts,
                            systemPrompt: enhancedSystemPrompt,
                        });

                        // Using Array.fill and Promise.all instead of for loop
                        await Promise.all(
                            Array(batchPrompts.length).fill('').map(async (emptySvg, i) => {
                                results.push(emptySvg);

                                // Call the callback for this empty image if provided
                                if (imageCallback) {
                                    try {
                                        await imageCallback(emptySvg, batchIndex, i);
                                    } catch (callbackError) {
                                        this.logger.error(`Error in image callback for empty image in no SVGs case: ${callbackError.message}`);
                                    }
                                }
                            })
                        );
                        processedCount += batchPrompts.length;
                        continue;
                    }
                }

                // Validate and fix each SVG using map instead of for loop
                const batchResults: string[] = await Promise.all(svgArray.map(async (svgContent, i) => {
                    // Extract SVG from string if needed
                    const svgMatch = svgContent.match(/<svg[\s\S]*?<\/svg>/);
                    if (svgMatch) {
                        svgContent = svgMatch[0];
                    }

                    // Validate and fix the SVG
                    const validatedSvg = this.validateAndFixSvg(svgContent);

                    // Call the callback for this individual image if provided
                    if (imageCallback && validatedSvg) {
                        try {
                            await imageCallback(validatedSvg, batchIndex, i);
                        } catch (callbackError) {
                            this.logger.error(`Error in image callback for batch ${batchIndex + 1}, image ${i}: ${callbackError.message}`);
                        }
                    }

                    return validatedSvg;
                }));

                // If we didn't get enough SVGs for this batch, pad with empty strings
                while (batchResults.length < batchPrompts.length) {
                    this.logger.warn(`Missing SVG in batch ${batchIndex + 1}, adding empty string`);
                    const emptySvg = '';
                    batchResults.push(emptySvg);

                    // Call the callback for this empty image if provided
                    if (imageCallback) {
                        try {
                            await imageCallback(emptySvg, batchIndex, batchResults.length - 1);
                        } catch (callbackError) {
                            this.logger.error(`Error in image callback for empty image: ${callbackError.message}`);
                        }
                    }
                }

                // If we got too many SVGs, trim the excess
                if (batchResults.length > batchPrompts.length) {
                    this.logger.warn(`Got ${batchResults.length} SVGs but only needed ${batchPrompts.length} for batch ${batchIndex + 1}, trimming excess`);
                    batchResults.splice(batchPrompts.length);
                }

                // Add the results from this batch to our collection
                results.push(...batchResults);
                processedCount += batchResults.length;

                // Log successful batch processing
                await this.aiServiceLogService.logAiServiceRequest({
                    worksheetId,
                    status: 'success',
                    prompts: batchPrompts,
                    systemPrompt: enhancedSystemPrompt,
                });

                // Emit progress update if worksheetId is provided
                if (worksheetId) {
                    this.socketGateway.emitWorksheetProgress(
                        worksheetId,
                        processedCount,
                        totalImages,
                        {
                            imageGenerationProgress: true,
                            currentBatch: batchIndex + 1,
                            totalBatches: batches,
                            processedImages: processedCount,
                            totalImages: totalImages
                        }
                    );
                    this.logger.log(`Image generation progress: ${processedCount}/${totalImages} for worksheet ${worksheetId}`);
                }

                // Add a small delay between batches to avoid rate limiting
                if (batchIndex < batches - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

            } catch (error) {
                const errorMessage = `Error generating SVGs for batch ${batchIndex + 1}: ${error.message}`;
                this.logger.error(errorMessage, error.stack);

                // Log the error with detailed information
                await this.aiServiceLogService.logAiServiceRequest({
                    worksheetId,
                    status: 'error',
                    errorMessage,
                    errorResponse: error,
                    prompts: batchPrompts,
                    systemPrompt: enhancedSystemPrompt,
                });

                // Add empty strings for this batch and continue with the next batch
                // Using Array.fill and Promise.all instead of for loop
                await Promise.all(
                    Array(batchPrompts.length).fill('').map(async (emptySvg, i) => {
                        results.push(emptySvg);

                        // Call the callback for this empty image if provided
                        if (imageCallback) {
                            try {
                                await imageCallback(emptySvg, batchIndex, i);
                            } catch (callbackError) {
                                this.logger.error(`Error in image callback for empty image in error case: ${callbackError.message}`);
                            }
                        }
                    })
                );
                processedCount += batchPrompts.length;
            }
        }

        // Emit final progress update if worksheetId is provided
        if (worksheetId) {
            this.socketGateway.emitWorksheetProgress(
                worksheetId,
                totalImages,
                totalImages,
                {
                    imageGenerationComplete: true,
                    processedImages: processedCount,
                    totalImages: totalImages
                }
            );
        }

        // Log batch processing statistics
        const successRate = batchStats.totalBatches > 0
            ? Math.round((batchStats.successfulBatches / batchStats.totalBatches) * 100)
            : 0;

        this.logger.log(
            `Batch processing statistics: ` +
            `${batchStats.successfulBatches}/${batchStats.totalBatches} batches successful (${successRate}%), ` +
            `${batchStats.failedBatches} batches failed, ` +
            `${batchStats.fallbacksTriggered} fallbacks triggered`
        );

        this.logger.log(`Completed batch generation of ${results.length} SVG images out of ${totalImages} prompts with MAX_BATCH_SIZE=${MAX_BATCH_SIZE}`);
        return results;
    }
}
