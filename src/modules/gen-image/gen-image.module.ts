import {forwardRef, Module} from '@nestjs/common';
import { GenImageService } from './gen-image.service';
import { BatchImageService } from './batch-image.service';
import {AiModule} from "../ai/ai.module";
import { MongooseModule } from '@nestjs/mongoose';
import { WorksheetPromptResult, WorksheetPromptResultSchema } from '../mongodb/schemas/worksheet-prompt-result.schema';
import { AiServiceLog, AiServiceLogSchema } from '../mongodb/schemas/ai-service-log.schema';
import { SocketModule } from '../socket/socket.module';
import { BuildPromptModule } from '../build-prompt/build-prompt.module';
import { AiServiceLogService } from './ai-service-log.service';
import { AiServiceLogController } from './ai-service-log.controller';

@Module({
  imports: [
      forwardRef(() => AiModule),
      MongooseModule.forFeature([
        { name: WorksheetPromptResult.name, schema: WorksheetPromptResultSchema },
        { name: AiServiceLog.name, schema: AiServiceLogSchema },
      ]),
      SocketModule,
      forwardRef(() => BuildPromptModule),
  ],
  controllers: [AiServiceLogController],
  providers: [GenImageService, BatchImageService, AiServiceLogService],
  exports: [GenImageService, BatchImageService, AiServiceLogService],
})
export class GenImageModule {}
