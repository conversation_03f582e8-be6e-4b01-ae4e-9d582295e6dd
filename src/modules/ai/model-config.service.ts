import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Service for managing AI model configuration with fallback logic
 * Centralizes model selection to ensure consistency across the application
 */
@Injectable()
export class ModelConfigService {
  private readonly DEFAULT_MODEL = 'gpt-4.1-nano';

  constructor(private readonly configService: ConfigService) {}

  /**
   * Gets the model for narrative structure extraction
   * Falls back to OPENAI_MODEL, then to default
   */
  getNarrativeStructureModel(): string {
    return (
      this.configService.get<string>('NARRATIVE_STRUCTURE_MODEL') ||
      this.configService.get<string>('OPENAI_MODEL') ||
      this.DEFAULT_MODEL
    );
  }

  /**
   * Gets the model for question generation
   * Falls back to OPENAI_MODEL, then to default
   */
  getQuestionGenerationModel(): string {
    return (
      this.configService.get<string>('QUESTION_GENERATION_MODEL') ||
      this.configService.get<string>('OPENAI_MODEL') ||
      this.DEFAULT_MODEL
    );
  }

  /**
   * Gets the model for image generation (SVG/image prompts)
   * Falls back to SVG_GENERATION_MODEL (for backward compatibility), 
   * then OPENAI_MODEL, then to default
   */
  getImageGenerationModel(): string {
    return (
      this.configService.get<string>('IMAGE_GENERATION_MODEL') ||
      this.configService.get<string>('SVG_GENERATION_MODEL') ||
      this.configService.get<string>('OPENAI_MODEL') ||
      this.DEFAULT_MODEL
    );
  }

  /**
   * Gets the general OpenAI model
   * Falls back to default if not set
   */
  getGeneralModel(): string {
    return (
      this.configService.get<string>('OPENAI_MODEL') ||
      this.DEFAULT_MODEL
    );
  }

  /**
   * Gets the LLM model for document processing
   * Falls back to OPENAI_MODEL, then to default
   */
  getLLMModel(): string {
    return (
      this.configService.get<string>('LLM_MODEL') ||
      this.configService.get<string>('OPENAI_MODEL') ||
      this.DEFAULT_MODEL
    );
  }

  /**
   * Gets the multimodal model for image analysis
   * Currently hardcoded but made configurable for future use
   */
  getMultimodalModel(): string {
    return (
      this.configService.get<string>('MULTIMODAL_MODEL') ||
      'qwen/qwen2.5-vl-72b-instruct'
    );
  }
}
