import { Injectable, Logger } from '@nestjs/common';
import { AiService } from './ai.service';
import { GoogleAiService } from './google-ai.service';
import { ExerciseQuestionItem } from '../prompt/interfaces/exercise-result.interface';
import { withRetry, RetryOptions } from '../../core/utils/retry.util';
import { WebSocketErrorCode } from '../../core/enums/websocket-error-codes.enum';

export interface AiGenerationRequest {
  userRequest: any;
  documentResults?: any;
  worksheetId?: string;
  questionCount: number;
  subject?: string;
  grade?: string;
  questionTypes?: string[];
}

export interface AiGenerationResult {
  questions: ExerciseQuestionItem[];
  provider: 'openai' | 'google-ai' | 'cached-content' | 'none';
  success: boolean;
  error?: string;
  attempts: {
    openai?: { success: boolean; error?: string; duration?: number };
    googleAi?: { success: boolean; error?: string; duration?: number };
    cachedContent?: { success: boolean; error?: string; duration?: number };
  };
  totalDuration: number;
}

export interface CachedContentQuestion {
  type: string;
  content: string;
  options?: any;
  answer: any;
  explain?: string;
  subject?: string;
  grade?: string;
  difficultyLevel?: string;
}

@Injectable()
export class AiOrchestrationService {
  private readonly logger = new Logger(AiOrchestrationService.name);

  constructor(
    private aiService: AiService,
    private googleAiService: GoogleAiService,
  ) {}

  /**
   * Generate questions using a resilient AI service chain
   * Attempts OpenAI -> Google AI -> Cached Content in sequence
   */
  async generateQuestions(request: AiGenerationRequest): Promise<AiGenerationResult> {
    const startTime = Date.now();
    const result: AiGenerationResult = {
      questions: [],
      provider: 'none',
      success: false,
      attempts: {},
      totalDuration: 0,
    };

    this.logger.log(`Starting AI question generation for ${request.questionCount} questions`);

    // Attempt 1: OpenAI via OpenRouter API
    try {
      this.logger.log('Attempting question generation with OpenAI via OpenRouter API');
      const openaiResult = await this.attemptOpenAiGeneration(request);
      
      result.attempts.openai = openaiResult;
      
      if (openaiResult.success && openaiResult.questions && openaiResult.questions.length > 0) {
        result.questions = openaiResult.questions;
        result.provider = 'openai';
        result.success = true;
        result.totalDuration = Date.now() - startTime;
        
        this.logger.log(`Successfully generated ${result.questions.length} questions using OpenAI via OpenRouter API`);
        return result;
      }
      
      this.logger.warn(`OpenAI via OpenRouter API failed or returned no questions: ${openaiResult.error}`);
    } catch (error) {
      this.logger.error(`OpenAI via OpenRouter API attempt failed: ${error.message}`, error.stack);
      result.attempts.openai = {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
      };
    }

    // Attempt 2: Google AI
    try {
      this.logger.log('Attempting question generation with Google AI');
      const googleAiResult = await this.attemptGoogleAiGeneration(request);
      
      result.attempts.googleAi = googleAiResult;
      
      if (googleAiResult.success && googleAiResult.questions && googleAiResult.questions.length > 0) {
        result.questions = googleAiResult.questions;
        result.provider = 'google-ai';
        result.success = true;
        result.totalDuration = Date.now() - startTime;
        
        this.logger.log(`Successfully generated ${result.questions.length} questions using Google AI`);
        return result;
      }
      
      this.logger.warn(`Google AI failed or returned no questions: ${googleAiResult.error}`);
    } catch (error) {
      this.logger.error(`Google AI attempt failed: ${error.message}`, error.stack);
      result.attempts.googleAi = {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
      };
    }

    // Attempt 3: Cached Content
    try {
      this.logger.log('Attempting to retrieve questions from cached content');
      const cachedResult = await this.attemptCachedContentRetrieval(request);
      
      result.attempts.cachedContent = cachedResult;
      
      if (cachedResult.success && cachedResult.questions && cachedResult.questions.length > 0) {
        result.questions = cachedResult.questions;
        result.provider = 'cached-content';
        result.success = true;
        result.totalDuration = Date.now() - startTime;
        
        this.logger.log(`Successfully retrieved ${result.questions.length} questions from cached content`);
        return result;
      }
      
      this.logger.warn(`Cached content retrieval failed: ${cachedResult.error}`);
    } catch (error) {
      this.logger.error(`Cached content attempt failed: ${error.message}`, error.stack);
      result.attempts.cachedContent = {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
      };
    }

    // All attempts failed
    result.totalDuration = Date.now() - startTime;
    result.error = 'All AI providers and cached content failed';
    
    this.logger.error(
      `All AI generation attempts failed for worksheet ${request.worksheetId}. ` +
      `OpenAI: ${result.attempts.openai?.error || 'not attempted'}, ` +
      `Google AI: ${result.attempts.googleAi?.error || 'not attempted'}, ` +
      `Cached Content: ${result.attempts.cachedContent?.error || 'not attempted'}`
    );

    return result;
  }

  /**
   * Attempt question generation using OpenAI via OpenRouter API
   */
  private async attemptOpenAiGeneration(request: AiGenerationRequest): Promise<{
    success: boolean;
    questions?: ExerciseQuestionItem[];
    error?: string;
    duration: number;
  }> {
    const startTime = Date.now();
    
    try {
      // Create a timeout wrapper for OpenAI generation
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('OpenAI generation timeout after 120 seconds')), 120000);
      });

      // Simulate the prompt service call that would normally be made
      // This would need to be integrated with your existing prompt generation logic
      const generationPromise = this.generateWithOpenAi(request);
      
      const questions = await Promise.race([generationPromise, timeoutPromise]);
      
      return {
        success: true,
        questions,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Attempt question generation using Google AI
   */
  private async attemptGoogleAiGeneration(request: AiGenerationRequest): Promise<{
    success: boolean;
    questions?: ExerciseQuestionItem[];
    error?: string;
    duration: number;
  }> {
    const startTime = Date.now();
    
    try {
      // Create a timeout wrapper for Google AI generation
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Google AI generation timeout after 120 seconds')), 120000);
      });

      const generationPromise = this.generateWithGoogleAi(request);
      
      const questions = await Promise.race([generationPromise, timeoutPromise]);
      
      return {
        success: true,
        questions,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Attempt to retrieve questions from cached content
   */
  private async attemptCachedContentRetrieval(request: AiGenerationRequest): Promise<{
    success: boolean;
    questions?: ExerciseQuestionItem[];
    error?: string;
    duration: number;
  }> {
    const startTime = Date.now();
    
    try {
      // This would retrieve pre-generated questions from a cache or database
      // For now, we'll implement a basic fallback with some sample questions
      const cachedQuestions = await this.getCachedQuestions(request);
      
      if (!cachedQuestions || cachedQuestions.length === 0) {
        return {
          success: false,
          error: 'No cached questions available for the requested criteria',
          duration: Date.now() - startTime,
        };
      }
      
      // Convert cached questions to the expected format
      const questions: ExerciseQuestionItem[] = cachedQuestions.slice(0, request.questionCount).map(cached => ({
        type: cached.type,
        content: cached.content,
        options: cached.options,
        answer: cached.answer,
        explain: cached.explain || '',
        imagePrompt: '',
        image: '',
        subject: cached.subject || request.subject || '',
        parentSubject: request.subject || '',
        childSubject: '',
        grade: cached.grade || request.grade || '',
        language: 'English',
      }));
      
      return {
        success: true,
        questions,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
      };
    }
  }

  /**
   * Generate questions using OpenAI service
   * This is a placeholder that would integrate with your existing prompt service
   */
  private async generateWithOpenAi(request: AiGenerationRequest): Promise<ExerciseQuestionItem[]> {
    // This would integrate with your existing PromptService.generatePrompt method
    // For now, we'll throw an error to simulate the integration point
    throw new Error('OpenAI generation integration point - needs to be connected to PromptService');
  }

  /**
   * Generate questions using Google AI service
   */
  private async generateWithGoogleAi(request: AiGenerationRequest): Promise<ExerciseQuestionItem[]> {
    // This would use the GoogleAiService to generate questions
    // Implementation would depend on your existing Google AI integration
    throw new Error('Google AI generation integration point - needs implementation');
  }

  /**
   * Retrieve cached questions based on request criteria
   */
  private async getCachedQuestions(request: AiGenerationRequest): Promise<CachedContentQuestion[]> {
    // This would retrieve from a database or cache
    // For now, return empty array to indicate no cached content available
    this.logger.debug(`Looking for cached questions: subject=${request.subject}, grade=${request.grade}, count=${request.questionCount}`);
    return [];
  }
}
