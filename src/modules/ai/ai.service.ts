import {Injectable, OnModuleInit} from '@nestjs/common';
import {ConfigService} from "@nestjs/config";
import OpenAI from "openai";

@Injectable()
export class AiService implements OnModuleInit{
    private ai:OpenAI;

    constructor(
        private configService:  ConfigService,
    ) {}

    onModuleInit() {
        const apiKey = this.configService.get<string>('OPENAI_API_KEY');
        this.ai = new OpenAI({
            apiKey: apiKey,
            baseURL: 'https://openrouter.ai/api/v1',

        });
    }


    async chat(
        model: string, 
        messages: OpenAI.ChatCompletionMessageParam[],
    ): Promise<OpenAI.ChatCompletion> {
        return this.ai.chat.completions.create({
            model,
            messages,
            response_format: {
                type: "json_object"
            },
        });
    }

    async chatImage(
        model: string, 
        messages: OpenAI.ChatCompletionMessageParam[],
    ): Promise<OpenAI.ChatCompletion> {
        return this.ai.chat.completions.create({
            model,
            messages,
        });
    }


}
