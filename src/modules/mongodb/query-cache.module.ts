import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { QueryCache, QueryCacheSchema } from './schemas/query-cache.schema';
import { QueryCacheService } from './services/query-cache.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: QueryCache.name, schema: QueryCacheSchema },
    ]),
  ],
  providers: [QueryCacheService],
  exports: [QueryCacheService],
})
export class QueryCacheModule {}