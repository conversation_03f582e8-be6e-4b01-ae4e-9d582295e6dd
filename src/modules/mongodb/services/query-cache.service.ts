import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { QueryCache } from '../schemas/query-cache.schema';
import * as crypto from 'crypto';

@Injectable()
export class QueryCacheService {
  private readonly logger = new Logger(QueryCacheService.name);
  private readonly memoryCache = new Map<string, any>();
  private readonly cacheStats = {
    memoryHits: 0,
    memoryMisses: 0,
    mongoHits: 0,
    mongoMisses: 0,
    lastCleared: new Date(),
  };

  constructor(
    @InjectModel(QueryCache.name)
    private queryCacheModel: Model<QueryCache>,
  ) {}

  /**
   * Generates a cache key from a query and parameters
   * @param query The query string
   * @param params The query parameters
   * @returns A unique cache key
   */
  generateCacheKey(query: string, params: Record<string, any>): string {
    const combinedString = `${query}|${JSON.stringify(params)}`;
    return crypto.createHash('md5').update(combinedString).digest('hex');
  }

  /**
   * Gets a result from cache (memory first, then MongoDB)
   * @param cacheKey The cache key
   * @returns The cached result or null if not found
   */
  async getFromCache(cacheKey: string): Promise<any> {
    // Check memory cache first (fastest)
    if (this.memoryCache.has(cacheKey)) {
      this.cacheStats.memoryHits++;
      this.logger.debug(`Memory cache hit for key: ${cacheKey}`);
      return this.memoryCache.get(cacheKey);
    }

    this.cacheStats.memoryMisses++;

    // Then check MongoDB cache
    try {
      const cacheEntry = await this.queryCacheModel.findOne({ cacheKey }).exec();

      if (cacheEntry) {
        // Update hit count
        await this.queryCacheModel.updateOne(
          { _id: cacheEntry._id },
          { $inc: { hitCount: 1 } }
        ).exec();

        this.cacheStats.mongoHits++;
        this.logger.debug(`MongoDB cache hit for key: ${cacheKey}`);

        // Store in memory cache for faster access next time
        this.memoryCache.set(cacheKey, cacheEntry.result);

        return cacheEntry.result;
      }
    } catch (error) {
      this.logger.error(`Error retrieving from MongoDB cache: ${error.message}`);
    }

    this.cacheStats.mongoMisses++;
    return null;
  }

  /**
   * Saves a result to cache (both memory and MongoDB)
   * @param query The query string
   * @param params The query parameters
   * @param result The result to cache
   * @param ttlMinutes Time to live in minutes
   * @returns The cache key
   */
  async saveToCache(
    query: string,
    params: Record<string, any>,
    result: any,
    ttlMinutes: number = 30
  ): Promise<string> {
    const cacheKey = this.generateCacheKey(query, params);

    // Save to memory cache
    this.memoryCache.set(cacheKey, result);

    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + ttlMinutes);

    try {
      // Check if entry already exists
      const existingEntry = await this.queryCacheModel.findOne({ cacheKey }).exec();

      if (existingEntry) {
        // Update existing entry
        await this.queryCacheModel.updateOne(
          { _id: existingEntry._id },
          { 
            result,
            expiresAt,
            $inc: { hitCount: 1 }
          }
        ).exec();
      } else {
        // Create new entry
        await this.queryCacheModel.create({
          cacheKey,
          query,
          queryParams: params,
          result,
          hitCount: 1,
          expiresAt,
        });
      }

      this.logger.debug(`Saved to cache with key: ${cacheKey}, expires in ${ttlMinutes} minutes`);
    } catch (error) {
      this.logger.error(`Error saving to MongoDB cache: ${error.message}`);
    }

    return cacheKey;
  }

  /**
   * Invalidates a cache entry
   * @param cacheKey The cache key to invalidate
   */
  async invalidateCache(cacheKey: string): Promise<void> {
    // Remove from memory cache
    this.memoryCache.delete(cacheKey);

    // Remove from MongoDB cache
    try {
      await this.queryCacheModel.deleteOne({ cacheKey }).exec();
      this.logger.debug(`Invalidated cache entry with key: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`Error invalidating MongoDB cache: ${error.message}`);
    }
  }

  /**
   * Gets cache statistics
   * @returns Cache statistics
   */
  getCacheStats(): Record<string, any> {
    return {
      ...this.cacheStats,
      memorySize: this.memoryCache.size,
    };
  }

  /**
   * Clears the memory cache
   */
  clearMemoryCache(): void {
    const cacheSize = this.memoryCache.size;
    this.memoryCache.clear();
    this.cacheStats.lastCleared = new Date();
    this.logger.debug(`Cleared memory cache with ${cacheSize} entries`);
  }

  /**
   * Clears expired entries from MongoDB cache
   * @returns Number of expired entries removed
   */
  async clearExpiredEntries(): Promise<number> {
    try {
      const now = new Date();
      const result = await this.queryCacheModel.deleteMany({
        expiresAt: { $lt: now }
      }).exec();

      const deletedCount = result.deletedCount || 0;
      this.logger.debug(`Cleared ${deletedCount} expired entries from MongoDB cache`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Error clearing expired entries from MongoDB cache: ${error.message}`);
      return 0;
    }
  }
}
