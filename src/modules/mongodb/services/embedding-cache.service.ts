import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EmbeddingCache } from '../schemas/embedding-cache.schema';
import * as crypto from 'crypto';

@Injectable()
export class EmbeddingCacheService {
  private readonly logger = new Logger(EmbeddingCacheService.name);
  private readonly memoryCache = new Map<string, any>();
  private readonly cacheStats = {
    memoryHits: 0,
    memoryMisses: 0,
    mongoHits: 0,
    mongoMisses: 0,
    lastCleared: new Date(),
  };

  // Default TTL for embeddings is 7 days (in minutes)
  private readonly DEFAULT_TTL_MINUTES = 7 * 24 * 60;

  constructor(
    @InjectModel(EmbeddingCache.name)
    private embeddingCacheModel: Model<EmbeddingCache>,
  ) {}

  /**
   * Generates a cache key from text
   * @param text The text to generate embeddings for
   * @returns A unique cache key
   */
  generateCacheKey(text: string): string {
    return crypto.createHash('md5').update(text).digest('hex');
  }

  /**
   * Gets an embedding from cache (memory first, then MongoDB)
   * @param text The text to get embeddings for
   * @returns The cached embedding or null if not found
   */
  async getEmbedding(text: string): Promise<any> {
    const cacheKey = this.generateCacheKey(text);

    // Check memory cache first (fastest)
    if (this.memoryCache.has(cacheKey)) {
      this.cacheStats.memoryHits++;
      this.logger.debug(`Memory cache hit for embedding: ${cacheKey.substring(0, 8)}...`);
      return this.memoryCache.get(cacheKey);
    }

    this.cacheStats.memoryMisses++;

    // Then check MongoDB cache
    try {
      const cacheEntry = await this.embeddingCacheModel.findOne({ cacheKey }).exec();

      if (cacheEntry) {
        // Update hit count
        await this.embeddingCacheModel.updateOne(
          { _id: cacheEntry._id },
          { $inc: { hitCount: 1 } }
        ).exec();

        this.cacheStats.mongoHits++;
        this.logger.debug(`MongoDB cache hit for embedding: ${cacheKey.substring(0, 8)}...`);

        // Store in memory cache for faster access next time
        this.memoryCache.set(cacheKey, cacheEntry.embedding);

        return cacheEntry.embedding;
      }
    } catch (error) {
      this.logger.error(`Error retrieving embedding from MongoDB cache: ${error.message}`);
    }

    this.cacheStats.mongoMisses++;
    return null;
  }

  /**
   * Saves an embedding to cache (both memory and MongoDB)
   * @param text The text that was embedded
   * @param embedding The embedding to cache
   * @param ttlMinutes Time to live in minutes (defaults to 7 days)
   * @returns The cache key
   */
  async saveEmbedding(
    text: string,
    embedding: any,
    ttlMinutes: number = this.DEFAULT_TTL_MINUTES
  ): Promise<string> {
    const cacheKey = this.generateCacheKey(text);

    // Save to memory cache
    this.memoryCache.set(cacheKey, embedding);

    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + ttlMinutes);

    try {
      // Check if entry already exists
      const existingEntry = await this.embeddingCacheModel.findOne({ cacheKey }).exec();

      if (existingEntry) {
        // Update existing entry
        await this.embeddingCacheModel.updateOne(
          { _id: existingEntry._id },
          { 
            embedding,
            expiresAt,
            $inc: { hitCount: 1 }
          }
        ).exec();
      } else {
        // Create new entry
        await this.embeddingCacheModel.create({
          cacheKey,
          text,
          embedding,
          hitCount: 1,
          expiresAt,
        });
      }

      this.logger.debug(`Saved embedding to cache with key: ${cacheKey.substring(0, 8)}..., expires in ${ttlMinutes} minutes`);
    } catch (error) {
      this.logger.error(`Error saving embedding to MongoDB cache: ${error.message}`);
    }

    return cacheKey;
  }

  /**
   * Gets cache statistics
   * @returns Cache statistics
   */
  getCacheStats(): Record<string, any> {
    return {
      ...this.cacheStats,
      memorySize: this.memoryCache.size,
    };
  }

  /**
   * Clears the memory cache
   */
  clearMemoryCache(): void {
    const cacheSize = this.memoryCache.size;
    this.memoryCache.clear();
    this.cacheStats.lastCleared = new Date();
    this.logger.debug(`Cleared embedding memory cache with ${cacheSize} entries`);
  }

  /**
   * Clears expired entries from MongoDB cache
   * @returns Number of expired entries removed
   */
  async clearExpiredEntries(): Promise<number> {
    try {
      const now = new Date();
      const result = await this.embeddingCacheModel.deleteMany({
        expiresAt: { $lt: now }
      }).exec();

      const deletedCount = result.deletedCount || 0;
      this.logger.debug(`Cleared ${deletedCount} expired entries from embedding MongoDB cache`);
      return deletedCount;
    } catch (error) {
      this.logger.error(`Error clearing expired entries from embedding MongoDB cache: ${error.message}`);
      return 0;
    }
  }
}
