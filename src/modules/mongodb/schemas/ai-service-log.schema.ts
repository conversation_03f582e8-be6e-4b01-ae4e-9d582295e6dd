import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class AiServiceLog extends Document {
  @Prop({ required: true, unique: true })
  batchId: string;

  @Prop()
  worksheetId: string;

  @Prop({ required: true, enum: ['success', 'error'] })
  status: string;

  @Prop()
  errorMessage: string;

  @Prop({ type: Object })
  errorResponse: any;

  @Prop({ type: [String], required: true })
  prompts: string[];

  @Prop()
  systemPrompt: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const AiServiceLogSchema = SchemaFactory.createForClass(
  AiServiceLog,
);