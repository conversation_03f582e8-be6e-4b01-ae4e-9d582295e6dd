import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class QueryCache extends Document {
  @Prop({ required: true, index: true })
  cacheKey: string;

  @Prop({ required: true })
  query: string;

  @Prop({ type: Object, required: true })
  queryParams: Record<string, any>;

  @Prop({ type: Object, required: true })
  result: any;

  @Prop({ default: 1 })
  hitCount: number;

  @Prop({ required: true })
  expiresAt: Date;
}

export const QueryCacheSchema = SchemaFactory.createForClass(QueryCache);

// Add TTL index for automatic expiration
QueryCacheSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });