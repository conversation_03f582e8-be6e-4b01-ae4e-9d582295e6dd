import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class WorksheetDocument extends Document {
  @Prop({ required: true })
  worksheetId: string;

  @Prop({ required: true })
  topic: string;

  @Prop({ required: true })
  grade: string;

  @Prop({ type: Object, required: true })
  documentResult: any;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;

  @Prop({ default: () => {
    // Default TTL: 7 days from now
    const date = new Date();
    date.setDate(date.getDate() + 7);
    return date;
  }})
  expiresAt: Date;

  @Prop({ default: 1 })
  hitCount: number;

  @Prop({ default: false })
  fromCache: boolean;
}

export const WorksheetDocumentSchema =
  SchemaFactory.createForClass(WorksheetDocument);

// Add TTL index for automatic expiration
WorksheetDocumentSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
