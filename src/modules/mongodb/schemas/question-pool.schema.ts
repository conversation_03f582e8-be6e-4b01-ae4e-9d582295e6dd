import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class QuestionPool extends Document {
  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  content: string;

  @Prop({ type: [String] })
  options?: string[];

  @Prop({ type: [String] })
  answer?: string[];

  @Prop()
  explain?: string;

  @Prop()
  imagePrompt?: string;

  @Prop()
  image?: string;

  @Prop()
  subject?: string; // Parent subject category

  @Prop()
  parentSubject?: string; // Subject type parent

  @Prop()
  childSubject?: string; // Subject type child

  @Prop({ type: Object })
  optionValue?: Record<string, any>; // Reference to option values used

  @Prop()
  grade?: string; // Grade level for the question

  @Prop({ default: 'English' })
  language?: string; // Language of the question

  @Prop({ default: 'active', enum: ['active', 'inactive'] })
  status: string;

  @Prop({ enum: ['Easy', 'Medium', 'Advanced'] })
  difficultyLevel?: string; // Direct difficulty level field for efficient querying

  @Prop({ enum: ['Easy', 'Medium', 'Advanced'] })
  difficulty?: string; // Alias for difficultyLevel for compatibility

  @Prop({ type: Object })
  metadata?: {
    tags?: string[];
    keywords?: string[];
    estimatedTimeMinutes?: number;
    cognitiveLevel?: string;
    learningObjectives?: string[];
    prerequisites?: string[];
    hints?: string[];
    references?: string[];
    authorNotes?: string;
    reviewNotes?: string;
    lastReviewDate?: Date;
    nextReviewDate?: Date;
  };

  @Prop({ default: false })
  isPublic?: boolean; // Whether the question is publicly accessible

  @Prop()
  schoolId?: string; // School ID for school-specific questions

  @Prop({ type: Date })
  lastSelectedTimestamp?: Date; // For diversity tracking

  @Prop({ type: Number, default: 0 })
  selectionFrequency?: number; // For diversity tracking

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const QuestionPoolSchema = SchemaFactory.createForClass(QuestionPool);

// Add schema validation to ensure consistency between childSubject and optionValue fields
QuestionPoolSchema.pre('save', function(next) {
  // Ensure consistency between childSubject and optionValue.value
  if (this.optionValue?.value && !this.childSubject) {
    this.childSubject = this.optionValue.value;
  } else if (this.childSubject && (!this.optionValue || !this.optionValue.value)) {
    if (!this.optionValue) {
      this.optionValue = {};
    }
    this.optionValue.value = this.childSubject;
  }

  next();
});

// Add validation middleware to warn about inconsistencies
QuestionPoolSchema.pre('save', function(next) {
  if (this.childSubject && this.optionValue?.value && this.childSubject !== this.optionValue.value) {
    console.warn(`Warning: Inconsistent field values detected for document ${this._id}: childSubject="${this.childSubject}" vs optionValue.value="${this.optionValue.value}". Using childSubject value.`);
    this.optionValue.value = this.childSubject;
  }

  next();
});

// Add custom validation method
QuestionPoolSchema.methods.validateFieldConsistency = function() {
  const errors: string[] = [];

  if (this.optionValue?.value && this.childSubject && this.optionValue.value !== this.childSubject) {
    errors.push(`Inconsistent values: childSubject="${this.childSubject}" vs optionValue.value="${this.optionValue.value}"`);
  }

  if (!this.childSubject && !this.optionValue?.value) {
    errors.push('Both childSubject and optionValue.value are missing');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Add indexes for efficient filtering
QuestionPoolSchema.index({ subject: 1 });
QuestionPoolSchema.index({ parentSubject: 1 });
QuestionPoolSchema.index({ childSubject: 1 });
QuestionPoolSchema.index({ status: 1 });
QuestionPoolSchema.index({ type: 1 });
QuestionPoolSchema.index({ 'optionValue.id': 1 });
QuestionPoolSchema.index({ 'optionValue.value': 1 }); // Added for field consistency fix
QuestionPoolSchema.index({ grade: 1 });
QuestionPoolSchema.index({ language: 1 });
QuestionPoolSchema.index({ difficultyLevel: 1 });
QuestionPoolSchema.index({ lastSelectedTimestamp: 1 });
QuestionPoolSchema.index({ selectionFrequency: 1 });

// Optimized compound indexes for common query patterns
// Based on analysis of getRandomQuestions aggregation pipelines

// Primary compound index for most common queries (status + subject hierarchy + difficulty + type)
QuestionPoolSchema.index({ status: 1, subject: 1, grade: 1, difficultyLevel: 1, type: 1 });
QuestionPoolSchema.index({ status: 1, parentSubject: 1, grade: 1, difficultyLevel: 1, type: 1 });
QuestionPoolSchema.index({ status: 1, childSubject: 1, grade: 1, difficultyLevel: 1, type: 1 });
// Added compound indexes for optionValue.value to support field consistency fix
QuestionPoolSchema.index({ status: 1, 'optionValue.value': 1, grade: 1, difficultyLevel: 1, type: 1 });

// Secondary compound indexes for distribution-based queries
QuestionPoolSchema.index({ status: 1, difficultyLevel: 1, type: 1, grade: 1 });
QuestionPoolSchema.index({ status: 1, subject: 1, difficultyLevel: 1, type: 1 });
QuestionPoolSchema.index({ status: 1, parentSubject: 1, difficultyLevel: 1, type: 1 });

// Language-specific compound indexes for multilingual support
QuestionPoolSchema.index({ status: 1, language: 1, grade: 1, difficultyLevel: 1 });
QuestionPoolSchema.index({ status: 1, language: 1, subject: 1, type: 1 });

// Diversity tracking compound indexes for selection frequency optimization
QuestionPoolSchema.index({ status: 1, lastSelectedTimestamp: 1, selectionFrequency: 1 });
QuestionPoolSchema.index({ status: 1, difficultyLevel: 1, lastSelectedTimestamp: 1 });

// Performance optimization indexes for aggregation pipelines
QuestionPoolSchema.index({ status: 1, type: 1, _id: 1 }); // For $nin exclusion queries
QuestionPoolSchema.index({ status: 1, grade: 1, subject: 1, _id: 1 }); // For replacement queries
