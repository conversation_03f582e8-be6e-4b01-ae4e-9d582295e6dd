import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class EmbeddingCache extends Document {
  @Prop({ required: true, index: true })
  cacheKey: string;

  @Prop({ required: true })
  text: string;

  @Prop({ type: Object, required: true })
  embedding: any;

  @Prop({ default: 1 })
  hitCount: number;

  @Prop({ required: true })
  expiresAt: Date;
}

export const EmbeddingCacheSchema = SchemaFactory.createForClass(EmbeddingCache);

// Add TTL index for automatic expiration (7 days)
EmbeddingCacheSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });