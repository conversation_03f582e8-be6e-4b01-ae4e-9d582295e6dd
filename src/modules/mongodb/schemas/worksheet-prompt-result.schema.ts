import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class WorksheetPromptResult extends Document {
  @Prop({ required: true })
  worksheetId: string;

  @Prop({ type: Object, required: true })
  promptResult: any;

  @Prop({ default: 0 })
  currentQuestionCount: number;

  @Prop({ required: true })
  totalQuestionCount: number;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const WorksheetPromptResultSchema = SchemaFactory.createForClass(
  WorksheetPromptResult,
);
