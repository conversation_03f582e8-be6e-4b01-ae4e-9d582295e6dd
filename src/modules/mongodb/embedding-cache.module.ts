import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EmbeddingCache, EmbeddingCacheSchema } from './schemas/embedding-cache.schema';
import { EmbeddingCacheService } from './services/embedding-cache.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: EmbeddingCache.name, schema: EmbeddingCacheSchema },
    ]),
  ],
  providers: [EmbeddingCacheService],
  exports: [EmbeddingCacheService],
})
export class EmbeddingCacheModule {}