import { Injectable, Logger, ConflictException, ForbiddenException } from '@nestjs/common';
import { RedisService } from '../../redis/redis.service';
import { v4 as uuidv4 } from 'uuid';
import { 
  IQuestionLock, 
  ILockRequest, 
  ILockResponse, 
  ICollaborationError 
} from '../interfaces/collaboration.interface';
import { 
  LockType, 
  CollaborationErrorCode 
} from '../enums/collaboration-events.enum';
import { UserContext } from './worksheet-question.service';

@Injectable()
export class WorksheetQuestionLockingService {
  private readonly logger = new Logger(WorksheetQuestionLockingService.name);
  private readonly LOCK_PREFIX = 'worksheet:lock';
  private readonly SESSION_PREFIX = 'worksheet:session';
  private readonly DEFAULT_LOCK_TIMEOUT = 5 * 60 * 1000; // 5 minutes

  constructor(private readonly redisService: RedisService) {}

  /**
   * Acquire a lock on a question
   */
  async acquireLock(
    worksheetId: string,
    questionId: string,
    user: UserContext,
    request: ILockRequest
  ): Promise<ILockResponse> {
    const lockKey = this.getLockKey(worksheetId, questionId);
    const sessionId = uuidv4();
    const redis = this.redisService.getClient('COMMON_CACHE_NAME');

    try {
      // Check if question is already locked
      const existingLock = await this.getLock(worksheetId, questionId);
      
      if (existingLock && existingLock.isActive) {
        // Check if lock is owned by the same user
        if (existingLock.lockedBy === user.sub) {
          // Renew existing lock
          return this.renewLock(worksheetId, questionId, user);
        }

        // Check if force acquisition is requested (admin only)
        if (request.force && user.role === 'admin') {
          await this.releaseLock(worksheetId, questionId, existingLock.lockedBy);
          this.logger.warn(`Admin ${user.sub} force-acquired lock for question ${questionId}`);
        } else {
          return {
            success: false,
            conflictingLock: existingLock,
            error: {
              errorCode: CollaborationErrorCode.LOCK_ACQUISITION_FAILED,
              message: `Question is already locked by another user`,
              timestamp: new Date(),
              recoverable: true,
              suggestedAction: 'Wait for lock to expire or contact the user'
            }
          };
        }
      }

      // Create new lock
      const duration = request.duration || this.DEFAULT_LOCK_TIMEOUT;
      const expiresAt = new Date(Date.now() + duration);
      
      const lock: IQuestionLock = {
        questionId,
        worksheetId,
        lockedBy: user.sub,
        lockedByName: user.email, // Could be enhanced with actual name
        lockType: request.lockType || LockType.PESSIMISTIC,
        acquiredAt: new Date(),
        expiresAt,
        sessionId,
        isActive: true
      };

      // Store lock in Redis with expiration
      await redis.setex(
        lockKey,
        Math.ceil(duration / 1000),
        JSON.stringify(lock)
      );

      // Store session information
      const sessionKey = this.getSessionKey(sessionId);
      await redis.setex(
        sessionKey,
        Math.ceil(duration / 1000),
        JSON.stringify({
          worksheetId,
          questionId,
          userId: user.sub,
          lockKey
        })
      );

      this.logger.log(`Lock acquired for question ${questionId} by user ${user.sub}`);

      return {
        success: true,
        lock
      };

    } catch (error) {
      this.logger.error(`Failed to acquire lock for question ${questionId}`, error);
      return {
        success: false,
        error: {
          errorCode: CollaborationErrorCode.REDIS_CONNECTION_FAILED,
          message: 'Failed to acquire lock due to system error',
          timestamp: new Date(),
          recoverable: true,
          suggestedAction: 'Try again in a moment'
        }
      };
    }
  }

  /**
   * Release a lock on a question
   */
  async releaseLock(
    worksheetId: string,
    questionId: string,
    userId: string
  ): Promise<boolean> {
    const lockKey = this.getLockKey(worksheetId, questionId);
    const redis = this.redisService.getClient('COMMON_CACHE_NAME');

    try {
      const existingLock = await this.getLock(worksheetId, questionId);
      
      if (!existingLock) {
        return true; // Lock doesn't exist, consider it released
      }

      // Verify lock ownership (unless it's an admin operation)
      if (existingLock.lockedBy !== userId) {
        throw new ForbiddenException('Cannot release lock owned by another user');
      }

      // Remove lock from Redis
      await redis.del(lockKey);

      // Remove session
      if (existingLock.sessionId) {
        const sessionKey = this.getSessionKey(existingLock.sessionId);
        await redis.del(sessionKey);
      }

      this.logger.log(`Lock released for question ${questionId} by user ${userId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to release lock for question ${questionId}`, error);
      return false;
    }
  }

  /**
   * Renew an existing lock
   */
  async renewLock(
    worksheetId: string,
    questionId: string,
    user: UserContext,
    duration?: number
  ): Promise<ILockResponse> {
    const existingLock = await this.getLock(worksheetId, questionId);
    
    if (!existingLock || !existingLock.isActive) {
      return {
        success: false,
        error: {
          errorCode: CollaborationErrorCode.LOCK_NOT_OWNED,
          message: 'No active lock found to renew',
          timestamp: new Date(),
          recoverable: false
        }
      };
    }

    if (existingLock.lockedBy !== user.sub) {
      return {
        success: false,
        error: {
          errorCode: CollaborationErrorCode.LOCK_NOT_OWNED,
          message: 'Cannot renew lock owned by another user',
          timestamp: new Date(),
          recoverable: false
        }
      };
    }

    // Extend lock duration
    const extensionDuration = duration || this.DEFAULT_LOCK_TIMEOUT;
    const newExpiresAt = new Date(Date.now() + extensionDuration);
    
    const renewedLock: IQuestionLock = {
      ...existingLock,
      expiresAt: newExpiresAt,
      lastRenewed: new Date()
    };

    const lockKey = this.getLockKey(worksheetId, questionId);
    const redis = this.redisService.getClient('COMMON_CACHE_NAME');

    try {
      await redis.setex(
        lockKey,
        Math.ceil(extensionDuration / 1000),
        JSON.stringify(renewedLock)
      );

      this.logger.log(`Lock renewed for question ${questionId} by user ${user.sub}`);

      return {
        success: true,
        lock: renewedLock
      };

    } catch (error) {
      this.logger.error(`Failed to renew lock for question ${questionId}`, error);
      return {
        success: false,
        error: {
          errorCode: CollaborationErrorCode.REDIS_CONNECTION_FAILED,
          message: 'Failed to renew lock due to system error',
          timestamp: new Date(),
          recoverable: true
        }
      };
    }
  }

  /**
   * Get current lock information for a question
   */
  async getLock(worksheetId: string, questionId: string): Promise<IQuestionLock | null> {
    const lockKey = this.getLockKey(worksheetId, questionId);
    const redis = this.redisService.getClient('COMMON_CACHE_NAME');

    try {
      const lockData = await redis.get(lockKey);
      
      if (!lockData) {
        return null;
      }

      const lock: IQuestionLock = JSON.parse(lockData);
      
      // Check if lock has expired
      if (new Date() > new Date(lock.expiresAt)) {
        // Clean up expired lock
        await redis.del(lockKey);
        return null;
      }

      return lock;

    } catch (error) {
      this.logger.error(`Failed to get lock for question ${questionId}`, error);
      return null;
    }
  }

  /**
   * Get all locks for a worksheet
   */
  async getWorksheetLocks(worksheetId: string): Promise<IQuestionLock[]> {
    const pattern = `${this.LOCK_PREFIX}:${worksheetId}:*`;
    const redis = this.redisService.getClient('COMMON_CACHE_NAME');

    try {
      const keys = await redis.keys(pattern);
      const locks: IQuestionLock[] = [];

      for (const key of keys) {
        const lockData = await redis.get(key);
        if (lockData) {
          const lock: IQuestionLock = JSON.parse(lockData);
          
          // Check if lock is still active
          if (new Date() <= new Date(lock.expiresAt)) {
            locks.push(lock);
          } else {
            // Clean up expired lock
            await redis.del(key);
          }
        }
      }

      return locks;

    } catch (error) {
      this.logger.error(`Failed to get worksheet locks for ${worksheetId}`, error);
      return [];
    }
  }

  /**
   * Check if a user can edit a question (no conflicting locks)
   */
  async canEditQuestion(
    worksheetId: string,
    questionId: string,
    userId: string
  ): Promise<boolean> {
    const lock = await this.getLock(worksheetId, questionId);
    
    if (!lock || !lock.isActive) {
      return true; // No lock, can edit
    }

    return lock.lockedBy === userId; // Can edit if user owns the lock
  }

  /**
   * Clean up expired locks for a worksheet
   */
  async cleanupExpiredLocks(worksheetId: string): Promise<number> {
    const locks = await this.getWorksheetLocks(worksheetId);
    let cleanedCount = 0;

    for (const lock of locks) {
      if (new Date() > new Date(lock.expiresAt)) {
        await this.releaseLock(worksheetId, lock.questionId, lock.lockedBy);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.log(`Cleaned up ${cleanedCount} expired locks for worksheet ${worksheetId}`);
    }

    return cleanedCount;
  }

  /**
   * Force release all locks for a user (when they disconnect)
   */
  async releaseUserLocks(userId: string): Promise<number> {
    const pattern = `${this.SESSION_PREFIX}:*`;
    const redis = this.redisService.getClient('COMMON_CACHE_NAME');
    let releasedCount = 0;

    try {
      const sessionKeys = await redis.keys(pattern);

      for (const sessionKey of sessionKeys) {
        const sessionData = await redis.get(sessionKey);
        if (sessionData) {
          const session = JSON.parse(sessionData);
          if (session.userId === userId) {
            await this.releaseLock(session.worksheetId, session.questionId, userId);
            await redis.del(sessionKey);
            releasedCount++;
          }
        }
      }

      if (releasedCount > 0) {
        this.logger.log(`Released ${releasedCount} locks for disconnected user ${userId}`);
      }

      return releasedCount;

    } catch (error) {
      this.logger.error(`Failed to release locks for user ${userId}`, error);
      return 0;
    }
  }

  /**
   * Generate Redis key for question lock
   */
  private getLockKey(worksheetId: string, questionId: string): string {
    return `${this.LOCK_PREFIX}:${worksheetId}:${questionId}`;
  }

  /**
   * Generate Redis key for session
   */
  private getSessionKey(sessionId: string): string {
    return `${this.SESSION_PREFIX}:${sessionId}`;
  }
}
