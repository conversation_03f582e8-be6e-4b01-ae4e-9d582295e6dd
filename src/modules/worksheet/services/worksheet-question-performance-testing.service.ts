import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { WorksheetQuestionMemoryOptimizationService } from './worksheet-question-memory-optimization.service';
import { WorksheetRedisCacheService } from './worksheet-redis-cache.service';
import { IExerciseQuestion, EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';

export interface PerformanceTestConfig {
  testName: string;
  description: string;
  iterations: number;
  concurrency: number;
  warmupIterations?: number;
  timeout?: number;
  dataSize?: 'small' | 'medium' | 'large' | 'xlarge';
  cacheEnabled?: boolean;
  backgroundProcessing?: boolean;
}

export interface PerformanceTestResult {
  testName: string;
  config: PerformanceTestConfig;
  results: {
    totalDuration: number;
    averageResponseTime: number;
    minResponseTime: number;
    maxResponseTime: number;
    p50ResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    throughput: number;
    successRate: number;
    errorRate: number;
    memoryUsage: {
      before: any;
      after: any;
      peak: any;
    };
    cacheMetrics?: {
      hitRate: number;
      missRate: number;
      operations: number;
    };
  };
  errors: string[];
  recommendations: string[];
  timestamp: string;
}

export interface LoadTestScenario {
  name: string;
  description: string;
  userLoad: number;
  duration: number;
  rampUpTime: number;
  operations: Array<{
    type: 'add' | 'update' | 'delete' | 'get' | 'reorder';
    weight: number;
    dataSize: number;
  }>;
}

/**
 * Comprehensive performance testing service for worksheet question operations
 * Provides load testing, benchmarking, and performance regression testing
 */
@Injectable()
export class WorksheetQuestionPerformanceTestingService {
  private readonly logger = new Logger(WorksheetQuestionPerformanceTestingService.name);

  // Test data configurations
  private readonly TEST_DATA_SIZES = {
    small: { questions: 10, worksheets: 5 },
    medium: { questions: 100, worksheets: 20 },
    large: { questions: 500, worksheets: 50 },
    xlarge: { questions: 2000, worksheets: 100 }
  };

  // Performance thresholds
  private readonly PERFORMANCE_THRESHOLDS = {
    responseTime: {
      excellent: 100, // ms
      good: 500,
      acceptable: 1000,
      poor: 2000
    },
    throughput: {
      minimum: 10, // requests per second
      good: 50,
      excellent: 100
    },
    memoryUsage: {
      warning: 100, // MB
      critical: 200
    },
    cacheHitRate: {
      minimum: 70, // percentage
      good: 85,
      excellent: 95
    }
  };

  constructor(
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    private readonly metricsService: WorksheetQuestionMetricsService,
    private readonly memoryOptimizationService: WorksheetQuestionMemoryOptimizationService,
    private readonly redisCacheService: WorksheetRedisCacheService,
    private readonly configService: ConfigService
  ) {}

  /**
   * Run a comprehensive performance test suite
   */
  async runPerformanceTestSuite(): Promise<PerformanceTestResult[]> {
    this.logger.log('Starting comprehensive performance test suite...');

    const testConfigs: PerformanceTestConfig[] = [
      {
        testName: 'Basic Question Retrieval',
        description: 'Test basic question retrieval performance',
        iterations: 100,
        concurrency: 10,
        warmupIterations: 10,
        dataSize: 'medium',
        cacheEnabled: true
      },
      {
        testName: 'Bulk Question Operations',
        description: 'Test bulk add/update/delete operations',
        iterations: 50,
        concurrency: 5,
        dataSize: 'large',
        backgroundProcessing: true
      },
      {
        testName: 'Memory Optimization',
        description: 'Test memory-optimized pagination and streaming',
        iterations: 20,
        concurrency: 3,
        dataSize: 'xlarge'
      },
      {
        testName: 'Cache Performance',
        description: 'Test cache hit rates and performance',
        iterations: 200,
        concurrency: 20,
        dataSize: 'medium',
        cacheEnabled: true
      },
      {
        testName: 'High Concurrency',
        description: 'Test performance under high concurrent load',
        iterations: 500,
        concurrency: 50,
        dataSize: 'medium',
        timeout: 30000
      }
    ];

    const results: PerformanceTestResult[] = [];

    for (const config of testConfigs) {
      try {
        this.logger.log(`Running test: ${config.testName}`);
        const result = await this.runPerformanceTest(config);
        results.push(result);
        
        // Wait between tests to allow system to stabilize
        await this.delay(2000);
      } catch (error) {
        this.logger.error(`Test ${config.testName} failed: ${error.message}`);
        results.push({
          testName: config.testName,
          config,
          results: {
            totalDuration: 0,
            averageResponseTime: 0,
            minResponseTime: 0,
            maxResponseTime: 0,
            p50ResponseTime: 0,
            p95ResponseTime: 0,
            p99ResponseTime: 0,
            throughput: 0,
            successRate: 0,
            errorRate: 100,
            memoryUsage: { before: {}, after: {}, peak: {} }
          },
          errors: [error.message],
          recommendations: ['Fix the underlying error before retesting'],
          timestamp: new Date().toISOString()
        });
      }
    }

    this.logger.log('Performance test suite completed');
    return results;
  }

  /**
   * Run a single performance test
   */
  async runPerformanceTest(config: PerformanceTestConfig): Promise<PerformanceTestResult> {
    const startTime = Date.now();
    const memoryBefore = process.memoryUsage();
    let peakMemory = memoryBefore;
    
    const responseTimes: number[] = [];
    const errors: string[] = [];
    let successCount = 0;

    // Setup test data
    const testData = await this.setupTestData(config.dataSize || 'medium');

    // Warmup iterations
    if (config.warmupIterations) {
      await this.runWarmupIterations(config.warmupIterations, testData);
    }

    // Run actual test iterations
    const promises: Promise<void>[] = [];
    const batchSize = Math.ceil(config.iterations / config.concurrency);

    for (let batch = 0; batch < config.concurrency; batch++) {
      const batchPromise = this.runTestBatch(
        batchSize,
        testData,
        config,
        responseTimes,
        errors,
        () => successCount++,
        () => {
          const currentMemory = process.memoryUsage();
          if (currentMemory.heapUsed > peakMemory.heapUsed) {
            peakMemory = currentMemory;
          }
        }
      );
      promises.push(batchPromise);
    }

    await Promise.all(promises);

    const totalDuration = Date.now() - startTime;
    const memoryAfter = process.memoryUsage();

    // Calculate statistics
    const stats = this.calculateStatistics(responseTimes, totalDuration, successCount, config.iterations);

    // Get cache metrics if cache is enabled
    let cacheMetrics;
    if (config.cacheEnabled) {
      cacheMetrics = await this.getCacheMetrics();
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(stats, config, cacheMetrics);

    // Cleanup test data
    await this.cleanupTestData(testData);

    return {
      testName: config.testName,
      config,
      results: {
        ...stats,
        memoryUsage: {
          before: memoryBefore,
          after: memoryAfter,
          peak: peakMemory
        },
        cacheMetrics
      },
      errors,
      recommendations,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Run load testing scenarios
   */
  async runLoadTestScenarios(): Promise<any[]> {
    const scenarios: LoadTestScenario[] = [
      {
        name: 'Normal Load',
        description: 'Typical user load during normal hours',
        userLoad: 50,
        duration: 300000, // 5 minutes
        rampUpTime: 60000, // 1 minute
        operations: [
          { type: 'get', weight: 60, dataSize: 20 },
          { type: 'add', weight: 20, dataSize: 5 },
          { type: 'update', weight: 15, dataSize: 3 },
          { type: 'delete', weight: 5, dataSize: 2 }
        ]
      },
      {
        name: 'Peak Load',
        description: 'High load during peak usage hours',
        userLoad: 200,
        duration: 600000, // 10 minutes
        rampUpTime: 120000, // 2 minutes
        operations: [
          { type: 'get', weight: 70, dataSize: 50 },
          { type: 'add', weight: 15, dataSize: 10 },
          { type: 'update', weight: 10, dataSize: 8 },
          { type: 'delete', weight: 5, dataSize: 3 }
        ]
      },
      {
        name: 'Stress Test',
        description: 'Extreme load to test system limits',
        userLoad: 500,
        duration: 300000, // 5 minutes
        rampUpTime: 60000, // 1 minute
        operations: [
          { type: 'get', weight: 80, dataSize: 100 },
          { type: 'add', weight: 10, dataSize: 20 },
          { type: 'update', weight: 8, dataSize: 15 },
          { type: 'delete', weight: 2, dataSize: 5 }
        ]
      }
    ];

    const results: any[] = [];
    for (const scenario of scenarios) {
      this.logger.log(`Running load test scenario: ${scenario.name}`);
      const result = await this.runLoadTestScenario(scenario);
      results.push(result);
    }

    return results;
  }

  /**
   * Generate performance report
   */
  generatePerformanceReport(testResults: PerformanceTestResult[]): any {
    const summary = {
      totalTests: testResults.length,
      passedTests: testResults.filter(r => r.results.successRate >= 95).length,
      failedTests: testResults.filter(r => r.results.successRate < 95).length,
      averageResponseTime: this.calculateAverage(testResults.map(r => r.results.averageResponseTime)),
      averageThroughput: this.calculateAverage(testResults.map(r => r.results.throughput)),
      overallRecommendations: this.generateOverallRecommendations(testResults)
    };

    const detailedResults = testResults.map(result => ({
      testName: result.testName,
      status: result.results.successRate >= 95 ? 'PASS' : 'FAIL',
      performance: this.categorizePerformance(result.results.averageResponseTime),
      keyMetrics: {
        responseTime: `${result.results.averageResponseTime.toFixed(2)}ms`,
        throughput: `${result.results.throughput.toFixed(2)} req/s`,
        successRate: `${result.results.successRate.toFixed(1)}%`,
        memoryUsage: `${Math.round(result.results.memoryUsage.peak.heapUsed / 1024 / 1024)}MB`
      },
      recommendations: result.recommendations
    }));

    return {
      summary,
      detailedResults,
      timestamp: new Date().toISOString(),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };
  }

  /**
   * Setup test data based on size configuration
   */
  private async setupTestData(size: string): Promise<any> {
    const config = this.TEST_DATA_SIZES[size];
    const testWorksheetIds: string[] = [];

    // Create test worksheets with questions
    for (let i = 0; i < config.worksheets; i++) {
      const worksheetId = `test_worksheet_${Date.now()}_${i}`;
      const questions: IExerciseQuestion[] = [];

      for (let j = 0; j < config.questions; j++) {
        questions.push({
          id: `test_question_${j}`,
          type: EQuestionType.MULTIPLE_CHOICE,
          content: `Test question ${j} content`,
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'Test explanation',
          position: j + 1,
          difficulty: EQuestionDifficulty.MEDIUM
        });
      }

      await this.worksheetQuestionModel.create({
        worksheetId,
        questions,
        totalQuestions: questions.length,
        schoolId: 'test_school',
        lastModifiedBy: 'test_user',
        lastModifiedAt: new Date()
      });

      testWorksheetIds.push(worksheetId);
    }

    return { worksheetIds: testWorksheetIds, questionCount: config.questions };
  }

  /**
   * Cleanup test data
   */
  private async cleanupTestData(testData: any): Promise<void> {
    await this.worksheetQuestionModel.deleteMany({
      worksheetId: { $in: testData.worksheetIds }
    });
  }

  /**
   * Run warmup iterations
   */
  private async runWarmupIterations(iterations: number, testData: any): Promise<void> {
    for (let i = 0; i < iterations; i++) {
      const worksheetId = testData.worksheetIds[i % testData.worksheetIds.length];
      await this.worksheetQuestionModel.findOne({ worksheetId });
    }
  }

  /**
   * Run a batch of test iterations
   */
  private async runTestBatch(
    batchSize: number,
    testData: any,
    config: PerformanceTestConfig,
    responseTimes: number[],
    errors: string[],
    onSuccess: () => void,
    onMemoryCheck: () => void
  ): Promise<void> {
    for (let i = 0; i < batchSize; i++) {
      try {
        const startTime = Date.now();
        
        // Simulate different operations based on test type
        await this.simulateOperation(testData, config);
        
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
        onSuccess();
        onMemoryCheck();
      } catch (error) {
        errors.push(error.message);
      }
    }
  }

  /**
   * Simulate different worksheet operations
   */
  private async simulateOperation(testData: any, config: PerformanceTestConfig): Promise<void> {
    const worksheetId = testData.worksheetIds[Math.floor(Math.random() * testData.worksheetIds.length)];
    
    switch (config.testName) {
      case 'Basic Question Retrieval':
        await this.worksheetQuestionModel.findOne({ worksheetId });
        break;
      case 'Memory Optimization':
        await this.memoryOptimizationService.getPaginatedQuestions(worksheetId, {
          page: 1,
          limit: 20
        });
        break;
      case 'Cache Performance':
        if (config.cacheEnabled) {
          await this.redisCacheService.getWorksheetSummary(worksheetId);
        }
        break;
      default:
        await this.worksheetQuestionModel.findOne({ worksheetId });
    }
  }

  /**
   * Calculate performance statistics
   */
  private calculateStatistics(responseTimes: number[], totalDuration: number, successCount: number, totalIterations: number): any {
    if (responseTimes.length === 0) {
      return {
        totalDuration,
        averageResponseTime: 0,
        minResponseTime: 0,
        maxResponseTime: 0,
        p50ResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        throughput: 0,
        successRate: 0,
        errorRate: 100
      };
    }

    const sortedTimes = responseTimes.sort((a, b) => a - b);
    const successRate = (successCount / totalIterations) * 100;

    return {
      totalDuration,
      averageResponseTime: this.calculateAverage(responseTimes),
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      p50ResponseTime: this.calculatePercentile(sortedTimes, 50),
      p95ResponseTime: this.calculatePercentile(sortedTimes, 95),
      p99ResponseTime: this.calculatePercentile(sortedTimes, 99),
      throughput: (successCount / totalDuration) * 1000, // requests per second
      successRate,
      errorRate: 100 - successRate
    };
  }

  /**
   * Helper methods
   */
  private calculateAverage(numbers: number[]): number {
    return numbers.length > 0 ? numbers.reduce((sum, num) => sum + num, 0) / numbers.length : 0;
  }

  private calculatePercentile(sortedNumbers: number[], percentile: number): number {
    const index = Math.ceil((percentile / 100) * sortedNumbers.length) - 1;
    return sortedNumbers[Math.max(0, index)] || 0;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async getCacheMetrics(): Promise<any> {
    // This would get actual cache metrics from the cache service
    return {
      hitRate: 85,
      missRate: 15,
      operations: 1000
    };
  }

  private generateRecommendations(stats: any, config: PerformanceTestConfig, cacheMetrics?: any): string[] {
    const recommendations: string[] = [];

    if (stats.averageResponseTime > this.PERFORMANCE_THRESHOLDS.responseTime.poor) {
      recommendations.push('Response time is poor. Consider optimizing database queries and adding more caching.');
    }

    if (stats.throughput < this.PERFORMANCE_THRESHOLDS.throughput.minimum) {
      recommendations.push('Throughput is below minimum threshold. Consider scaling horizontally or optimizing bottlenecks.');
    }

    if (cacheMetrics && cacheMetrics.hitRate < this.PERFORMANCE_THRESHOLDS.cacheHitRate.minimum) {
      recommendations.push('Cache hit rate is low. Review cache warming strategies and TTL configurations.');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance is within acceptable thresholds.');
    }

    return recommendations;
  }

  private generateOverallRecommendations(testResults: PerformanceTestResult[]): string[] {
    const recommendations: string[] = [];
    const avgResponseTime = this.calculateAverage(testResults.map(r => r.results.averageResponseTime));
    
    if (avgResponseTime > this.PERFORMANCE_THRESHOLDS.responseTime.good) {
      recommendations.push('Overall response times could be improved with additional optimizations.');
    }

    return recommendations;
  }

  private categorizePerformance(responseTime: number): string {
    if (responseTime <= this.PERFORMANCE_THRESHOLDS.responseTime.excellent) return 'EXCELLENT';
    if (responseTime <= this.PERFORMANCE_THRESHOLDS.responseTime.good) return 'GOOD';
    if (responseTime <= this.PERFORMANCE_THRESHOLDS.responseTime.acceptable) return 'ACCEPTABLE';
    return 'POOR';
  }

  private async runLoadTestScenario(scenario: LoadTestScenario): Promise<any> {
    // This would implement the actual load testing logic
    // For now, return a placeholder result
    return {
      scenarioName: scenario.name,
      status: 'COMPLETED',
      metrics: {
        averageResponseTime: 150,
        throughput: 45,
        errorRate: 2.1
      },
      timestamp: new Date().toISOString()
    };
  }
}
