import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  ConflictException,
  Logger
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectModel } from '@nestjs/mongoose';
import { Repository } from 'typeorm';
import { Model } from 'mongoose';

import { v4 as uuidv4 } from 'uuid';

import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetPromptResult } from '../../mongodb/schemas/worksheet-prompt-result.schema';
import { CreateExerciseQuestionDto } from '../dto/exercise-question.dto';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto, BulkReorderQuestionsDto, ReorderQuestionDto, AutoFillQuestionsDto } from '../dto/worksheet-question.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';
import { EUserRole } from '../../user/dto/create-user.dto';
import { WorksheetQuestionAuditService } from './worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { QuestionPoolService } from '../../question-pool/question-pool.service';
import { QuestionPool } from '../../mongodb/schemas/question-pool.schema';
import { UserQuestionHistoryService } from '../../question-pool/services/user-question-history.service';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { WorksheetQuestionLockingService } from './worksheet-question-locking.service';
import { CollaborationEvent } from '../enums/collaboration-events.enum';
import { MonitorDbPerformance, MonitorConnectionPool } from '../decorators/db-performance.decorator';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { WorksheetQuestionEnhancedCacheService } from './worksheet-question-enhanced-cache.service';
import { ExerciseQuestionItem } from '../../prompt/interfaces/exercise-result.interface';

export interface UserContext {
  sub: string;
  email: string;
  role: EUserRole;
  schoolId?: string | null;
}

@Injectable()
export class WorksheetQuestionService {
  private readonly logger = new Logger(WorksheetQuestionService.name);

  constructor(
    @InjectRepository(Worksheet)
    private readonly worksheetRepository: Repository<Worksheet>,
    @InjectModel(WorksheetQuestionDocument.name)
    private readonly worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    @InjectModel(WorksheetPromptResult.name)
    private readonly worksheetPromptResultModel: Model<WorksheetPromptResult>,
    private readonly auditService: WorksheetQuestionAuditService,
    private readonly socketGateway: SocketGateway,
    private readonly collaborationGateway: WorksheetQuestionCollaborationGateway,
    private readonly lockingService: WorksheetQuestionLockingService,
    private readonly worksheetQuestionMetricsService: WorksheetQuestionMetricsService,
    private readonly enhancedCacheService: WorksheetQuestionEnhancedCacheService,
    private readonly questionPoolService: QuestionPoolService,
    private readonly userQuestionHistoryService: UserQuestionHistoryService,
  ) {}

  // ============================================================================
  // HELPER METHODS FOR WORKSHEETPROMPTRESULT OPERATIONS
  // ============================================================================

  /**
   * Find a question by ID in the WorksheetPromptResult document
   */
  private async findQuestionInPromptResult(
    worksheetId: string,
    questionId: string
  ): Promise<{ promptResult: WorksheetPromptResult; question: ExerciseQuestionItem; index: number } | null> {
    this.logger.debug(`Searching for question ${questionId} in worksheet ${worksheetId}`);

    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult) {
      this.logger.debug(`WorksheetPromptResult document not found for worksheet ${worksheetId}`);
      return null;
    }

    if (!promptResult.promptResult?.result) {
      this.logger.debug(`No questions array found in WorksheetPromptResult for worksheet ${worksheetId}`);
      return null;
    }

    // Log available question IDs for debugging
    const availableIds = promptResult.promptResult.result.map((q: any) => q.id).filter(Boolean);

    this.logger.debug('Available question IDs:', availableIds);

    const questionIndex = promptResult.promptResult.result.findIndex(
      (q: any) => q.id === questionId
    );

    if (questionIndex === -1) {
      this.logger.debug(`Question ${questionId} not found in available IDs: [${availableIds.join(', ')}]`);
      return null;
    }

    this.logger.debug(`Found question ${questionId} at index ${questionIndex}`);
    return {
      promptResult,
      question: promptResult.promptResult.result[questionIndex],
      index: questionIndex
    };
  }

  /**
   * Add a question to the WorksheetPromptResult document using atomic operations
   */
  private async addQuestionToPromptResult(
    worksheetId: string,
    questionData: ExerciseQuestionItem,
    position?: number
  ): Promise<void> {
    try {
      // First, ensure the document exists and has the proper structure
      // Note: createdAt and updatedAt are automatically managed by Mongoose timestamps
      await this.worksheetPromptResultModel.updateOne(
        { worksheetId: worksheetId },
        {
          $setOnInsert: {
            worksheetId: worksheetId,
            promptResult: { result: [] },
            currentQuestionCount: 0,
            totalQuestionCount: 0
          }
        },
        { upsert: true }
      );

      // Get current document to determine position and count
      const currentDoc = await this.worksheetPromptResultModel.findOne({
        worksheetId: worksheetId
      });

      if (!currentDoc) {
        throw new NotFoundException(`WorksheetPromptResult not found for worksheet ${worksheetId} after upsert`);
      }

      const currentQuestions = currentDoc.promptResult?.result || [];
      const targetPosition = position !== undefined && position >= 0 && position <= currentQuestions.length
        ? position
        : currentQuestions.length;

      // Create new questions array with the question inserted at the correct position
      const newQuestions = [...currentQuestions];
      newQuestions.splice(targetPosition, 0, questionData);

      // Perform atomic update with the new array and counts
      // Note: updatedAt is automatically managed by Mongoose timestamps
      const updateResult = await this.worksheetPromptResultModel.updateOne(
        { worksheetId: worksheetId },
        {
          $set: {
            'promptResult.result': newQuestions,
            currentQuestionCount: newQuestions.length,
            totalQuestionCount: newQuestions.length
          }
        }
      );

      if (updateResult.matchedCount === 0) {
        throw new Error(`Failed to update WorksheetPromptResult for worksheet ${worksheetId} - document not found`);
      }

      if (updateResult.modifiedCount === 0) {
        this.logger.warn(`No modifications made to WorksheetPromptResult for worksheet ${worksheetId} - possible race condition`);
      }

      this.logger.log(`📝 Atomically saved question to MongoDB. New count: ${newQuestions.length}`);
      this.logger.log(`✅ Successfully saved question to MongoDB for worksheet ${worksheetId}`);

    } catch (error) {
      this.logger.error(`Failed to add question to WorksheetPromptResult for worksheet ${worksheetId}:`, error);
      throw error;
    }
  }

  /**
   * Update a question in the WorksheetPromptResult document
   */
  private async updateQuestionInPromptResult(
    worksheetId: string,
    questionId: string,
    updates: Partial<ExerciseQuestionItem>
  ): Promise<ExerciseQuestionItem | null> {
    const result = await this.findQuestionInPromptResult(worksheetId, questionId);
    if (!result) {
      return null;
    }

    const { promptResult, index } = result;

    // Apply updates to the question
    Object.assign(promptResult.promptResult.result[index], updates);

    // Update timestamp
    promptResult.updatedAt = new Date();

    await promptResult.save();

    return promptResult.promptResult.result[index];
  }

  /**
   * Remove a question from the WorksheetPromptResult document using atomic operations
   */
  private async removeQuestionFromPromptResult(
    worksheetId: string,
    questionId: string
  ): Promise<ExerciseQuestionItem | null> {
    this.logger.debug(`Attempting to remove question ${questionId} from MongoDB for worksheet ${worksheetId}`);

    try {
      // First, get the current document to find the question and its index
      const promptResult = await this.worksheetPromptResultModel.findOne({
        worksheetId: worksheetId
      });

      if (!promptResult) {
        this.logger.error(`WorksheetPromptResult document not found for worksheet ${worksheetId}`);
        throw new NotFoundException(`WorksheetPromptResult not found for worksheet ${worksheetId}`);
      }

      if (!promptResult.promptResult?.result) {
        this.logger.error(`No questions array found in WorksheetPromptResult for worksheet ${worksheetId}`);
        throw new NotFoundException(`No questions found in WorksheetPromptResult for worksheet ${worksheetId}`);
      }

      // Log all question IDs for debugging
      const existingQuestionIds = promptResult.promptResult.result.map((q: any) => q.id).filter(Boolean);
      this.logger.debug(`Existing question IDs in MongoDB: [${existingQuestionIds.join(', ')}]`);
      this.logger.debug(`Looking for question ID: ${questionId} (type: ${typeof questionId})`);

      const questionIndex = promptResult.promptResult.result.findIndex(
        (q: any) => q.id === questionId
      );

      if (questionIndex === -1) {
        this.logger.error(`Question ${questionId} not found in MongoDB. Available IDs: [${existingQuestionIds.join(', ')}]`);
        throw new NotFoundException(`Question ${questionId} not found in WorksheetPromptResult for worksheet ${worksheetId}`);
      }

      const questionToRemove = promptResult.promptResult.result[questionIndex];
      this.logger.debug(`Found question at index ${questionIndex}, removing from MongoDB`);

      // Create new questions array without the target question
      const newQuestions = promptResult.promptResult.result.filter((q: any) => q.id !== questionId);

      // Perform atomic update with the new array and counts
      // Note: updatedAt is automatically managed by Mongoose timestamps
      const updateResult = await this.worksheetPromptResultModel.updateOne(
        { worksheetId: worksheetId },
        {
          $set: {
            'promptResult.result': newQuestions,
            currentQuestionCount: newQuestions.length,
            totalQuestionCount: newQuestions.length
          }
        }
      );

      if (updateResult.matchedCount === 0) {
        throw new Error(`Failed to update WorksheetPromptResult for worksheet ${worksheetId} - document not found during removal`);
      }

      if (updateResult.modifiedCount === 0) {
        this.logger.warn(`No modifications made to WorksheetPromptResult for worksheet ${worksheetId} during removal - possible race condition`);
      }

      this.logger.log(`Successfully removed question ${questionId} from MongoDB for worksheet ${worksheetId}. New count: ${newQuestions.length}`);
      return questionToRemove;

    } catch (error) {
      this.logger.error(`Failed to remove question ${questionId} from WorksheetPromptResult for worksheet ${worksheetId}:`, error);
      throw error;
    }
  }

  /**
   * Bulk remove multiple questions from the WorksheetPromptResult document using atomic operations
   */
  private async bulkRemoveQuestionsFromPromptResult(
    worksheetId: string,
    questionIds: string[]
  ): Promise<string[]> {
    this.logger.debug(`Bulk removing ${questionIds.length} questions from MongoDB for worksheet ${worksheetId}`);

    try {
      // First, get the current document to analyze what will be removed
      const promptResult = await this.worksheetPromptResultModel.findOne({
        worksheetId: worksheetId
      });

      if (!promptResult) {
        this.logger.error(`WorksheetPromptResult document not found for worksheet ${worksheetId}`);
        throw new NotFoundException(`WorksheetPromptResult not found for worksheet ${worksheetId}`);
      }

      if (!promptResult.promptResult?.result) {
        this.logger.error(`No questions array found in WorksheetPromptResult for worksheet ${worksheetId}`);
        throw new NotFoundException(`No questions found in WorksheetPromptResult for worksheet ${worksheetId}`);
      }

      const originalCount = promptResult.promptResult.result.length;
      const existingQuestionIds = promptResult.promptResult.result.map((q: any) => q.id).filter(Boolean);

      this.logger.debug(`Original question count: ${originalCount}`);
      this.logger.debug(`Existing question IDs: [${existingQuestionIds.join(', ')}]`);
      this.logger.debug(`Questions to remove: [${questionIds.join(', ')}]`);

      // Filter out questions that should be removed
      const remainingQuestions = promptResult.promptResult.result.filter(
        (q: any) => !questionIds.includes(q.id)
      );

      const removedCount = originalCount - remainingQuestions.length;
      const actuallyRemoved = questionIds.filter(id => existingQuestionIds.includes(id));

      this.logger.debug(`Questions actually removed: ${removedCount} (${actuallyRemoved.join(', ')})`);

      // Perform atomic update with the new array and counts
      // Note: updatedAt is automatically managed by Mongoose timestamps
      const updateResult = await this.worksheetPromptResultModel.updateOne(
        { worksheetId: worksheetId },
        {
          $set: {
            'promptResult.result': remainingQuestions,
            currentQuestionCount: remainingQuestions.length,
            totalQuestionCount: remainingQuestions.length
          }
        }
      );

      if (updateResult.matchedCount === 0) {
        throw new Error(`Failed to update WorksheetPromptResult for worksheet ${worksheetId} - document not found during bulk removal`);
      }

      if (updateResult.modifiedCount === 0) {
        this.logger.warn(`No modifications made to WorksheetPromptResult for worksheet ${worksheetId} during bulk removal - possible race condition`);
      }

      this.logger.log(`Successfully bulk removed ${removedCount} questions from MongoDB for worksheet ${worksheetId}. New count: ${remainingQuestions.length}`);
      return actuallyRemoved;

    } catch (error) {
      this.logger.error(`Failed to bulk remove questions from WorksheetPromptResult for worksheet ${worksheetId}:`, error);
      throw error;
    }
  }

  /**
   * Utility method to ensure MongoDB question counts are always consistent
   * Call this after any direct MongoDB operations that modify the questions array
   * NOTE: With atomic operations, this should rarely find inconsistencies
   */
  private async ensureMongoDBCountsConsistent(worksheetId: string): Promise<void> {
    try {
      const promptResult = await this.worksheetPromptResultModel.findOne({
        worksheetId: worksheetId
      });

      if (!promptResult || !promptResult.promptResult?.result) {
        this.logger.warn(`Cannot ensure counts consistency - WorksheetPromptResult not found for worksheet ${worksheetId}`);
        return;
      }

      const actualQuestionCount = promptResult.promptResult.result.length;
      const currentCount = promptResult.currentQuestionCount;
      const totalCount = promptResult.totalQuestionCount;

      // Check if counts are inconsistent
      if (currentCount !== actualQuestionCount || totalCount !== actualQuestionCount) {
        this.logger.warn(`MongoDB count inconsistency detected for worksheet ${worksheetId}: actual=${actualQuestionCount}, current=${currentCount}, total=${totalCount}`);
        this.logger.warn(`This should be rare with atomic operations - investigating potential race condition`);

        // Fix the counts atomically
        // Note: updatedAt is automatically managed by Mongoose timestamps
        const updateResult = await this.worksheetPromptResultModel.updateOne(
          { worksheetId: worksheetId },
          {
            $set: {
              currentQuestionCount: actualQuestionCount,
              totalQuestionCount: actualQuestionCount
            }
          }
        );

        if (updateResult.modifiedCount > 0) {
          this.logger.log(`Fixed MongoDB count inconsistency for worksheet ${worksheetId}: updated to ${actualQuestionCount}`);
        } else {
          this.logger.warn(`Failed to fix count inconsistency for worksheet ${worksheetId} - document may have been modified concurrently`);
        }
      } else {
        this.logger.debug(`MongoDB counts are consistent for worksheet ${worksheetId}: ${actualQuestionCount} questions`);
      }
    } catch (error) {
      this.logger.error(`Error ensuring MongoDB counts consistency for worksheet ${worksheetId}:`, error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Convert ExerciseQuestionItem to IExerciseQuestion interface
   */
  private convertExerciseQuestionItemToIExerciseQuestion(
    item: ExerciseQuestionItem,
    position?: number
  ): IExerciseQuestion {
    return {
      id: item.id || item.questionId, // Use id field from promptResult.result
      position: position || 1,
      order: position || 1, // For backward compatibility
      points: item.points || 1,
      type: item.type as any,
      content: item.content,
      options: item.options || [],
      answer: item.answer || [],
      explain: item.explain || '',
      subject: item.subject,
      parentSubject: item.parentSubject,
      childSubject: item.childSubject,
      topic: item.topic,
      subtopic: item.subtopic,
      grade: item.grade,
      difficulty: item.difficulty as any,
      media: item.media,
      imagePrompt: item.imagePrompt,
      imageUrl: item.imageUrl,
      image: item.image,
      status: item.status as any,
      metadata: item.metadata,
      audit: item.audit,
      schoolId: item.schoolId
    };
  }

  /**
   * Convert IExerciseQuestion to ExerciseQuestionItem
   */
  private convertIExerciseQuestionToExerciseQuestionItem(
    question: IExerciseQuestion
  ): ExerciseQuestionItem {
    return {
      id: question.id,
      type: question.type,
      content: question.content,
      options: question.options,
      answer: question.answer,
      explain: question.explain,
      imagePrompt: question.imagePrompt,
      subject: question.subject,
      parentSubject: question.parentSubject,
      childSubject: question.childSubject,
      topic: question.topic,
      subtopic: question.subtopic,
      grade: question.grade,
      difficulty: question.difficulty,
      media: question.media,
      imageUrl: question.imageUrl,
      image: question.image,
      status: question.status,
      metadata: question.metadata,
      audit: question.audit,
      schoolId: question.schoolId,
      points: question.points
    };
  }

  // ============================================================================
  // MAIN SERVICE METHODS
  // ============================================================================

  /**
   * Add a new question to a worksheet
   */
  async addQuestionToWorksheet(
    worksheetId: string,
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Adding question to worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Check question limit
    await this.validateQuestionLimit(worksheet);

    // Step 3: Generate a UUID for the question
    const questionId = uuidv4();

    // Step 4: Create the question in WorksheetPromptResult
    this.logger.debug(`🔄 Step 4: Creating question in MongoDB...`);
    const newQuestion = await this.createQuestionInMongoDB(questionDto, user, worksheet, questionId);
    this.logger.debug(`✅ Step 4: Question created in MongoDB with ID: ${newQuestion.id}`);

    // Step 5: Update worksheet questionIds array in PostgreSQL
    this.logger.debug(`🔄 Step 5: Updating PostgreSQL questionIds array...`);
    await this.addQuestionIdToWorksheet(worksheet, questionId, user, questionDto.position);
    this.logger.debug(`✅ Step 5: PostgreSQL questionIds array updated`);

    // Step 5: Update cache with fresh data from WorksheetPromptResult
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Step 6: Emit real-time updates (both legacy and collaboration)
    const questionForResponse = newQuestion;
    await this.emitQuestionUpdate(worksheetId, 'question_added', {
      question: questionForResponse,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId
    }, user.sub);

    // Step 6b: Emit collaboration event
    await this.collaborationGateway.broadcastQuestionUpdate(
      worksheetId,
      CollaborationEvent.QUESTION_ADDED_REALTIME,
      {
        question: questionForResponse,
        totalQuestions: worksheet.questionIds?.length || 0,
        addedBy: user.sub
      },
      user.sub
    );

    // Step 7: Log audit event
    await this.auditService.logQuestionAdded(worksheetId, questionId, user);

    this.logger.log(`Successfully added question ${questionId} to worksheet ${worksheetId}`);
    return questionForResponse;
  }

  /**
   * Validate user access to worksheet and return worksheet
   */
  @MonitorDbPerformance('findWorksheet', 'worksheets')
  @MonitorConnectionPool()
  private async validateWorksheetAccess(
    worksheetId: string,
    user: UserContext
  ): Promise<Worksheet> {
    this.logger.debug('User:', user);
    const worksheet = await this.worksheetRepository.findOne({
      where: { id: worksheetId },
      relations: ['selectedOptions']
    });

    if (!worksheet) {
      throw new NotFoundException(`Worksheet with ID ${worksheetId} not found`);
    }

    // Admin has access to all worksheets
    if (user.role === EUserRole.ADMIN) {
      return worksheet;
    }

    // School-based access control
    if (user.role === EUserRole.SCHOOL_MANAGER) {
      if (!user.schoolId) {
        throw new ForbiddenException('School manager must be assigned to a school');
      }
      if (worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Cannot modify questions from different school');
      }
      return worksheet;
    }

    // Independent teacher can only modify their own worksheets
    if (user.role === EUserRole.INDEPENDENT_TEACHER) {
      if (!user.schoolId || worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Can only modify your own worksheets');
      }
      return worksheet;
    }

    // Regular teacher can modify worksheets in their school
    if (user.role === EUserRole.TEACHER) {
      if (!user.schoolId || worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Access denied to worksheet from different school');
      }
      return worksheet;
    }

    throw new ForbiddenException('Insufficient permissions to modify worksheet questions');
  }

  /**
   * Validate that adding a question won't exceed the limit
   */
  private async validateQuestionLimit(worksheet: Worksheet): Promise<void> {
    const currentQuestionCount = worksheet.questionIds?.length || 0;
    const maxQuestions = worksheet.maxQuestions || 100;

    if (currentQuestionCount >= maxQuestions) {
      throw new BadRequestException(
        `Question limit exceeded. Current: ${currentQuestionCount}, Maximum: ${maxQuestions}`
      );
    }
  }

  /**
   * Create a new question in WorksheetPromptResult
   */
  private async createQuestionInMongoDB(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    questionId: string
  ): Promise<IExerciseQuestion> {
    this.logger.debug(`🔄 createQuestionInMongoDB called for worksheet ${worksheet.id}, questionId: ${questionId}`);

    const currentQuestionCount = worksheet.questionIds?.length || 0;
    const position = questionDto.position || currentQuestionCount + 1;

    this.logger.debug(`🔄 Position calculated: ${position}, questionPoolId: ${questionDto.questionPoolId}`);

    // If questionPoolId is provided, get the question from the pool
    if (questionDto.questionPoolId) {
      this.logger.debug(`🔄 Creating question from pool with ID: ${questionDto.questionPoolId}`);
      return this.createQuestionFromPool(questionDto, user, worksheet, questionId, position);
    }

    // Otherwise, create a new question from the provided data
    this.logger.debug(`🔄 Creating question from DTO data`);
    const newQuestion = this.createQuestionFromDto(questionDto, user, worksheet, questionId, position);

    // Convert to ExerciseQuestionItem and add to WorksheetPromptResult
    const questionItem = this.convertIExerciseQuestionToExerciseQuestionItem(newQuestion);
    await this.addQuestionToPromptResult(worksheet.id, questionItem, position - 1);

    return newQuestion;
  }

  /**
   * Create a question document from the question pool
   */
  private async createQuestionDocFromPool(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    position: number,
    questionId: string
  ): Promise<WorksheetQuestionDocument> {
    // Get the question from the pool
    const poolQuestion = await this.questionPoolService.getQuestionById(questionDto.questionPoolId!);

    if (!poolQuestion) {
      throw new NotFoundException(`Question with ID ${questionDto.questionPoolId} not found in question pool`);
    }

    // Validate user access to the pool question
    this.validatePoolQuestionAccess(poolQuestion, user);

    // Create the MongoDB document (let MongoDB generate the _id automatically)
    const questionDoc = new this.worksheetQuestionModel({
      worksheetId: worksheet.id,
      questionId, // Set the UUID questionId
      position,
      points: questionDto.points || 1,

      // Copy question content from pool
      type: poolQuestion.type,
      content: poolQuestion.content,
      options: poolQuestion.options || [],
      answer: poolQuestion.answer || [],
      explain: poolQuestion.explain || '',

      // Copy subject and academic information
      subject: poolQuestion.subject,
      parentSubject: poolQuestion.parentSubject,
      childSubject: poolQuestion.childSubject,
      grade: poolQuestion.grade,
      difficulty: poolQuestion.difficultyLevel,

      // Copy media information
      image: poolQuestion.image,
      imagePrompt: poolQuestion.imagePrompt,

      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },

      // School association
      schoolId: user.schoolId || undefined,
      status: 'ACTIVE'
    });

    // Save to MongoDB
    const savedDoc = await questionDoc.save();

    // Update pool question usage statistics
    await this.updatePoolQuestionUsage(questionDto.questionPoolId!);

    return savedDoc;
  }

  /**
   * Create a question document from DTO data
   */
  private async createQuestionDocFromDto(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    position: number,
    questionId: string
  ): Promise<WorksheetQuestionDocument> {
    // Create the MongoDB document (let MongoDB generate the _id automatically)
    const questionDoc = new this.worksheetQuestionModel({
      worksheetId: worksheet.id,
      questionId, // Set the UUID questionId
      position,
      points: questionDto.points || 1,
      type: questionDto.type,
      content: questionDto.content,
      options: questionDto.options,
      answer: questionDto.answer,
      explain: questionDto.explain,
      subject: questionDto.subject,
      parentSubject: questionDto.parentSubject,
      childSubject: questionDto.childSubject,
      topic: questionDto.topic,
      subtopic: questionDto.subtopic,
      grade: questionDto.grade,
      difficulty: questionDto.difficulty,
      media: questionDto.media,
      imagePrompt: questionDto.imagePrompt,
      imageUrl: questionDto.imageUrl,
      status: questionDto.status || 'ACTIVE',
      metadata: questionDto.metadata,
      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },
      // School association
      schoolId: user.schoolId || undefined
    });

    // Save to MongoDB
    return await questionDoc.save();
  }

  /**
   * Add question ID to worksheet's questionIds array in PostgreSQL
   */
  private async addQuestionIdToWorksheet(
    worksheet: Worksheet,
    questionId: string,
    user: UserContext,
    position?: number
  ): Promise<void> {
    this.logger.log(`📝 Adding question ID ${questionId} to PostgreSQL worksheet ${worksheet.id} at position ${position || 'end'}`);

    if (!worksheet.questionIds) {
      worksheet.questionIds = [];
    }

    const beforeCount = worksheet.questionIds.length;

    // Insert at specific position or at the end
    if (position !== undefined && position > 0 && position <= worksheet.questionIds.length + 1) {
      // Convert 1-based position to 0-based index
      const insertIndex = position - 1;
      worksheet.questionIds.splice(insertIndex, 0, questionId);
      this.logger.log(`📍 Inserted question at position ${position} (index ${insertIndex})`);
    } else {
      worksheet.questionIds.push(questionId);
      this.logger.log(`📍 Added question at end (position ${worksheet.questionIds.length})`);
    }

    worksheet.totalQuestions = worksheet.questionIds.length;
    worksheet.lastModifiedBy = user.sub;

    await this.worksheetRepository.save(worksheet);

    this.logger.log(`✅ PostgreSQL updated: ${beforeCount} -> ${worksheet.questionIds.length} questions`);
  }

  /**
   * Convert MongoDB document to IExerciseQuestion interface
   */
  private convertMongoDocToIExerciseQuestion(doc: WorksheetQuestionDocument): IExerciseQuestion {
    return {
      id: doc.questionId, // Use the questionId field instead of MongoDB _id
      position: doc.position,
      order: doc.position, // For backward compatibility
      points: doc.points,
      type: doc.type,
      content: doc.content,
      options: doc.options,
      answer: doc.answer,
      explain: doc.explain,
      subject: doc.subject,
      parentSubject: doc.parentSubject,
      childSubject: doc.childSubject,
      topic: doc.topic,
      subtopic: doc.subtopic,
      grade: doc.grade,
      difficulty: doc.difficulty,
      media: doc.media,
      imagePrompt: doc.imagePrompt,
      imageUrl: doc.imageUrl,
      image: doc.image,
      status: doc.status as any, // Type conversion for status enum
      metadata: doc.metadata,
      audit: doc.audit,
      schoolId: doc.schoolId
    };
  }

  /**
   * Update cache by fetching fresh data from WorksheetPromptResult
   */
  private async updateQuestionCacheFromMongoDB(
    worksheetId: string,
    user: UserContext
  ): Promise<void> {
    try {
      // Ensure MongoDB counts are consistent before updating cache
      await this.ensureMongoDBCountsConsistent(worksheetId);

      // Fetch all questions for this worksheet using the new method
      const questions = await this.getWorksheetQuestions(worksheetId, user);

      // Update Redis cache
      await this.enhancedCacheService.cacheWorksheetQuestions(
        worksheetId,
        questions,
        user.schoolId || undefined
      );

      this.logger.debug(`Updated cache from WorksheetPromptResult for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Failed to update question cache from WorksheetPromptResult for worksheet ${worksheetId}`, error);
      // Don't throw error - cache update failure shouldn't fail the operation
    }
  }

  /**
   * Get all questions for a worksheet from WorksheetPromptResult
   */
  async getWorksheetQuestions(
    worksheetId: string,
    user: UserContext
  ): Promise<IExerciseQuestion[]> {
    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Fetch questions from WorksheetPromptResult
    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult || !promptResult.promptResult?.result) {
      return [];
    }

    // Convert to IExerciseQuestion format, maintaining order from worksheet.questionIds
    const questions: IExerciseQuestion[] = [];

    if (worksheet.questionIds && worksheet.questionIds.length > 0) {
      // Create a map for quick lookup
      const questionMap = new Map();
      promptResult.promptResult.result.forEach((question: any) => {
        if (question.id) {
          questionMap.set(question.id, question);
        }
      });

      // Return questions in the order specified by worksheet.questionIds
      worksheet.questionIds.forEach((questionId, index) => {
        const questionData = questionMap.get(questionId);
        if (questionData) {
          questions.push(this.convertExerciseQuestionItemToIExerciseQuestion(questionData, index + 1));
        }
      });
    }

    return questions;
  }

  /**
   * Get a single question from WorksheetPromptResult
   */
  async getWorksheetQuestion(
    worksheetId: string,
    questionId: string,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Verify question belongs to this worksheet
    if (!worksheet.questionIds?.includes(questionId)) {
      this.logger.error(`Question ${questionId} not found in worksheet.questionIds array for worksheet ${worksheetId}`);
      this.logger.debug(`Available questionIds: [${worksheet.questionIds?.join(', ') || 'none'}]`);
      throw new NotFoundException(`Question ${questionId} not found in worksheet ${worksheetId}`);
    }

    this.logger.debug(`Question ${questionId} found in worksheet.questionIds, now checking MongoDB...`);

    // Fetch question from WorksheetPromptResult
    const result = await this.findQuestionInPromptResult(worksheetId, questionId);
    if (!result) {
      this.logger.error(`Data consistency issue: Question ${questionId} exists in PostgreSQL questionIds but not found in MongoDB WorksheetPromptResult`);
      this.logger.debug(`This indicates a sync issue between PostgreSQL and MongoDB for worksheet ${worksheetId}`);

      // For now, throw a more descriptive error
      throw new NotFoundException(
        `Question ${questionId} found in worksheet but not in database. This indicates a data consistency issue. ` +
        `Please contact support or try regenerating the worksheet.`
      );
    }

    // Get position from worksheet.questionIds array
    const position = worksheet.questionIds.indexOf(questionId) + 1;

    return this.convertExerciseQuestionItemToIExerciseQuestion(result.question, position);
  }

  /**
   * Check if a question exists in a worksheet
   */
  async questionExistsInWorksheet(
    worksheetId: string,
    questionId: string
  ): Promise<boolean> {
    const worksheet = await this.worksheetRepository.findOne({
      where: { id: worksheetId }
    });

    if (!worksheet || !worksheet.questionIds) {
      return false;
    }

    return worksheet.questionIds.includes(questionId);
  }

  /**
   * Create a new question from DTO
   */
  private async createQuestion(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet
  ): Promise<IExerciseQuestion> {
    const questionId = uuidv4();
    const currentQuestions = worksheet.questions || [];
    const position = questionDto.position || currentQuestions.length + 1;

    // If questionPoolId is provided, get the question from the pool
    if (questionDto.questionPoolId) {
      return this.createQuestionFromPool(questionDto, user, worksheet, questionId, position);
    }

    // Otherwise, create a new question from the provided data
    return this.createQuestionFromDto(questionDto, user, worksheet, questionId, position);
  }

  /**
   * Create a question from the question pool
   */
  private async createQuestionFromPool(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    questionId: string,
    position: number
  ): Promise<IExerciseQuestion> {
    // Get the question from the pool
    const poolQuestion = await this.questionPoolService.getQuestionById(questionDto.questionPoolId!);

    if (!poolQuestion) {
      throw new NotFoundException(`Question with ID ${questionDto.questionPoolId} not found in question pool`);
    }

    // Validate user access to the pool question
    this.validatePoolQuestionAccess(poolQuestion, user);

    // Create the worksheet question based on the pool question
    const newQuestion: IExerciseQuestion = {
      id: questionId,
      position,
      points: questionDto.points || 1,

      // Copy question content from pool
      type: poolQuestion.type as any,
      content: poolQuestion.content,
      options: poolQuestion.options || [],
      answer: poolQuestion.answer || [],
      explain: poolQuestion.explain || '',

      // Copy subject and academic information
      subject: poolQuestion.subject,
      parentSubject: poolQuestion.parentSubject,
      childSubject: poolQuestion.childSubject,
      grade: poolQuestion.grade,
      difficulty: poolQuestion.difficultyLevel as any,

      // Copy media information
      image: poolQuestion.image,
      imagePrompt: poolQuestion.imagePrompt,

      // Override with any provided values from DTO
      ...(questionDto.optionTypeId && { optionTypeId: questionDto.optionTypeId }),
      ...(questionDto.optionValueId && { optionValueId: questionDto.optionValueId }),

      // Add pool reference for tracking
      questionPoolId: questionDto.questionPoolId,
      sourceType: 'question_pool',

      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },

      // School association
      schoolId: user.schoolId || undefined
    };

    // Convert to ExerciseQuestionItem and add to WorksheetPromptResult
    this.logger.debug(`🔄 Converting question to ExerciseQuestionItem for MongoDB...`);
    const questionItem = this.convertIExerciseQuestionToExerciseQuestionItem(newQuestion);
    this.logger.debug(`🔄 Converted question item:`, { id: questionItem.id, type: questionItem.type });

    this.logger.debug(`🔄 Adding question to WorksheetPromptResult at position ${position - 1}...`);
    await this.addQuestionToPromptResult(worksheet.id, questionItem, position - 1);
    this.logger.debug(`✅ Question added to WorksheetPromptResult successfully`);

    // Update pool question usage statistics
    this.logger.debug(`🔄 Updating pool question usage statistics...`);
    await this.updatePoolQuestionUsage(questionDto.questionPoolId!);

    return newQuestion;
  }

  /**
   * Create a question from DTO data (original functionality)
   */
  private createQuestionFromDto(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet,
    questionId: string,
    position: number
  ): IExerciseQuestion {

    const newQuestion: IExerciseQuestion = {
      id: questionId,
      position,
      points: questionDto.points || 1,
      type: questionDto.type,
      content: questionDto.content,
      options: questionDto.options,
      answer: questionDto.answer,
      explain: questionDto.explain,
      subject: questionDto.subject,
      parentSubject: questionDto.parentSubject,
      childSubject: questionDto.childSubject,
      topic: questionDto.topic,
      subtopic: questionDto.subtopic,
      grade: questionDto.grade,
      difficulty: questionDto.difficulty,
      media: questionDto.media,
      imagePrompt: questionDto.imagePrompt,
      imageUrl: questionDto.imageUrl,
      status: questionDto.status, // Use the status from the base DTO
      isPublic: questionDto.isPublic,
      metadata: questionDto.metadata,
      // Database option references (if provided)
      optionTypeId: questionDto.optionTypeId,
      optionValueId: questionDto.optionValueId,
      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },
      // School association
      schoolId: user.schoolId || undefined
    };

    return newQuestion;
  }

  /**
   * Update worksheet with new question
   */
  private async updateWorksheetWithNewQuestion(
    worksheet: Worksheet,
    newQuestion: IExerciseQuestion,
    user: UserContext
  ): Promise<void> {
    if (!worksheet.questions) {
      worksheet.questions = [];
    }

    worksheet.questions.push(newQuestion);
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    await this.worksheetRepository.save(worksheet);
  }

  /**
   * Update Redis cache with provided questions
   */
  @MonitorDbPerformance('updateCache', 'worksheet_questions')
  private async updateQuestionCache(
    worksheetId: string,
    questions: IExerciseQuestion[],
    user: UserContext
  ): Promise<void> {
    try {
      // Update Redis cache only (WorksheetPromptResult is the source of truth now)
      await this.enhancedCacheService.cacheWorksheetQuestions(
        worksheetId,
        questions,
        user.schoolId || undefined
      );

      this.logger.debug(`Updated Redis cache for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Failed to update question cache for worksheet ${worksheetId}`, error);
      // Don't throw error - cache update failure shouldn't fail the operation
    }
  }

  /**
   * Remove a question from a worksheet
   */
  async removeQuestionFromWorksheet(
    worksheetId: string,
    questionId: string,
    user: UserContext
  ): Promise<void> {
    this.logger.log(`Removing question ${questionId} from worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Find and validate the question exists in the worksheet
    const questionToRemove = await this.findQuestionInWorksheet(worksheet, questionId);

    // Step 3: Validate minimum questions requirement
    await this.validateMinimumQuestions(worksheet);

    // Step 4: Remove the question from both databases
    this.logger.log(`🗑️ Removing question ${questionId} from both databases`);
    await this.removeQuestionAndReorder(worksheet, questionId, user);
    this.logger.log(`✅ Successfully removed question ${questionId}`);

    // Step 5: Update cache and invalidate Redis cache
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);
    await this.enhancedCacheService.invalidateWorksheetCache(worksheetId);

    // Step 6: Emit real-time update
    const finalWorksheet = await this.worksheetRepository.findOne({ where: { id: worksheetId } });
    await this.emitQuestionUpdate(worksheetId, 'question_removed', {
      questionId,
      totalQuestions: finalWorksheet?.questionIds?.length || 0,
      worksheetId
    }, user.sub);

    // Step 7: Log audit event
    await this.auditService.logQuestionRemoved(worksheetId, questionId, questionToRemove, user);

    this.logger.log(`Successfully processed question removal for ${questionId} from worksheet ${worksheetId}`);
  }

  /**
   * Find a question in the worksheet and return it (from WorksheetPromptResult)
   */
  private async findQuestionInWorksheet(
    worksheet: Worksheet,
    questionId: string
  ): Promise<IExerciseQuestion> {
    // Check if question exists in worksheet questionIds
    if (!worksheet.questionIds?.includes(questionId)) {
      throw new NotFoundException(
        `Question with ID ${questionId} not found in worksheet ${worksheet.id}`
      );
    }

    // Fetch from WorksheetPromptResult
    const result = await this.findQuestionInPromptResult(worksheet.id, questionId);
    if (!result) {
      throw new NotFoundException(
        `Question with ID ${questionId} not found in database`
      );
    }

    // Get position from worksheet.questionIds array
    const position = worksheet.questionIds.indexOf(questionId) + 1;
    return this.convertExerciseQuestionItemToIExerciseQuestion(result.question, position);
  }

  /**
   * Validate that removing a question won't violate minimum requirements
   */
  private async validateMinimumQuestions(worksheet: Worksheet): Promise<void> {
    const currentQuestionCount = worksheet.questionIds?.length || 0;
    const minQuestions = 1; // Minimum 1 question per worksheet

    if (currentQuestionCount <= minQuestions) {
      throw new BadRequestException(
        `Cannot remove question. Worksheet must have at least ${minQuestions} question(s). Current count: ${currentQuestionCount}`
      );
    }
  }

  /**
   * Remove question from worksheet and reorder remaining questions
   */
  private async removeQuestionAndReorder(
    worksheet: Worksheet,
    questionId: string,
    user: UserContext
  ): Promise<void> {
    if (!worksheet.questionIds || worksheet.questionIds.length === 0) {
      throw new NotFoundException('No questions found in worksheet');
    }

    // Find and remove the question ID from PostgreSQL
    const questionIndex = worksheet.questionIds.findIndex(id => id === questionId);

    if (questionIndex === -1) {
      throw new NotFoundException(`Question with ID ${questionId} not found in worksheet`);
    }

    // Remove from PostgreSQL questionIds array
    worksheet.questionIds.splice(questionIndex, 1);

    // Remove from WorksheetPromptResult
    await this.removeQuestionFromPromptResult(worksheet.id, questionId);

    // Note: Position reordering is handled automatically by the order of questionIds in PostgreSQL
    // The WorksheetPromptResult questions will be reordered when accessed via getWorksheetQuestions

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questionIds.length;
    worksheet.lastModifiedBy = user.sub;

    // Save the updated worksheet
    await this.worksheetRepository.save(worksheet);
  }













  /**
   * Update a question in a worksheet (PATCH - partial update)
   */
  async updateQuestionInWorksheet(
    worksheetId: string,
    questionId: string,
    updateDto: UpdateWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Updating question ${questionId} in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Check if user can edit this question (has lock)
    const canEdit = await this.lockingService.canEditQuestion(worksheetId, questionId, user.sub);
    if (!canEdit) {
      throw new ConflictException('Question is locked by another user. Acquire lock before editing.');
    }

    // Step 2: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 3: Get existing question from MongoDB
    const existingQuestion = await this.getWorksheetQuestion(worksheetId, questionId, user);

    // Step 4: Validate optimistic locking if version is provided
    if (updateDto.version !== undefined) {
      await this.validateQuestionVersion(existingQuestion, updateDto.version);
    }

    // Step 5: Validate the update data
    await this.validateQuestionUpdate(updateDto, questionId, worksheetId);

    // Step 6: Apply updates to WorksheetPromptResult
    const updateFields: any = {};
    if (updateDto.content !== undefined) updateFields.content = updateDto.content;
    if (updateDto.options !== undefined) updateFields.options = updateDto.options;
    if (updateDto.answer !== undefined) updateFields.answer = updateDto.answer;
    if (updateDto.explain !== undefined) updateFields.explain = updateDto.explain;
    if (updateDto.points !== undefined) updateFields.points = updateDto.points;
    if (updateDto.difficulty !== undefined) updateFields.difficulty = updateDto.difficulty;
    if (updateDto.media !== undefined) updateFields.media = updateDto.media;
    if (updateDto.type !== undefined) updateFields.type = updateDto.type;

    // Update audit information
    if (!updateFields.audit) updateFields.audit = existingQuestion.audit || {};
    updateFields.audit.updatedBy = user.sub;
    updateFields.audit.updatedAt = new Date();
    updateFields.audit.version = (existingQuestion.audit?.version || 1) + 1;

    // Add to change log
    if (!updateFields.audit.changeLog) updateFields.audit.changeLog = existingQuestion.audit?.changeLog || [];
    updateFields.audit.changeLog.push({
      timestamp: new Date(),
      userId: user.sub,
      action: 'partial_update',
      changes: updateFields,
      reason: updateDto.updateReason || 'Question updated'
    });

    const updatedQuestionItem = await this.updateQuestionInPromptResult(worksheetId, questionId, updateFields);

    if (!updatedQuestionItem) {
      throw new NotFoundException(`Question ${questionId} not found for update`);
    }

    // Get position from worksheet.questionIds array
    const position = worksheet.questionIds?.indexOf(questionId) + 1 || 1;
    const updatedQuestion = this.convertExerciseQuestionItemToIExerciseQuestion(updatedQuestionItem, position);

    // Step 7: Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;

    // Step 8: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 9: Update cache from MongoDB
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Step 10: Emit real-time updates (both legacy and collaboration)
    await this.emitQuestionUpdate(worksheetId, 'question_updated', {
      question: updatedQuestion,
      questionId,
      worksheetId,
      updateType: 'partial'
    }, user.sub);

    // Step 10b: Emit collaboration event
    await this.collaborationGateway.broadcastQuestionUpdate(
      worksheetId,
      CollaborationEvent.QUESTION_UPDATED_REALTIME,
      {
        question: updatedQuestion,
        questionId,
        updateType: 'partial',
        updatedBy: user.sub
      },
      user.sub
    );

    // Step 11: Log audit event
    await this.auditService.logQuestionUpdatedInWorksheet(worksheetId, questionId, user, updateDto);

    this.logger.log(`Successfully updated question ${questionId} in worksheet ${worksheetId}`);
    return updatedQuestion;
  }



  /**
   * Reorder questions in a worksheet (bulk operation)
   */
  async reorderQuestionsInWorksheet(
    worksheetId: string,
    reorderDto: BulkReorderQuestionsDto,
    user: UserContext
  ): Promise<{
    worksheetId: string;
    totalQuestions: number;
    reorderedQuestions: Array<{
      questionId: string;
      oldPosition: number;
      newPosition: number;
    }>;
    version: number;
  }> {
    this.logger.log(`Reordering ${reorderDto.reorders.length} questions in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Validate optimistic locking
    await this.validateWorksheetVersion(worksheet, user);

    // Step 3: Validate reorder operations
    await this.validateReorderOperations(worksheet, reorderDto.reorders);

    // Step 4: Perform the reordering
    const reorderResults = await this.performQuestionReordering(worksheet, reorderDto.reorders, user);

    // Step 5: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 6: Update cache from MongoDB
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Step 7: Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_reordered', {
      worksheetId,
      totalQuestions: worksheet.questionIds?.length || 0,
      reorderedQuestions: reorderResults,
      version: worksheet.questionMetadata?.questionVersion || 1
    }, user.sub);

    // Step 8: Log audit events
    for (const result of reorderResults) {
      await this.auditService.logQuestionMoved(
        result.questionId,
        result.oldPosition,
        result.newPosition,
        worksheetId,
        worksheetId,
        user.sub,
        { reorderOperation: true }
      );
    }

    this.logger.log(`Successfully reordered ${reorderResults.length} questions in worksheet ${worksheetId}`);

    return {
      worksheetId,
      totalQuestions: worksheet.questionIds?.length || 0,
      reorderedQuestions: reorderResults,
      version: worksheet.questionMetadata?.questionVersion || 1
    };
  }

  /**
   * Find question index in worksheet questionIds array
   */
  private async findQuestionIndex(worksheet: Worksheet, questionId: string): Promise<number> {
    if (!worksheet.questionIds || worksheet.questionIds.length === 0) {
      throw new NotFoundException('No questions found in worksheet');
    }

    const questionIndex = worksheet.questionIds.findIndex(id => id === questionId);
    if (questionIndex === -1) {
      throw new NotFoundException(`Question with ID ${questionId} not found in worksheet`);
    }

    return questionIndex;
  }

  /**
   * Validate question version for optimistic locking
   */
  private async validateQuestionVersion(
    existingQuestion: IExerciseQuestion,
    providedVersion: number
  ): Promise<void> {
    const currentVersion = existingQuestion.audit?.version || 1;

    if (currentVersion !== providedVersion) {
      throw new ConflictException(
        `Question has been modified by another user. Expected version: ${providedVersion}, Current version: ${currentVersion}. Please refresh and try again.`
      );
    }
  }

  /**
   * Validate question update data
   */
  private async validateQuestionUpdate(
    updateDto: UpdateWorksheetQuestionDto,
    questionId: string,
    worksheetId: string
  ): Promise<void> {
    // Validate answer format based on question type
    if (updateDto.type && updateDto.answer) {
      // If new type is provided, validate against the new type
      await this.validateAnswerFormat(updateDto.type, updateDto.answer);
    } else if (updateDto.answer) {
      // If only answer is provided, fetch current type from WorksheetPromptResult
      await this.validateAnswerFormatFromDB(questionId, updateDto.answer, worksheetId);
    }

    // For options validation, we need to get the type
    if (updateDto.options) {
      let questionType = updateDto.type;
      if (!questionType) {
        // Fetch type from WorksheetPromptResult
        const result = await this.findQuestionInPromptResult(worksheetId, questionId);
        questionType = result?.question?.type as any;
      }
      if (questionType) {
        await this.validateQuestionOptions(updateDto.options, questionType);
      }
    }

    // Validate image prompt if provided
    if (updateDto.imagePrompt) {
      await this.validateImagePrompt(updateDto.imagePrompt);
    }

    // Validate educational standards compliance
    await this.validateEducationalStandards(updateDto);
  }



  /**
   * Apply partial updates to existing question
   */
  private async applyQuestionUpdate(
    existingQuestion: IExerciseQuestion,
    updateDto: UpdateWorksheetQuestionDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    const updatedQuestion: IExerciseQuestion = {
      ...existingQuestion,
      ...updateDto,
      id: existingQuestion.id, // Preserve ID
      audit: {
        createdAt: existingQuestion.audit?.createdAt || new Date(),
        updatedAt: new Date(),
        createdBy: existingQuestion.audit?.createdBy || user.sub,
        updatedBy: user.sub,
        version: (existingQuestion.audit?.version || 1) + 1,
        changeLog: [
          ...(existingQuestion.audit?.changeLog || []),
          {
            timestamp: new Date(),
            userId: user.sub,
            action: 'partial_update',
            changes: updateDto,
            reason: updateDto.updateReason
          }
        ]
      },
      schoolId: user.schoolId || undefined
    };

    return updatedQuestion;
  }



  /**
   * Validate answer format based on question type (with type provided)
   */
  private async validateAnswerFormat(type: string, answers: string[]): Promise<void> {
    switch (type) {
      case 'multiple_choice':
        if (answers.length === 0) {
          throw new BadRequestException('Multiple choice questions must have at least one correct answer');
        }
        break;
      case 'true_false':
        if (answers.length !== 1 || !['true', 'false'].includes(answers[0].toLowerCase())) {
          throw new BadRequestException('True/false questions must have exactly one answer: "true" or "false"');
        }
        break;
      case 'short_answer':
      case 'long_answer':
        if (answers.length === 0) {
          throw new BadRequestException('Answer questions must have at least one acceptable answer');
        }
        break;
      default:
        if (answers.length === 0) {
          throw new BadRequestException('Questions must have at least one correct answer');
        }
    }
  }

  /**
   * Validate answer format by fetching question type from WorksheetPromptResult
   */
  private async validateAnswerFormatFromDB(questionId: string, answers: string[], worksheetId: string): Promise<void> {
    // Fetch the question from WorksheetPromptResult to get the current type
    const result = await this.findQuestionInPromptResult(worksheetId, questionId);
    if (!result) {
      throw new NotFoundException(`Question with ID ${questionId} not found in database`);
    }

    // Use the type from WorksheetPromptResult
    await this.validateAnswerFormat(result.question.type, answers);
  }

  /**
   * Validate question options
   */
  private async validateQuestionOptions(options: string[], type: string): Promise<void> {
    if (type === 'multiple_choice' && options.length < 2) {
      throw new BadRequestException('Multiple choice questions must have at least 2 options');
    }

    if (type === 'true_false' && options.length !== 2) {
      throw new BadRequestException('True/false questions must have exactly 2 options');
    }

    // Check for duplicate options
    const uniqueOptions = new Set(options.map(opt => opt.trim().toLowerCase()));
    if (uniqueOptions.size !== options.length) {
      throw new BadRequestException('Question options must be unique');
    }
  }

  /**
   * Validate image prompt
   */
  private async validateImagePrompt(imagePrompt: string): Promise<void> {
    // Check for measurement requirements
    const measurementKeywords = ['cm', 'mm', 'inch', 'meter', 'feet', 'dimension', 'size'];
    const hasSpecificMeasurements = measurementKeywords.some(keyword =>
      imagePrompt.toLowerCase().includes(keyword)
    );

    if (hasSpecificMeasurements) {
      // Validate that specific dimensions are provided
      const dimensionPattern = /\d+\s*(cm|mm|inch|meter|feet|px)/i;
      if (!dimensionPattern.test(imagePrompt)) {
        throw new BadRequestException(
          'Image prompts with measurement requirements must include specific dimensions (e.g., "10cm", "5 inches")'
        );
      }
    }

    // Check prompt length
    if (imagePrompt.length > 500) {
      throw new BadRequestException('Image prompt must be 500 characters or less');
    }
  }

  /**
   * Validate educational standards compliance
   */
  private async validateEducationalStandards(questionData: any): Promise<void> {
    // Validate grade level appropriateness
    if (questionData.grade) {
      const validGrades = ['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];
      if (!validGrades.includes(questionData.grade)) {
        throw new BadRequestException(`Invalid grade level: ${questionData.grade}`);
      }
    }

    // Validate subject alignment
    if (questionData.subject && questionData.childSubject) {
      // Ensure child subject is related to parent subject
      await this.validateSubjectHierarchy(questionData.subject, questionData.childSubject);
    }

    // Validate content appropriateness
    if (questionData.content) {
      await this.validateContentAppropriatenesss(questionData.content, questionData.grade);
    }
  }

  /**
   * Validate subject hierarchy
   */
  private async validateSubjectHierarchy(parentSubject: string, childSubject: string): Promise<void> {
    // This would typically check against a database of valid subject hierarchies
    // For now, we'll do basic validation
    const subjectMappings = {
      'Mathematics': ['Algebra', 'Geometry', 'Calculus', 'Statistics', 'Arithmetic'],
      'Science': ['Physics', 'Chemistry', 'Biology', 'Earth Science'],
      'English': ['Literature', 'Grammar', 'Writing', 'Reading Comprehension'],
      'Social Studies': ['History', 'Geography', 'Civics', 'Economics']
    };

    if (subjectMappings[parentSubject] && !subjectMappings[parentSubject].includes(childSubject)) {
      this.logger.warn(`Potential subject hierarchy mismatch: ${parentSubject} -> ${childSubject}`);
    }
  }

  /**
   * Validate content appropriateness for grade level
   */
  private async validateContentAppropriatenesss(content: string, grade?: string): Promise<void> {
    if (!grade) return;

    // Basic content complexity validation based on grade
    const gradeNum = parseInt(grade) || 0;
    const wordCount = content.split(' ').length;
    const avgWordLength = content.split(' ').reduce((sum, word) => sum + word.length, 0) / wordCount;

    // Elementary grades (K-5) should have simpler content
    if (gradeNum <= 5) {
      if (wordCount > 50) {
        this.logger.warn(`Question content may be too long for grade ${grade}: ${wordCount} words`);
      }
      if (avgWordLength > 6) {
        this.logger.warn(`Question content may use complex words for grade ${grade}: avg ${avgWordLength} chars per word`);
      }
    }
  }

  /**
   * Validate worksheet version for optimistic locking
   */
  private async validateWorksheetVersion(worksheet: Worksheet, user: UserContext): Promise<void> {
    // Check if worksheet is locked by another user
    if (worksheet.questionMetadata?.lockStatus?.isLocked) {
      const lockedBy = worksheet.questionMetadata.lockStatus.lockedBy;
      if (lockedBy && lockedBy !== user.sub) {
        throw new ConflictException(
          `Worksheet is currently being edited by another user. Please try again later.`
        );
      }
    }
  }

  /**
   * Validate reorder operations
   */
  private async validateReorderOperations(
    worksheet: Worksheet,
    reorders: ReorderQuestionDto[]
  ): Promise<void> {
    console.log('validateReorderOperations', worksheet, reorders);
    if (!worksheet.questionIds || worksheet.questionIds.length === 0) {
      throw new BadRequestException('Worksheet has no questions to reorder');
    }

    const totalQuestions = worksheet.questionIds.length;
    const questionIds = worksheet.questionIds
    const newPositions = new Set<number>();

    // Validate each reorder operation
    for (const reorder of reorders) {
      // Check if question exists in worksheet
      if (!questionIds.includes(reorder.questionId)) {
        console.log('Question not found in worksheet', reorder.questionId, questionIds);
        throw new NotFoundException(`Question ${reorder.questionId} not found in worksheet`);
      }

      // Check if new position is valid
      if (reorder.newPosition < 1 || reorder.newPosition > totalQuestions) {
        throw new BadRequestException(
          `Invalid position ${reorder.newPosition}. Must be between 1 and ${totalQuestions}`
        );
      }

      // Check for duplicate positions
      if (newPositions.has(reorder.newPosition)) {
        throw new BadRequestException(
          `Duplicate position ${reorder.newPosition} found in reorder operations`
        );
      }
      newPositions.add(reorder.newPosition);
    }

    // The reordering logic will automatically handle position shifting,
    // so we don't need to validate position gaps here
  }

  /**
   * Perform the actual question reordering
   */
  private async performQuestionReordering(
    worksheet: Worksheet,
    reorders: ReorderQuestionDto[],
    user: UserContext
  ): Promise<Array<{
    questionId: string;
    oldPosition: number;
    newPosition: number;
  }>> {
    const results: Array<{
      questionId: string;
      oldPosition: number;
      newPosition: number;
    }> = [];

    if (!worksheet.questionIds || worksheet.questionIds.length === 0) {
      throw new BadRequestException('No questions found in worksheet');
    }

    // Track old positions before reordering
    const oldPositions = new Map<string, number>();
    worksheet.questionIds.forEach((questionId, index) => {
      oldPositions.set(questionId, index + 1);
    });

    // Process each reorder operation
    for (const reorder of reorders) {
      const questionId = reorder.questionId;
      const currentIndex = worksheet.questionIds.findIndex(id => id === questionId);

      if (currentIndex === -1) {
        this.logger.warn(`Question ${questionId} not found in worksheet ${worksheet.id}`);
        continue;
      }

      const oldPosition = currentIndex + 1;
      const newPosition = reorder.newPosition;

      if (oldPosition === newPosition) continue; // No change needed

      // Reorder the questionIds array in PostgreSQL
      const [movedQuestionId] = worksheet.questionIds.splice(currentIndex, 1);
      worksheet.questionIds.splice(newPosition - 1, 0, movedQuestionId);

      results.push({
        questionId,
        oldPosition,
        newPosition
      });
    }

    // Update MongoDB worksheetpromptresults to match the new PostgreSQL order
    await this.syncMongoDBQuestionOrder(worksheet, user);

    // Note: worksheet_questions collection is not used in current architecture
    // If it were used, we would update positions here, but since it's empty,
    // we skip this step to avoid unnecessary operations

    // Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;
    worksheet.questionMetadata = {
      ...worksheet.questionMetadata,
      lastQuestionUpdate: new Date(),
      questionVersion: (worksheet.questionMetadata?.questionVersion || 1) + 1,
      hasUnsavedChanges: false
    };

    return results;
  }

  /**
   * Sync MongoDB worksheetpromptresults question order to match PostgreSQL
   */
  private async syncMongoDBQuestionOrder(worksheet: Worksheet, user: UserContext): Promise<void> {
    try {
      // Get the current worksheetpromptresults document
      const promptResult = await this.worksheetPromptResultModel.findOne({
        worksheetId: worksheet.id
      });

      if (!promptResult || !promptResult.promptResult?.result) {
        this.logger.warn(`No prompt result found for worksheet ${worksheet.id}, skipping MongoDB sync`);
        return;
      }

      // Create a map of questionId to question data for quick lookup
      const questionMap = new Map();
      promptResult.promptResult.result.forEach((question: any) => {
        if (question.id) {
          questionMap.set(question.id, question);
        }
      });

      // Reorder the questions array to match PostgreSQL questionIds order
      const reorderedQuestions = worksheet.questionIds
        .map(questionId => questionMap.get(questionId))
        .filter(question => question !== undefined); // Remove any missing questions

      // Update the MongoDB document with the new order and counts
      await this.worksheetPromptResultModel.updateOne(
        { worksheetId: worksheet.id },
        {
          $set: {
            'promptResult.result': reorderedQuestions,
            currentQuestionCount: reorderedQuestions.length,
            totalQuestionCount: reorderedQuestions.length,
            updatedAt: new Date()
          }
        }
      );

      this.logger.log(`Successfully synced MongoDB question order for worksheet ${worksheet.id}`);

      // Ensure counts are consistent after reordering
      await this.ensureMongoDBCountsConsistent(worksheet.id);
    } catch (error) {
      this.logger.error(`Failed to sync MongoDB question order for worksheet ${worksheet.id}:`, error);
      // Don't throw error to avoid breaking the reordering operation
    }
  }

  /**
   * Bulk add questions to a worksheet (synchronous operation)
   */
  async bulkAddQuestions(
    worksheetId: string,
    questions: AddQuestionToWorksheetDto[],
    user: UserContext,
    options: {
      insertPosition?: number;
      validateQuestions?: boolean;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: IExerciseQuestion[];
    failures: Array<{ item: AddQuestionToWorksheetDto; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk adding ${questions.length} questions to worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Check if bulk addition would exceed question limit
    const currentCount = worksheet.questions?.length || 0;
    const maxQuestions = worksheet.maxQuestions || 100;
    if (currentCount + questions.length > maxQuestions) {
      throw new BadRequestException(
        `Bulk addition would exceed question limit. Current: ${currentCount}, Adding: ${questions.length}, Maximum: ${maxQuestions}`
      );
    }

    const successes: IExerciseQuestion[] = [];
    const failures: Array<{ item: AddQuestionToWorksheetDto; error: string; index: number }> = [];

    // Process each question
    for (let i = 0; i < questions.length; i++) {
      try {
        const questionDto = questions[i];
        const newQuestion = await this.createQuestion(questionDto, user, worksheet);

        // Add to worksheet
        if (!worksheet.questions) {
          worksheet.questions = [];
        }

        // Insert at specified position or at the end
        if (options.insertPosition && i === 0) {
          worksheet.questions.splice(options.insertPosition - 1, 0, newQuestion);
        } else {
          worksheet.questions.push(newQuestion);
        }

        successes.push(newQuestion);
      } catch (error) {
        failures.push({
          item: questions[i],
          error: error.message,
          index: i
        });
      }
    }

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_added', {
      addedQuestions: successes,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_add',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk add completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: questions.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Bulk remove questions from a worksheet (synchronous operation)
   */
  async bulkRemoveQuestions(
    worksheetId: string,
    questionIds: string[],
    user: UserContext,
    options: {
      forceRemoval?: boolean;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: string[];
    failures: Array<{ questionId: string; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk removing ${questionIds.length} questions from worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Check minimum questions requirement unless forced
    if (!options.forceRemoval) {
      const currentCount = worksheet.questionIds?.length || 0;
      const remainingCount = currentCount - questionIds.length;
      if (remainingCount < 1) {
        throw new BadRequestException(
          `Cannot remove ${questionIds.length} questions. Worksheet must have at least 1 question. Current count: ${currentCount}`
        );
      }
    }

    const successes: string[] = [];
    const failures: Array<{ questionId: string; error: string; index: number }> = [];

    // Validate which questions exist in the worksheet
    for (let i = 0; i < questionIds.length; i++) {
      try {
        const questionId = questionIds[i];

        // Find question in worksheet questionIds array
        const questionIndex = worksheet.questionIds?.findIndex(id => id === questionId);
        if (questionIndex === undefined || questionIndex === -1) {
          throw new Error(`Question with ID ${questionId} not found in worksheet`);
        }

        successes.push(questionId);
      } catch (error) {
        failures.push({
          questionId: questionIds[i],
          error: error.message,
          index: i
        });
      }
    }

    // Remove the questions from both databases
    this.logger.log(`🗑️ Removing ${successes.length} questions from both databases`);

    // Remove from PostgreSQL
    for (const questionId of successes) {
      const questionIndex = worksheet.questionIds?.findIndex(id => id === questionId);
      if (questionIndex !== undefined && questionIndex !== -1) {
        worksheet.questionIds.splice(questionIndex, 1);
      }
    }

    // Remove from MongoDB
    if (successes.length > 0) {
      await this.bulkRemoveQuestionsFromPromptResult(worksheetId, successes);
    }

    this.logger.log(`✅ Successfully removed ${successes.length} questions`);

    // Note: Position reordering is handled automatically by the order of questionIds in PostgreSQL
    // The WorksheetPromptResult questions will be reordered when accessed via getWorksheetQuestions

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questionIds?.length || 0;
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_removed', {
      removedQuestionIds: successes,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_remove',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk remove completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: questionIds.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Bulk update questions in a worksheet (synchronous operation)
   */
  async bulkUpdateQuestions(
    worksheetId: string,
    updates: Array<{ questionId: string; updates: UpdateWorksheetQuestionDto }>,
    user: UserContext,
    options: {
      validateQuestions?: boolean;
      reason?: string;
    } = {}
  ): Promise<{
    success: boolean;
    successCount: number;
    failureCount: number;
    totalCount: number;
    successes: IExerciseQuestion[];
    failures: Array<{ item: { questionId: string; updates: UpdateWorksheetQuestionDto }; error: string; index: number }>;
    processingTimeMs: number;
  }> {
    const startTime = Date.now();
    this.logger.log(`Bulk updating ${updates.length} questions in worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    const successes: IExerciseQuestion[] = [];
    const failures: Array<{ item: { questionId: string; updates: UpdateWorksheetQuestionDto }; error: string; index: number }> = [];

    // Process each update
    for (let i = 0; i < updates.length; i++) {
      try {
        const { questionId, updates: updateDto } = updates[i];

        // Verify question exists in worksheet questionIds
        if (!worksheet.questionIds?.includes(questionId)) {
          throw new Error(`Question with ID ${questionId} not found in worksheet`);
        }

        // Get the question from WorksheetPromptResult
        const existingQuestion = await this.getWorksheetQuestion(worksheetId, questionId, user);
        if (!existingQuestion) {
          throw new Error(`Question with ID ${questionId} not found in WorksheetPromptResult`);
        }

        // Validate optimistic locking if version is provided
        if (updateDto.version !== undefined) {
          if (existingQuestion.audit?.version !== updateDto.version) {
            throw new Error(`Version mismatch. Expected ${updateDto.version}, got ${existingQuestion.audit?.version}`);
          }
        }

        // Apply the update to WorksheetPromptResult
        const updateFields: any = {};
        if (updateDto.content !== undefined) updateFields.content = updateDto.content;
        if (updateDto.options !== undefined) updateFields.options = updateDto.options;
        if (updateDto.answer !== undefined) updateFields.answer = updateDto.answer;
        if (updateDto.explain !== undefined) updateFields.explain = updateDto.explain;
        if (updateDto.points !== undefined) updateFields.points = updateDto.points;
        if (updateDto.difficulty !== undefined) updateFields.difficulty = updateDto.difficulty;
        if (updateDto.media !== undefined) updateFields.media = updateDto.media;

        // Update audit information
        if (!updateFields.audit) updateFields.audit = existingQuestion.audit || {};
        updateFields.audit.updatedBy = user.sub;
        updateFields.audit.updatedAt = new Date();
        updateFields.audit.version = (existingQuestion.audit?.version || 1) + 1;

        // Add to change log
        if (!updateFields.audit.changeLog) updateFields.audit.changeLog = existingQuestion.audit?.changeLog || [];
        updateFields.audit.changeLog.push({
          timestamp: new Date(),
          userId: user.sub,
          action: 'bulk_update',
          changes: updateFields,
          reason: options.reason || 'Bulk update operation'
        });

        const updatedQuestionItem = await this.updateQuestionInPromptResult(worksheetId, questionId, updateFields);

        if (updatedQuestionItem) {
          // Get position from worksheet.questionIds array
          const position = worksheet.questionIds?.indexOf(questionId) + 1 || 1;
          const updatedQuestion = this.convertExerciseQuestionItemToIExerciseQuestion(updatedQuestionItem, position);
          successes.push(updatedQuestion);
        }
      } catch (error) {
        failures.push({
          item: updates[i],
          error: error.message,
          index: i
        });
      }
    }

    // Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;

    // Save worksheet
    await this.worksheetRepository.save(worksheet);

    // Update cache
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);

    // Emit real-time updates
    await this.emitQuestionUpdate(worksheetId, 'questions_bulk_updated', {
      updatedQuestions: successes,
      totalQuestions: worksheet.questionIds?.length || 0,
      worksheetId,
      reason: options.reason
    }, user.sub);

    // Log audit event
    await this.auditService.logBulkQuestionOperation(
      worksheetId,
      'bulk_update',
      user,
      { successCount: successes.length, failureCount: failures.length, reason: options.reason }
    );

    const processingTimeMs = Date.now() - startTime;
    this.logger.log(`Bulk update completed: ${successes.length} successes, ${failures.length} failures in ${processingTimeMs}ms`);

    return {
      success: failures.length === 0,
      successCount: successes.length,
      failureCount: failures.length,
      totalCount: updates.length,
      successes,
      failures,
      processingTimeMs
    };
  }

  /**
   * Emit real-time update via WebSocket
   */
  private async emitQuestionUpdate(
    worksheetId: string,
    event: string,
    data: any,
    excludeUserId?: string
  ): Promise<void> {
    try {
      // Emit to all users subscribed to this worksheet except the one who made the change
      this.socketGateway.server.to(`worksheet-${worksheetId}`).emit(event, {
        ...data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.logger.error(`Failed to emit ${event} for worksheet ${worksheetId}`, error);
      // Don't throw error - WebSocket failure shouldn't fail the operation
    }
  }

  /**
   * Validate user access to a pool question
   */
  private validatePoolQuestionAccess(poolQuestion: QuestionPool, user: UserContext): void {
    // Admin can access all questions
    if (user.role === EUserRole.ADMIN) {
      return;
    }

    // For now, all active questions in the pool are accessible to authenticated users
    // In the future, you could implement more granular access control based on:
    // - School-specific question pools
    // - Public/private question visibility
    // - User role-based restrictions

    if (poolQuestion.status !== 'active') {
      throw new BadRequestException('Question is not available for use');
    }
  }

  /**
   * Substitute one question ID with another question ID from the question pool
   * This method handles cases where PostgreSQL has been manually updated but MongoDB needs to be synced
   */
  async substituteQuestionInWorksheet(
    worksheetId: string,
    oldQuestionId: string,
    newQuestionId: string,
    user: UserContext
  ): Promise<void> {
    this.logger.log(`Substituting question ${oldQuestionId} with ${newQuestionId} in worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Verify the new question ID exists in PostgreSQL questionIds
    if (!worksheet.questionIds || !worksheet.questionIds.includes(newQuestionId)) {
      throw new NotFoundException(`New question ID ${newQuestionId} not found in worksheet ${worksheetId} PostgreSQL questionIds`);
    }

    // Step 3: Get the MongoDB document
    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult) {
      throw new NotFoundException(`WorksheetPromptResult not found for worksheet ${worksheetId}`);
    }

    if (!promptResult.promptResult?.result) {
      throw new NotFoundException(`No questions array found in MongoDB for worksheet ${worksheetId}`);
    }

    // Step 4: Find the old question in MongoDB
    const oldQuestionIndex = promptResult.promptResult.result.findIndex(
      (question: any) => question.id === oldQuestionId
    );

    if (oldQuestionIndex === -1) {
      throw new NotFoundException(`Old question ${oldQuestionId} not found in MongoDB for worksheet ${worksheetId}`);
    }

    // Step 5: Get the new question data from the question pool
    const newQuestionData = await this.questionPoolService.getQuestionById(newQuestionId);
    if (!newQuestionData) {
      throw new NotFoundException(`Question ${newQuestionId} not found in question pool`);
    }

    // Step 6: Create the new question object for MongoDB
    const newQuestionForMongoDB = {
      id: newQuestionId,
      type: newQuestionData.type,
      content: newQuestionData.content,
      options: newQuestionData.options || [],
      answer: newQuestionData.answer || [],
      explain: newQuestionData.explain || '',
      subject: newQuestionData.subject,
      parentSubject: newQuestionData.parentSubject,
      childSubject: newQuestionData.childSubject,
      grade: newQuestionData.grade,
      difficulty: newQuestionData.difficulty || 'medium',
      language: newQuestionData.language || 'en',
      tags: newQuestionData.metadata?.tags || [],
      metadata: {
        addedAt: new Date(),
        addedBy: user.sub,
        source: 'question_pool',
        questionPoolId: newQuestionId,
        substitutedFrom: oldQuestionId
      },
      audit: {
        createdAt: new Date(),
        createdBy: user.sub,
        lastModifiedAt: new Date(),
        lastModifiedBy: user.sub,
        version: 1,
        changeLog: [{
          timestamp: new Date(),
          userId: user.sub,
          action: 'substitution',
          changes: {
            oldQuestionId,
            newQuestionId,
            reason: 'Question ID substitution'
          }
        }]
      }
    };

    // Step 7: Replace the old question with the new question in MongoDB
    promptResult.promptResult.result[oldQuestionIndex] = newQuestionForMongoDB;

    // Step 8: Update MongoDB counts (should remain the same since we're replacing, not adding/removing)
    promptResult.currentQuestionCount = promptResult.promptResult.result.length;
    promptResult.totalQuestionCount = promptResult.promptResult.result.length;
    promptResult.updatedAt = new Date();

    // Step 9: Save the updated MongoDB document
    try {
      await promptResult.save();
      this.logger.log(`Successfully substituted question ${oldQuestionId} with ${newQuestionId} in MongoDB for worksheet ${worksheetId}`);
    } catch (error) {
      this.logger.error(`Failed to save WorksheetPromptResult after substitution:`, error);
      throw error;
    }

    // Step 10: Update worksheet metadata
    worksheet.lastModifiedBy = user.sub;
    worksheet.questionMetadata = {
      ...worksheet.questionMetadata,
      lastQuestionUpdate: new Date(),
      questionVersion: (worksheet.questionMetadata?.questionVersion || 1) + 1,
      hasUnsavedChanges: false
    };

    // Step 11: Save the updated worksheet
    await this.worksheetRepository.save(worksheet);

    // Step 12: Update cache from MongoDB
    await this.updateQuestionCacheFromMongoDB(worksheetId, user);
    await this.enhancedCacheService.invalidateWorksheetCache(worksheetId);

    // Step 13: Emit real-time update
    await this.emitQuestionUpdate(worksheetId, 'question_substituted', {
      oldQuestionId,
      newQuestionId,
      worksheetId,
      totalQuestions: worksheet.questionIds?.length || 0
    }, user.sub);

    // Step 14: Log audit event
    await this.auditService.logQuestionUpdatedInWorksheet(worksheetId, newQuestionId, user, {
      oldQuestionId,
      newQuestionId,
      updateReason: 'Question ID substitution'
    });

    this.logger.log(`Successfully completed question substitution for worksheet ${worksheetId}`);
  }

  /**
   * Sync MongoDB with PostgreSQL questionIds when manual changes have been made
   * This method identifies discrepancies and fixes them
   */
  async syncMongoDBWithPostgreSQL(
    worksheetId: string,
    user: UserContext,
    options: {
      dryRun?: boolean;
      forceSync?: boolean;
    } = {}
  ): Promise<{
    success: boolean;
    changes: Array<{
      action: 'remove' | 'add' | 'reorder';
      questionId: string;
      details: string;
    }>;
    summary: string;
  }> {
    this.logger.log(`${options.dryRun ? 'DRY RUN - ' : ''}Syncing MongoDB with PostgreSQL for worksheet ${worksheetId}`);

    const changes: Array<{
      action: 'remove' | 'add' | 'reorder';
      questionId: string;
      details: string;
    }> = [];

    // Step 1: Get current state from both databases
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);
    const pgQuestionIds = worksheet.questionIds || [];

    const promptResult = await this.worksheetPromptResultModel.findOne({
      worksheetId: worksheetId
    });

    if (!promptResult) {
      throw new NotFoundException(`WorksheetPromptResult not found for worksheet ${worksheetId}`);
    }

    const mongoQuestions = promptResult.promptResult?.result || [];
    const mongoQuestionIds = mongoQuestions.map((q: any) => q.id).filter(Boolean);

    // Step 2: Identify differences
    const pgSet = new Set(pgQuestionIds);
    const mongoSet = new Set(mongoQuestionIds);

    // Questions to remove from MongoDB (exist in MongoDB but not in PostgreSQL)
    const questionsToRemove = mongoQuestionIds.filter((id: string) => !pgSet.has(id));

    // Questions to add to MongoDB (exist in PostgreSQL but not in MongoDB)
    const questionsToAdd = pgQuestionIds.filter((id: string) => !mongoSet.has(id));

    // Step 3: Log what would be changed
    for (const questionId of questionsToRemove) {
      changes.push({
        action: 'remove',
        questionId,
        details: `Remove question ${questionId} from MongoDB (not in PostgreSQL)`
      });
    }

    for (const questionId of questionsToAdd) {
      changes.push({
        action: 'add',
        questionId,
        details: `Add question ${questionId} to MongoDB (missing from MongoDB)`
      });
    }

    // Check if reordering is needed
    const commonQuestions = pgQuestionIds.filter(id => mongoSet.has(id));
    const mongoCommonOrder = mongoQuestions
      .filter((q: any) => commonQuestions.includes(q.id))
      .map((q: any) => q.id);

    const pgCommonOrder = pgQuestionIds.filter(id => mongoSet.has(id));

    if (JSON.stringify(mongoCommonOrder) !== JSON.stringify(pgCommonOrder)) {
      changes.push({
        action: 'reorder',
        questionId: 'multiple',
        details: 'Reorder questions in MongoDB to match PostgreSQL order'
      });
    }

    // Step 4: If dry run, just return the changes
    if (options.dryRun) {
      return {
        success: true,
        changes,
        summary: `Would make ${changes.length} changes: ${questionsToRemove.length} removals, ${questionsToAdd.length} additions, ${changes.filter(c => c.action === 'reorder').length} reorderings`
      };
    }

    // Step 5: Apply the changes
    if (changes.length === 0) {
      return {
        success: true,
        changes: [],
        summary: 'No changes needed - databases are already in sync'
      };
    }

    try {
      // Remove questions that shouldn't be in MongoDB
      for (const questionId of questionsToRemove) {
        const questionIndex = mongoQuestions.findIndex((q: any) => q.id === questionId);
        if (questionIndex !== -1) {
          mongoQuestions.splice(questionIndex, 1);
          this.logger.log(`Removed question ${questionId} from MongoDB`);
        }
      }

      // Add questions that should be in MongoDB
      for (const questionId of questionsToAdd) {
        try {
          const questionData = await this.questionPoolService.getQuestionById(questionId);
          if (questionData) {
            const newQuestion = {
              id: questionId,
              type: questionData.type,
              content: questionData.content,
              options: questionData.options || [],
              answer: questionData.answer || [],
              explain: questionData.explain || '',
              subject: questionData.subject,
              parentSubject: questionData.parentSubject,
              childSubject: questionData.childSubject,
              grade: questionData.grade,
              difficulty: questionData.difficulty || 'medium',
              language: questionData.language || 'en',
              tags: questionData.metadata?.tags || [],
              metadata: {
                addedAt: new Date(),
                addedBy: user.sub,
                source: 'question_pool',
                questionPoolId: questionId,
                syncedFromPostgreSQL: true
              },
              audit: {
                createdAt: new Date(),
                createdBy: user.sub,
                lastModifiedAt: new Date(),
                lastModifiedBy: user.sub,
                version: 1,
                changeLog: [{
                  timestamp: new Date(),
                  userId: user.sub,
                  action: 'sync_addition',
                  changes: { reason: 'Added during PostgreSQL-MongoDB sync' }
                }]
              }
            };
            mongoQuestions.push(newQuestion);
            this.logger.log(`Added question ${questionId} to MongoDB`);
          }
        } catch (error) {
          this.logger.error(`Failed to add question ${questionId} to MongoDB:`, error);
          // Continue with other questions
        }
      }

      // Reorder questions to match PostgreSQL order
      const questionMap = new Map();
      mongoQuestions.forEach((question: any) => {
        if (question.id) {
          questionMap.set(question.id, question);
        }
      });

      const reorderedQuestions = pgQuestionIds
        .map(questionId => questionMap.get(questionId))
        .filter(question => question !== undefined);

      // Update the MongoDB document
      promptResult.promptResult.result = reorderedQuestions;
      promptResult.currentQuestionCount = reorderedQuestions.length;
      promptResult.totalQuestionCount = reorderedQuestions.length;
      promptResult.updatedAt = new Date();

      await promptResult.save();

      // Update worksheet metadata
      worksheet.lastModifiedBy = user.sub;
      worksheet.questionMetadata = {
        ...worksheet.questionMetadata,
        lastQuestionUpdate: new Date(),
        questionVersion: (worksheet.questionMetadata?.questionVersion || 1) + 1,
        hasUnsavedChanges: false
      };

      await this.worksheetRepository.save(worksheet);

      // Update cache
      await this.updateQuestionCacheFromMongoDB(worksheetId, user);
      await this.enhancedCacheService.invalidateWorksheetCache(worksheetId);

      return {
        success: true,
        changes,
        summary: `Successfully made ${changes.length} changes: ${questionsToRemove.length} removals, ${questionsToAdd.length} additions, ${changes.filter(c => c.action === 'reorder').length} reorderings`
      };

    } catch (error) {
      this.logger.error(`Failed to sync MongoDB with PostgreSQL for worksheet ${worksheetId}:`, error);
      throw error;
    }
  }

  /**
   * Auto-fill worksheet with questions from question pool based on worksheet criteria
   */
  async autoFillQuestionsFromPool(
    worksheetId: string,
    user: UserContext,
    options: AutoFillQuestionsDto = {}
  ): Promise<{
    questionsAdded: number;
    totalQuestions: number;
    addedQuestions: IExerciseQuestion[];
  }> {
    this.logger.log(`Auto-filling questions for worksheet ${worksheetId} by user ${user.sub}`);

    // Validate worksheet access
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Get current question count
    const currentQuestionCount = worksheet.questionIds?.length || 0;

    // Determine how many questions to add
    let questionsToAdd: number;
    if (options.questionCount) {
      questionsToAdd = options.questionCount;
    } else {
      // Calculate based on worksheet target (assuming a default target of 10 if not specified)
      const targetQuestions = 10; // You might want to get this from worksheet settings
      questionsToAdd = Math.max(0, targetQuestions - currentQuestionCount);
    }

    if (questionsToAdd <= 0) {
      throw new BadRequestException('Worksheet already has sufficient questions or invalid question count');
    }

    // Extract criteria from worksheet selectedOptions
    const criteria = await this.extractCriteriaFromWorksheet(worksheet);

    // Override with provided options
    if (options.difficultyOverride) {
      criteria.difficulty = options.difficultyOverride;
    }
    if (options.questionTypesOverride) {
      criteria.questionTypes = options.questionTypesOverride;
    }

    // Build query parameters - only include fields that exist in question pool
    const queryParams: any = {
      grade: criteria.grade?.toUpperCase(), // Convert to uppercase to match database format (P6, not p6)
      status: 'active'
    };

    // Add subject filter if available
    if (criteria.subject) {
      queryParams.subject = criteria.subject;
    }

    // Add optional fields only if they exist
    if (criteria.parentSubject) {
      queryParams.parentSubject = criteria.parentSubject;
    }
    if (criteria.childSubject) {
      queryParams.childSubject = criteria.childSubject;
    }
    if (criteria.questionTypes && criteria.questionTypes.length > 0) {
      queryParams.type = { $in: criteria.questionTypes };
    }

    // Skip difficulty filter as questions in question pool don't have difficulty fields
    if (criteria.difficulty) {
      this.logger.debug(`Skipping difficulty filter (${criteria.difficulty}) - not available in question pool schema`);
    }

    // Skip schoolId and isPublic filters as they don't exist in current question pool schema
    // if (user.schoolId) {
    //   queryParams.schoolId = user.schoolId;
    // }
    // queryParams.isPublic = true;

    this.logger.debug(`Querying question pool with params:`, JSON.stringify(queryParams, null, 2));

    // Query question pool
    const poolQuestions = await this.questionPoolService.getQuestions(
      queryParams,
      questionsToAdd,
      0
    );

    if (!poolQuestions.questions || poolQuestions.questions.length === 0) {
      throw new BadRequestException('No suitable questions found in question pool matching worksheet criteria');
    }

    // Add questions to worksheet
    const addedQuestions: IExerciseQuestion[] = [];
    let successCount = 0;

    for (const poolQuestion of poolQuestions.questions) {
      try {
        const questionDto = {
          questionPoolId: (poolQuestion as any)._id.toString(),
          position: currentQuestionCount + successCount + 1
        } as AddQuestionToWorksheetDto;

        const addedQuestion = await this.addQuestionToWorksheet(worksheetId, questionDto, user);

        // Add the complete question data to the response
        addedQuestions.push(addedQuestion);

        successCount++;

        // Update pool question usage
        await this.updatePoolQuestionUsage((poolQuestion as any)._id.toString());

      } catch (error) {
        this.logger.warn(`Failed to add question ${(poolQuestion as any)._id} from pool:`, error);
        // Continue with next question
      }
    }

    if (successCount === 0) {
      throw new BadRequestException('Failed to add any questions from pool');
    }

    // Get updated worksheet to return current total
    const updatedWorksheet = await this.worksheetRepository.findOne({
      where: { id: worksheetId }
    });

    this.logger.log(`Successfully added ${successCount} questions from pool to worksheet ${worksheetId}`);

    return {
      questionsAdded: successCount,
      totalQuestions: updatedWorksheet?.questionIds?.length || 0,
      addedQuestions
    };
  }

  /**
   * Extract criteria from worksheet selectedOptions for question pool querying
   */
  private async extractCriteriaFromWorksheet(worksheet: Worksheet): Promise<{
    subject?: string;
    parentSubject?: string;
    childSubject?: string;
    grade?: string;
    difficulty?: string;
    questionTypes?: string[];
  }> {
    // Load selectedOptions with relations
    const worksheetWithOptions = await this.worksheetRepository.findOne({
      where: { id: worksheet.id },
      relations: ['selectedOptions', 'selectedOptions.optionType', 'selectedOptions.optionValue']
    });

    if (!worksheetWithOptions?.selectedOptions) {
      throw new BadRequestException('Worksheet has no criteria defined in selectedOptions');
    }

    const criteria: any = {};

    // Extract subject
    const subjectOption = worksheetWithOptions.selectedOptions.find(
      opt => opt.optionType.key === 'subject'
    );
    if (subjectOption) {
      criteria.subject = subjectOption.optionValue.value;
    }

    // Extract parent subject
    const parentSubjectOption = worksheetWithOptions.selectedOptions.find(
      opt => opt.optionType.key === 'parent_subject'
    );
    if (parentSubjectOption) {
      criteria.parentSubject = parentSubjectOption.optionValue.value;
    }

    // Extract child subject
    const childSubjectOption = worksheetWithOptions.selectedOptions.find(
      opt => opt.optionType.key === 'child_subject'
    );
    if (childSubjectOption) {
      criteria.childSubject = childSubjectOption.optionValue.value;
    }

    // Extract grade
    const gradeOption = worksheetWithOptions.selectedOptions.find(
      opt => opt.optionType.key === 'grade'
    );
    if (gradeOption) {
      criteria.grade = gradeOption.optionValue.value;
    }

    // Extract difficulty (try multiple possible keys)
    const difficultyOption = worksheetWithOptions.selectedOptions.find(
      opt => opt.optionType.key === 'difficulty_level' || opt.optionType.key === 'level' || opt.optionType.key === 'difficulty'
    );
    if (difficultyOption) {
      criteria.difficulty = difficultyOption.optionValue.value;
      this.logger.debug(`Found difficulty: ${criteria.difficulty} from key: ${difficultyOption.optionType.key}`);
    } else {
      this.logger.debug(`No difficulty found in selectedOptions - will skip difficulty filtering`);
    }

    // Extract question types
    const questionTypeOptions = worksheetWithOptions.selectedOptions.filter(
      opt => opt.optionType.key === 'question_type'
    );
    if (questionTypeOptions.length > 0) {
      criteria.questionTypes = questionTypeOptions.map(opt => opt.optionValue.value);
    }

    this.logger.debug(`Extracted criteria from worksheet:`, JSON.stringify(criteria, null, 2));
    this.logger.debug(`Available selectedOptions keys:`, worksheetWithOptions.selectedOptions.map(opt => opt.optionType.key));

    return criteria;
  }

  /**
   * Update pool question usage statistics
   */
  private async updatePoolQuestionUsage(questionPoolId: string): Promise<void> {
    try {
      // This could be implemented to track usage statistics
      // For now, we'll just log the usage
      this.logger.debug(`Question ${questionPoolId} used from pool`);

      // In the future, you could:
      // - Increment usage count in the pool question
      // - Track last used timestamp
      // - Update popularity metrics
      // await this.questionPoolService.incrementUsageCount(questionPoolId);
    } catch (error) {
      this.logger.error(`Failed to update pool question usage for ${questionPoolId}`, error);
      // Don't throw error - usage tracking failure shouldn't fail the operation
    }
  }




}
