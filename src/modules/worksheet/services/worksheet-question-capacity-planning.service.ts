import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { WorksheetQuestionPerformanceTestingService, PerformanceTestResult } from './worksheet-question-performance-testing.service';

export interface CapacityMetrics {
  currentLoad: {
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
    cpuUsage: number;
  };
  projectedLoad: {
    expectedGrowthRate: number; // percentage per month
    timeHorizon: number; // months
    projectedRequestsPerSecond: number;
    projectedMemoryUsage: number;
  };
  systemLimits: {
    maxRequestsPerSecond: number;
    maxMemoryUsage: number;
    maxResponseTime: number;
    maxErrorRate: number;
  };
}

export interface CapacityRecommendation {
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: 'scaling' | 'optimization' | 'infrastructure' | 'monitoring';
  title: string;
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  timeline: string;
  implementation: string[];
}

export interface CapacityPlanningReport {
  timestamp: string;
  currentCapacity: CapacityMetrics;
  bottlenecks: string[];
  recommendations: CapacityRecommendation[];
  scalingPlan: {
    shortTerm: CapacityRecommendation[];
    mediumTerm: CapacityRecommendation[];
    longTerm: CapacityRecommendation[];
  };
  costProjections: {
    currentMonthlyCost: number;
    projectedMonthlyCost: number;
    optimizationSavings: number;
  };
}

/**
 * Service for analyzing system performance and providing capacity planning recommendations
 */
@Injectable()
export class WorksheetQuestionCapacityPlanningService {
  private readonly logger = new Logger(WorksheetQuestionCapacityPlanningService.name);

  // Performance thresholds for capacity planning
  private readonly CAPACITY_THRESHOLDS = {
    responseTime: {
      warning: 500, // ms
      critical: 1000,
    },
    throughput: {
      minimum: 20, // requests/second
      optimal: 100,
    },
    errorRate: {
      warning: 1, // percentage
      critical: 5,
    },
    memoryUsage: {
      warning: 70, // percentage of available memory
      critical: 85,
    },
    cpuUsage: {
      warning: 70, // percentage
      critical: 85,
    },
  };

  constructor(
    private readonly metricsService: WorksheetQuestionMetricsService,
    private readonly performanceTestingService: WorksheetQuestionPerformanceTestingService,
    private readonly configService: ConfigService
  ) {}

  /**
   * Generate comprehensive capacity planning report
   */
  async generateCapacityPlanningReport(): Promise<CapacityPlanningReport> {
    this.logger.log('Generating capacity planning report...');

    const currentMetrics = await this.getCurrentCapacityMetrics();
    const performanceData = await this.getPerformanceData();
    const bottlenecks = this.identifyBottlenecks(currentMetrics, performanceData);
    const recommendations = this.generateRecommendations(currentMetrics, bottlenecks);
    const scalingPlan = this.createScalingPlan(recommendations);
    const costProjections = this.calculateCostProjections(currentMetrics, recommendations);

    const report: CapacityPlanningReport = {
      timestamp: new Date().toISOString(),
      currentCapacity: currentMetrics,
      bottlenecks,
      recommendations,
      scalingPlan,
      costProjections,
    };

    this.logger.log('Capacity planning report generated successfully');
    return report;
  }

  /**
   * Get current system capacity metrics
   */
  private async getCurrentCapacityMetrics(): Promise<CapacityMetrics> {
    const performanceSummary = await this.metricsService.getPerformanceSummary();
    
    // Get system resource usage
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    return {
      currentLoad: {
        requestsPerSecond: performanceSummary.averageApiDuration ? 1000 / performanceSummary.averageApiDuration : 0,
        averageResponseTime: performanceSummary.averageApiDuration || 0,
        errorRate: this.calculateErrorRate(performanceSummary),
        memoryUsage: memoryUsagePercent,
        cpuUsage: await this.getCpuUsage(),
      },
      projectedLoad: {
        expectedGrowthRate: this.configService.get<number>('EXPECTED_GROWTH_RATE', 20), // 20% per month default
        timeHorizon: 12, // 12 months
        projectedRequestsPerSecond: 0, // Will be calculated
        projectedMemoryUsage: 0, // Will be calculated
      },
      systemLimits: {
        maxRequestsPerSecond: this.configService.get<number>('MAX_REQUESTS_PER_SECOND', 200),
        maxMemoryUsage: this.configService.get<number>('MAX_MEMORY_USAGE_PERCENT', 80),
        maxResponseTime: this.configService.get<number>('MAX_RESPONSE_TIME', 1000),
        maxErrorRate: this.configService.get<number>('MAX_ERROR_RATE', 2),
      },
    };
  }

  /**
   * Get performance data from recent tests
   */
  private async getPerformanceData(): Promise<PerformanceTestResult[]> {
    // Run a quick performance test to get current performance data
    const testResults = await this.performanceTestingService.runPerformanceTestSuite();
    return testResults;
  }

  /**
   * Identify system bottlenecks
   */
  private identifyBottlenecks(metrics: CapacityMetrics, performanceData: PerformanceTestResult[]): string[] {
    const bottlenecks: string[] = [];

    // Check response time bottlenecks
    if (metrics.currentLoad.averageResponseTime > this.CAPACITY_THRESHOLDS.responseTime.warning) {
      bottlenecks.push(`High response time: ${metrics.currentLoad.averageResponseTime}ms`);
    }

    // Check throughput bottlenecks
    if (metrics.currentLoad.requestsPerSecond < this.CAPACITY_THRESHOLDS.throughput.minimum) {
      bottlenecks.push(`Low throughput: ${metrics.currentLoad.requestsPerSecond} req/s`);
    }

    // Check error rate bottlenecks
    if (metrics.currentLoad.errorRate > this.CAPACITY_THRESHOLDS.errorRate.warning) {
      bottlenecks.push(`High error rate: ${metrics.currentLoad.errorRate}%`);
    }

    // Check memory usage bottlenecks
    if (metrics.currentLoad.memoryUsage > this.CAPACITY_THRESHOLDS.memoryUsage.warning) {
      bottlenecks.push(`High memory usage: ${metrics.currentLoad.memoryUsage.toFixed(1)}%`);
    }

    // Check CPU usage bottlenecks
    if (metrics.currentLoad.cpuUsage > this.CAPACITY_THRESHOLDS.cpuUsage.warning) {
      bottlenecks.push(`High CPU usage: ${metrics.currentLoad.cpuUsage.toFixed(1)}%`);
    }

    // Analyze performance test results for additional bottlenecks
    performanceData.forEach(result => {
      if (result.results.errorRate > 5) {
        bottlenecks.push(`Performance test "${result.testName}" showing high error rate: ${result.results.errorRate}%`);
      }
      if (result.results.averageResponseTime > 1000) {
        bottlenecks.push(`Performance test "${result.testName}" showing slow response time: ${result.results.averageResponseTime}ms`);
      }
    });

    return bottlenecks;
  }

  /**
   * Generate capacity planning recommendations
   */
  private generateRecommendations(metrics: CapacityMetrics, bottlenecks: string[]): CapacityRecommendation[] {
    const recommendations: CapacityRecommendation[] = [];

    // High response time recommendations
    if (metrics.currentLoad.averageResponseTime > this.CAPACITY_THRESHOLDS.responseTime.warning) {
      recommendations.push({
        priority: metrics.currentLoad.averageResponseTime > this.CAPACITY_THRESHOLDS.responseTime.critical ? 'critical' : 'high',
        category: 'optimization',
        title: 'Optimize Database Queries',
        description: 'Response times are above acceptable thresholds',
        impact: 'Reduce response time by 30-50%',
        effort: 'medium',
        timeline: '2-4 weeks',
        implementation: [
          'Add database indexes for frequently queried fields',
          'Implement query result caching',
          'Optimize MongoDB aggregation pipelines',
          'Consider database connection pooling optimization'
        ]
      });
    }

    // Low throughput recommendations
    if (metrics.currentLoad.requestsPerSecond < this.CAPACITY_THRESHOLDS.throughput.minimum) {
      recommendations.push({
        priority: 'high',
        category: 'scaling',
        title: 'Horizontal Scaling',
        description: 'System throughput is below optimal levels',
        impact: 'Increase throughput by 2-3x',
        effort: 'high',
        timeline: '4-8 weeks',
        implementation: [
          'Deploy additional application instances',
          'Implement load balancing',
          'Consider microservices architecture',
          'Optimize resource allocation'
        ]
      });
    }

    // High memory usage recommendations
    if (metrics.currentLoad.memoryUsage > this.CAPACITY_THRESHOLDS.memoryUsage.warning) {
      recommendations.push({
        priority: metrics.currentLoad.memoryUsage > this.CAPACITY_THRESHOLDS.memoryUsage.critical ? 'critical' : 'high',
        category: 'optimization',
        title: 'Memory Optimization',
        description: 'Memory usage is approaching system limits',
        impact: 'Reduce memory usage by 20-40%',
        effort: 'medium',
        timeline: '2-3 weeks',
        implementation: [
          'Implement memory-efficient data structures',
          'Add garbage collection optimization',
          'Implement data streaming for large datasets',
          'Review and optimize caching strategies'
        ]
      });
    }

    // General performance recommendations
    recommendations.push({
      priority: 'medium',
      category: 'monitoring',
      title: 'Enhanced Performance Monitoring',
      description: 'Implement comprehensive performance monitoring',
      impact: 'Improve visibility and early detection of issues',
      effort: 'low',
      timeline: '1-2 weeks',
      implementation: [
        'Set up performance dashboards',
        'Implement automated alerting',
        'Add performance regression testing to CI/CD',
        'Create capacity planning automation'
      ]
    });

    return recommendations;
  }

  /**
   * Create scaling plan based on recommendations
   */
  private createScalingPlan(recommendations: CapacityRecommendation[]) {
    return {
      shortTerm: recommendations.filter(r => r.timeline.includes('1-2 weeks') || r.timeline.includes('2-3 weeks')),
      mediumTerm: recommendations.filter(r => r.timeline.includes('2-4 weeks') || r.timeline.includes('4-8 weeks')),
      longTerm: recommendations.filter(r => r.timeline.includes('8+ weeks') || r.timeline.includes('months')),
    };
  }

  /**
   * Calculate cost projections
   */
  private calculateCostProjections(metrics: CapacityMetrics, recommendations: CapacityRecommendation[]) {
    // This is a simplified cost calculation - in a real system, you'd integrate with cloud provider APIs
    const baseMonthlyCost = this.configService.get<number>('BASE_MONTHLY_COST', 1000);
    const growthMultiplier = 1 + (metrics.projectedLoad.expectedGrowthRate / 100);
    
    return {
      currentMonthlyCost: baseMonthlyCost,
      projectedMonthlyCost: baseMonthlyCost * growthMultiplier,
      optimizationSavings: baseMonthlyCost * 0.2, // Assume 20% savings from optimizations
    };
  }

  /**
   * Helper methods
   */
  private calculateErrorRate(performanceSummary: any): number {
    if (!performanceSummary.totalApiRequests || !performanceSummary.totalApiErrors) {
      return 0;
    }
    return (performanceSummary.totalApiErrors / performanceSummary.totalApiRequests) * 100;
  }

  private async getCpuUsage(): Promise<number> {
    // Simplified CPU usage calculation
    // In a real system, you'd use a proper system monitoring library
    return Math.random() * 50 + 20; // Mock value between 20-70%
  }
}
