import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Connection } from 'mongoose';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetQuestionMetricsService } from './worksheet-question-metrics.service';
import { ConfigService } from '@nestjs/config';

/**
 * Service for optimizing database queries and managing indexes for worksheet question operations
 */
@Injectable()
export class WorksheetQuestionDbOptimizationService implements OnModuleInit {
  private readonly logger = new Logger(WorksheetQuestionDbOptimizationService.name);

  constructor(
    @InjectModel(WorksheetQuestionDocument.name)
    private worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    private readonly metricsService: WorksheetQuestionMetricsService,
    private readonly configService: ConfigService
  ) {}

  async onModuleInit() {
    // Initialize database optimizations on module startup
    await this.initializeOptimizations();
  }

  /**
   * Initialize database optimizations including indexes and query monitoring
   */
  async initializeOptimizations(): Promise<void> {
    try {
      this.logger.log('Initializing database optimizations for worksheet questions...');

      // Create optimized indexes
      await this.createOptimizedIndexes();

      // Enable query profiling if configured
      await this.configureQueryProfiling();

      // Set up connection monitoring
      this.setupConnectionMonitoring();

      this.logger.log('Database optimizations initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize database optimizations', error);
    }
  }

  /**
   * Create optimized compound indexes for worksheet question queries
   */
  async createOptimizedIndexes(): Promise<void> {
    try {
      const collection = this.worksheetQuestionModel.collection;

      // Primary compound indexes for common query patterns
      const indexes = [
        // Most common query: find questions by worksheet ID and position
        { worksheetId: 1, position: 1 },
        
        // School-based queries with worksheet filtering
        { schoolId: 1, worksheetId: 1, status: 1 },
        
        // Status-based queries for active questions
        { status: 1, worksheetId: 1, position: 1 },
        
        // User-based queries for collaboration
        { worksheetId: 1, 'audit.lastModifiedBy': 1, 'audit.lastModifiedAt': -1 },
        
        // Performance optimization for question counting
        { worksheetId: 1, status: 1 },
        
        // Collaboration and locking queries
        { worksheetId: 1, status: 1, 'audit.lastModifiedAt': -1 },
        
        // Subject and grade-based filtering
        { subject: 1, grade: 1, status: 1 },
        { childSubject: 1, grade: 1, status: 1 },
        
        // Question type and difficulty filtering
        { type: 1, difficulty: 1, status: 1 },
        
        // Analytics and reporting queries
        { 'analytics.totalAttempts': -1, status: 1 },
        { 'analytics.correctAttempts': -1, 'analytics.totalAttempts': -1 },
        
        // Time-based queries for recent activity
        { 'audit.createdAt': -1, status: 1 },
        { 'audit.lastModifiedAt': -1, worksheetId: 1 },
        
        // User response tracking
        { 'userResponses.userId': 1, worksheetId: 1, status: 1 },
        
        // Media and content queries
        { 'media.hasImage': 1, status: 1 },
        { 'media.hasVideo': 1, status: 1 }
      ];

      // Create indexes with proper options
      for (const indexSpec of indexes) {
        try {
          // Filter out undefined values from index specification
          const cleanIndexSpec = Object.fromEntries(
            Object.entries(indexSpec).filter(([key, value]) => value !== undefined)
          );

          if (Object.keys(cleanIndexSpec).length > 0) {
            await collection.createIndex(cleanIndexSpec, {
              background: true,
              name: this.generateIndexName(cleanIndexSpec)
            });

            this.logger.debug(`Created index: ${this.generateIndexName(cleanIndexSpec)}`);
          }
        } catch (error) {
          // Index might already exist, log but don't fail
          this.logger.debug(`Index creation skipped (may already exist): ${this.generateIndexName(indexSpec)}`);
        }
      }

      // Create text indexes for search functionality
      try {
        await collection.createIndex(
          { content: 'text', explain: 'text' },
          { 
            background: true,
            name: 'worksheet_question_text_search',
            weights: { content: 10, explain: 5 }
          }
        );
        this.logger.debug('Created text search index');
      } catch (error) {
        this.logger.debug('Text search index creation skipped (may already exist)');
      }

      this.logger.log(`Database indexes optimized for worksheet questions`);
    } catch (error) {
      this.logger.error('Failed to create optimized indexes', error);
      throw error;
    }
  }

  /**
   * Configure query profiling for performance monitoring
   */
  async configureQueryProfiling(): Promise<void> {
    try {
      const enableProfiling = this.configService.get<boolean>('ENABLE_QUERY_PROFILING', false);
      
      if (!enableProfiling) {
        this.logger.debug('Query profiling disabled by configuration');
        return;
      }

      const db = this.worksheetQuestionModel.db.db;

      // Set profiling level (2 = profile all operations)
      if (db) {
        await db.admin().command({ profile: 2, slowms: 100 });
      }
      
      this.logger.log('Query profiling enabled for slow operations (>100ms)');
    } catch (error) {
      this.logger.warn('Failed to configure query profiling', error);
    }
  }

  /**
   * Set up connection monitoring
   */
  private setupConnectionMonitoring(): void {
    const connection = this.worksheetQuestionModel.db;
    
    // Monitor connection events
    connection.on('connected', () => {
      this.logger.log('MongoDB connection established');
      this.updateConnectionMetrics();
    });

    connection.on('disconnected', () => {
      this.logger.warn('MongoDB connection lost');
      this.updateConnectionMetrics();
    });

    connection.on('error', (error) => {
      this.logger.error('MongoDB connection error', error);
      this.updateConnectionMetrics();
    });

    // Update metrics periodically
    setInterval(() => {
      this.updateConnectionMetrics();
    }, 30000); // Every 30 seconds
  }

  /**
   * Update connection metrics
   */
  private updateConnectionMetrics(): void {
    try {
      const connection = this.worksheetQuestionModel.db;
      const readyState = connection.readyState;
      
      // Update metrics based on connection state
      this.metricsService.updateDbConnections(readyState === 1 ? 1 : 0);
    } catch (error) {
      this.logger.debug('Failed to update connection metrics', error);
    }
  }

  /**
   * Generate a descriptive index name from index specification
   */
  private generateIndexName(indexSpec: any): string {
    const fields = Object.keys(indexSpec).map(key => {
      const direction = indexSpec[key] === 1 ? 'asc' : 'desc';
      return `${key}_${direction}`;
    }).join('_');
    
    return `worksheet_question_${fields}`;
  }

  /**
   * Analyze query performance and suggest optimizations
   */
  async analyzeQueryPerformance(): Promise<any> {
    try {
      const db = this.worksheetQuestionModel.db.db;

      // Get profiling data
      const profilingData = await db?.collection('system.profile')
        .find({ ns: `${db?.databaseName}.worksheet_questions` })
        .sort({ ts: -1 })
        .limit(100)
        .toArray() || [];

      // Analyze slow queries
      const slowQueries = profilingData.filter(op => op.millis > 100);
      
      // Get index usage statistics
      const indexStats = await this.worksheetQuestionModel.collection.aggregate([
        { $indexStats: {} }
      ]).toArray();

      // Get collection statistics using collStats command
      const collStats = await db?.command({ collStats: 'worksheet_questions' }).catch(() => ({})) || {};

      return {
        slowQueries: slowQueries.length,
        averageQueryTime: profilingData.length > 0 
          ? profilingData.reduce((sum, op) => sum + op.millis, 0) / profilingData.length 
          : 0,
        indexUsage: indexStats,
        collectionStats: {
          documentCount: (collStats as any)?.count || 0,
          avgDocumentSize: (collStats as any)?.avgObjSize || 0,
          totalIndexSize: (collStats as any)?.totalIndexSize || 0,
          storageSize: (collStats as any)?.storageSize || 0
        },
        recommendations: this.generateOptimizationRecommendations(slowQueries, indexStats)
      };
    } catch (error) {
      this.logger.error('Failed to analyze query performance', error);
      return {
        error: 'Failed to analyze query performance',
        message: error.message
      };
    }
  }

  /**
   * Generate optimization recommendations based on analysis
   */
  private generateOptimizationRecommendations(slowQueries: any[], indexStats: any[]): string[] {
    const recommendations: string[] = [];

    // Analyze slow queries
    if (slowQueries.length > 0) {
      recommendations.push(`Found ${slowQueries.length} slow queries. Consider optimizing query patterns.`);
      
      // Check for common slow query patterns
      const sortOperations = slowQueries.filter(q => q.command?.sort);
      if (sortOperations.length > 0) {
        recommendations.push('Consider adding indexes for sort operations.');
      }

      const regexQueries = slowQueries.filter(q => 
        JSON.stringify(q.command).includes('$regex')
      );
      if (regexQueries.length > 0) {
        recommendations.push('Consider using text indexes instead of regex queries for better performance.');
      }
    }

    // Analyze index usage
    const unusedIndexes = indexStats.filter(stat => stat.accesses.ops === 0);
    if (unusedIndexes.length > 0) {
      recommendations.push(`Found ${unusedIndexes.length} unused indexes. Consider removing them to improve write performance.`);
    }

    // Check for missing indexes on frequently queried fields
    const frequentFields = ['worksheetId', 'status', 'position', 'schoolId'];
    const existingIndexes = indexStats.map(stat => Object.keys(stat.key)).flat();
    
    for (const field of frequentFields) {
      if (!existingIndexes.includes(field)) {
        recommendations.push(`Consider adding an index on '${field}' field for better query performance.`);
      }
    }

    if (recommendations.length === 0) {
      recommendations.push('Database performance appears optimal. No immediate optimizations needed.');
    }

    return recommendations;
  }

  /**
   * Optimize aggregation pipelines for better performance
   */
  optimizeAggregationPipeline(pipeline: any[]): any[] {
    const optimizedPipeline = [...pipeline];

    // Move $match stages to the beginning
    const matchStages = optimizedPipeline.filter(stage => stage.$match);
    const otherStages = optimizedPipeline.filter(stage => !stage.$match);
    
    // Combine multiple $match stages
    if (matchStages.length > 1) {
      const combinedMatch = matchStages.reduce((combined, stage) => {
        return { $match: { ...combined.$match, ...stage.$match } };
      }, { $match: {} });
      
      return [combinedMatch, ...otherStages];
    }

    // Add index hints for complex queries
    if (optimizedPipeline.some(stage => stage.$sort)) {
      // Add hint for sort operations
      optimizedPipeline.unshift({ $hint: { worksheetId: 1, position: 1 } });
    }

    return optimizedPipeline;
  }

  /**
   * Get database performance metrics
   */
  async getPerformanceMetrics(): Promise<any> {
    try {
      const db = this.worksheetQuestionModel.db.db;

      // Get server status
      const serverStatus = await db?.admin().serverStatus() || {};

      // Get database stats
      const dbStats = await db?.stats() || {};

      // Get collection stats using collStats command
      const collStats = await db?.command({ collStats: 'worksheet_questions' }).catch(() => ({})) || {};

      return {
        connections: {
          current: serverStatus.connections.current,
          available: serverStatus.connections.available,
          totalCreated: serverStatus.connections.totalCreated
        },
        operations: {
          insert: serverStatus.opcounters.insert,
          query: serverStatus.opcounters.query,
          update: serverStatus.opcounters.update,
          delete: serverStatus.opcounters.delete
        },
        memory: {
          resident: serverStatus.mem.resident,
          virtual: serverStatus.mem.virtual,
          mapped: serverStatus.mem.mapped
        },
        database: {
          collections: dbStats.collections,
          objects: dbStats.objects,
          avgObjSize: dbStats.avgObjSize,
          dataSize: dbStats.dataSize,
          storageSize: dbStats.storageSize,
          indexSize: dbStats.indexSize
        },
        collection: {
          count: (collStats as any)?.count || 0,
          size: (collStats as any)?.size || 0,
          avgObjSize: (collStats as any)?.avgObjSize || 0,
          storageSize: (collStats as any)?.storageSize || 0,
          totalIndexSize: (collStats as any)?.totalIndexSize || 0,
          indexSizes: (collStats as any)?.indexSizes || {}
        }
      };
    } catch (error) {
      this.logger.error('Failed to get performance metrics', error);
      return { error: 'Failed to get performance metrics' };
    }
  }
}
