import { Injectable, Logger } from '@nestjs/common';
import { Registry, Histogram, Counter, Gauge, collectDefaultMetrics } from 'prom-client';

/**
 * Service for collecting and managing performance metrics for worksheet question operations
 * Extends existing monitoring patterns from QuestionPoolMetricsService
 */
@Injectable()
export class WorksheetQuestionMetricsService {
  private readonly logger = new Logger(WorksheetQuestionMetricsService.name);
  private readonly registry: Registry;

  // API Performance Metrics
  private readonly apiDurationHistogram: Histogram<string>;
  private readonly apiRequestCounter: Counter<string>;
  private readonly apiErrorCounter: Counter<string>;

  // Database Performance Metrics
  private readonly dbQueryDurationHistogram: Histogram<string>;
  private readonly dbConnectionsGauge: Gauge<string>;

  // Cache Performance Metrics
  private readonly cacheHitCounter: Counter<string>;
  private readonly cacheMissCounter: Counter<string>;
  private readonly cacheOperationDurationHistogram: Histogram<string>;

  // Memory and Resource Metrics
  private readonly memoryUsageGauge: Gauge<string>;
  private readonly activeOperationsGauge: Gauge<string>;
  private readonly questionCountGauge: Gauge<string>;

  // Real-time Collaboration Metrics
  private readonly collaborationEventsCounter: Counter<string>;
  private readonly activeCollaboratorsGauge: Gauge<string>;

  constructor() {
    // Create a new registry for this service
    this.registry = new Registry();

    // Collect default Node.js metrics only in production
    if (process.env.NODE_ENV !== 'test') {
      collectDefaultMetrics({ register: this.registry });
    }

    // Initialize API performance metrics
    this.apiDurationHistogram = new Histogram({
      name: 'worksheet_question_api_duration_seconds',
      help: 'Duration of worksheet question API operations in seconds',
      labelNames: ['method', 'endpoint', 'status_code', 'user_role'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
      registers: [this.registry],
    });

    this.apiRequestCounter = new Counter({
      name: 'worksheet_question_api_requests_total',
      help: 'Total number of worksheet question API requests',
      labelNames: ['method', 'endpoint', 'status_code', 'user_role'],
      registers: [this.registry],
    });

    this.apiErrorCounter = new Counter({
      name: 'worksheet_question_api_errors_total',
      help: 'Total number of worksheet question API errors',
      labelNames: ['method', 'endpoint', 'error_type', 'user_role'],
      registers: [this.registry],
    });

    // Initialize database performance metrics
    this.dbQueryDurationHistogram = new Histogram({
      name: 'worksheet_question_db_query_duration_seconds',
      help: 'Duration of database queries for worksheet questions in seconds',
      labelNames: ['operation', 'collection', 'query_type'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
      registers: [this.registry],
    });

    this.dbConnectionsGauge = new Gauge({
      name: 'worksheet_question_db_connections_active',
      help: 'Number of active database connections for worksheet questions',
      registers: [this.registry],
    });

    // Initialize cache performance metrics
    this.cacheHitCounter = new Counter({
      name: 'worksheet_question_cache_hits_total',
      help: 'Total number of cache hits for worksheet questions',
      labelNames: ['cache_type', 'operation'],
      registers: [this.registry],
    });

    this.cacheMissCounter = new Counter({
      name: 'worksheet_question_cache_misses_total',
      help: 'Total number of cache misses for worksheet questions',
      labelNames: ['cache_type', 'operation'],
      registers: [this.registry],
    });

    this.cacheOperationDurationHistogram = new Histogram({
      name: 'worksheet_question_cache_operation_duration_seconds',
      help: 'Duration of cache operations for worksheet questions in seconds',
      labelNames: ['cache_type', 'operation', 'result'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
      registers: [this.registry],
    });

    // Initialize memory and resource metrics
    this.memoryUsageGauge = new Gauge({
      name: 'worksheet_question_memory_usage_bytes',
      help: 'Memory usage for worksheet question operations in bytes',
      labelNames: ['operation_type'],
      registers: [this.registry],
    });

    this.activeOperationsGauge = new Gauge({
      name: 'worksheet_question_active_operations',
      help: 'Number of active worksheet question operations',
      labelNames: ['operation_type'],
      registers: [this.registry],
    });

    this.questionCountGauge = new Gauge({
      name: 'worksheet_question_count',
      help: 'Number of questions in worksheets',
      labelNames: ['worksheet_id', 'school_id'],
      registers: [this.registry],
    });

    // Initialize collaboration metrics
    this.collaborationEventsCounter = new Counter({
      name: 'worksheet_question_collaboration_events_total',
      help: 'Total number of collaboration events for worksheet questions',
      labelNames: ['event_type', 'worksheet_id'],
      registers: [this.registry],
    });

    this.activeCollaboratorsGauge = new Gauge({
      name: 'worksheet_question_active_collaborators',
      help: 'Number of active collaborators on worksheet questions',
      labelNames: ['worksheet_id'],
      registers: [this.registry],
    });

    this.logger.log('Worksheet Question Metrics Service initialized');
  }

  /**
   * Record API operation duration and request count
   */
  recordApiOperation(
    duration: number,
    method: string,
    endpoint: string,
    statusCode: number,
    userRole: string
  ): void {
    const labels = { method, endpoint, status_code: statusCode.toString(), user_role: userRole };
    
    this.apiDurationHistogram.observe(labels, duration);
    this.apiRequestCounter.inc(labels);
  }

  /**
   * Record API error
   */
  recordApiError(
    method: string,
    endpoint: string,
    errorType: string,
    userRole: string
  ): void {
    this.apiErrorCounter.inc({ method, endpoint, error_type: errorType, user_role: userRole });
  }

  /**
   * Record database query duration
   */
  recordDbQuery(
    duration: number,
    operation: string,
    collection: string,
    queryType: string
  ): void {
    this.dbQueryDurationHistogram.observe({ operation, collection, query_type: queryType }, duration);
  }

  /**
   * Update database connections count
   */
  updateDbConnections(activeConnections: number): void {
    this.dbConnectionsGauge.set(activeConnections);
  }

  /**
   * Record cache hit
   */
  recordCacheHit(cacheType: string, operation: string): void {
    this.cacheHitCounter.inc({ cache_type: cacheType, operation });
  }

  /**
   * Record cache miss
   */
  recordCacheMiss(cacheType: string, operation: string): void {
    this.cacheMissCounter.inc({ cache_type: cacheType, operation });
  }

  /**
   * Record cache operation duration
   */
  recordCacheOperation(
    duration: number,
    cacheType: string,
    operation: string,
    result: 'hit' | 'miss' | 'error'
  ): void {
    this.cacheOperationDurationHistogram.observe(
      { cache_type: cacheType, operation, result },
      duration
    );
  }

  /**
   * Update memory usage
   */
  updateMemoryUsage(operationType: string, memoryBytes: number): void {
    this.memoryUsageGauge.set({ operation_type: operationType }, memoryBytes);
  }

  /**
   * Update active operations count
   */
  updateActiveOperations(operationType: string, count: number): void {
    this.activeOperationsGauge.set({ operation_type: operationType }, count);
  }

  /**
   * Update question count for a worksheet
   */
  updateQuestionCount(worksheetId: string, schoolId: string, count: number): void {
    this.questionCountGauge.set({ worksheet_id: worksheetId, school_id: schoolId }, count);
  }

  /**
   * Record collaboration event
   */
  recordCollaborationEvent(eventType: string, worksheetId: string): void {
    this.collaborationEventsCounter.inc({ event_type: eventType, worksheet_id: worksheetId });
  }

  /**
   * Update active collaborators count
   */
  updateActiveCollaborators(worksheetId: string, count: number): void {
    this.activeCollaboratorsGauge.set({ worksheet_id: worksheetId }, count);
  }

  /**
   * Get the Prometheus registry for metrics endpoint
   */
  getRegistry(): Registry {
    return this.registry;
  }

  /**
   * Get current metrics as a string (for Prometheus scraping)
   */
  async getMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  /**
   * Get performance summary for dashboard
   */
  async getPerformanceSummary(): Promise<any> {
    const metrics = await this.registry.getMetricsAsJSON();
    
    // Extract key performance indicators
    const summary = {
      totalApiRequests: this.getTotalFromMetrics(metrics, 'worksheet_question_api_requests_total'),
      totalApiErrors: this.getTotalFromMetrics(metrics, 'worksheet_question_api_errors_total'),
      averageApiDuration: this.getAverageFromHistogram(metrics, 'worksheet_question_api_duration_seconds'),
      cacheHitRate: this.calculateCacheHitRate(metrics),
      activeOperations: this.getGaugeValue(metrics, 'worksheet_question_active_operations'),
      memoryUsage: this.getGaugeValue(metrics, 'worksheet_question_memory_usage_bytes'),
      timestamp: new Date().toISOString(),
    };

    return summary;
  }

  /**
   * Helper method to extract total from counter metrics
   */
  private getTotalFromMetrics(metrics: any[], metricName: string): number {
    const metric = metrics.find(m => m.name === metricName);
    return metric ? metric.values.reduce((sum: number, v: any) => sum + v.value, 0) : 0;
  }

  /**
   * Helper method to calculate average from histogram metrics
   */
  private getAverageFromHistogram(metrics: any[], metricName: string): number {
    const metric = metrics.find(m => m.name === metricName);
    if (!metric) return 0;

    const sumMetric = metrics.find(m => m.name === `${metricName}_sum`);
    const countMetric = metrics.find(m => m.name === `${metricName}_count`);

    if (!sumMetric || !countMetric) return 0;

    const totalSum = sumMetric.values.reduce((sum: number, v: any) => sum + v.value, 0);
    const totalCount = countMetric.values.reduce((sum: number, v: any) => sum + v.value, 0);

    return totalCount > 0 ? totalSum / totalCount : 0;
  }

  /**
   * Helper method to calculate cache hit rate
   */
  private calculateCacheHitRate(metrics: any[]): number {
    const hits = this.getTotalFromMetrics(metrics, 'worksheet_question_cache_hits_total');
    const misses = this.getTotalFromMetrics(metrics, 'worksheet_question_cache_misses_total');
    const total = hits + misses;

    return total > 0 ? (hits / total) * 100 : 0;
  }

  /**
   * Helper method to get gauge value
   */
  private getGaugeValue(metrics: any[], metricName: string): number {
    const metric = metrics.find(m => m.name === metricName);
    return metric ? metric.values.reduce((sum: number, v: any) => sum + v.value, 0) : 0;
  }
}
