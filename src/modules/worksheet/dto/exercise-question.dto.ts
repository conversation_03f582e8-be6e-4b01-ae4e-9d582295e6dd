import { 
  IsString, 
  IsArray, 
  IsOptional, 
  IsEnum, 
  IsBoolean, 
  IsNumber, 
  IsDate, 
  IsUUID,
  ArrayMinSize, 
  ArrayMaxSize, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  ValidateNested,
  IsUrl,
  IsNotEmpty
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { 
  EQuestionType, 
  EQuestionDifficulty, 
  EQuestionStatus,
  IQuestionMedia,
  IQuestionMetadata
} from '../../../shared/interfaces/exercise-question.interface';

/**
 * DTO for question media validation
 */
export class QuestionMediaDto implements IQuestionMedia {
  @ApiPropertyOptional({ description: 'Image URL' })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiPropertyOptional({ description: 'Image generation prompt' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  imagePrompt?: string;

  @ApiPropertyOptional({ description: 'Audio URL' })
  @IsOptional()
  @IsUrl()
  audioUrl?: string;

  @ApiPropertyOptional({ description: 'Video URL' })
  @IsOptional()
  @IsUrl()
  videoUrl?: string;

  @ApiPropertyOptional({ description: 'Attachment URLs', type: [String] })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  attachmentUrls?: string[];
}

/**
 * DTO for question metadata validation
 */
export class QuestionMetadataDto implements IQuestionMetadata {
  @ApiPropertyOptional({ description: 'Question tags', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(20)
  tags?: string[];

  @ApiPropertyOptional({ description: 'Keywords for search', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(50)
  keywords?: string[];

  @ApiPropertyOptional({ description: 'Estimated time in minutes' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(180)
  estimatedTimeMinutes?: number;

  @ApiPropertyOptional({ description: 'Cognitive level (Bloom\'s taxonomy)' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  cognitiveLevel?: string;

  @ApiPropertyOptional({ description: 'Learning objectives', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(10)
  learningObjectives?: string[];

  @ApiPropertyOptional({ description: 'Prerequisites', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(10)
  prerequisites?: string[];

  @ApiPropertyOptional({ description: 'Hints for students', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(5)
  hints?: string[];

  @ApiPropertyOptional({ description: 'Reference materials', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMaxSize(10)
  references?: string[];

  @ApiPropertyOptional({ description: 'Author notes' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  authorNotes?: string;

  @ApiPropertyOptional({ description: 'Review notes' })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  reviewNotes?: string;

  @ApiPropertyOptional({ description: 'Last review date' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  lastReviewDate?: Date;

  @ApiPropertyOptional({ description: 'Next review date' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  nextReviewDate?: Date;
}

/**
 * DTO for creating a new exercise question
 */
export class CreateExerciseQuestionDto {
  @ApiProperty({
    description: 'Question type',
    enum: EQuestionType,
    enumName: 'EQuestionType',
    example: EQuestionType.MULTIPLE_CHOICE
  })
  @IsEnum(EQuestionType)
  type: EQuestionType;

  @ApiProperty({ 
    description: 'Question content/text',
    example: 'What is the capital of France?'
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  @MaxLength(2000)
  content: string;

  @ApiProperty({ 
    description: 'Answer options',
    type: [String],
    example: ['Paris', 'London', 'Berlin', 'Madrid']
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(10)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  options: string[];

  @ApiProperty({ 
    description: 'Correct answer(s)',
    type: [String],
    example: ['Paris']
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  answer: string[];

  @ApiProperty({ 
    description: 'Explanation of the correct answer',
    example: 'Paris is the capital and largest city of France.'
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  @MaxLength(1000)
  explain: string;

  @ApiPropertyOptional({ description: 'Subject' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  subject?: string;

  @ApiPropertyOptional({ description: 'Parent subject' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  parentSubject?: string;

  @ApiPropertyOptional({ description: 'Child subject' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  childSubject?: string;

  @ApiPropertyOptional({ description: 'Topic' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  topic?: string;

  @ApiPropertyOptional({ description: 'Subtopic' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  subtopic?: string;

  @ApiPropertyOptional({ description: 'Grade level' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  grade?: string;

  @ApiPropertyOptional({ 
    description: 'Question difficulty',
    enum: EQuestionDifficulty
  })
  @IsOptional()
  @IsEnum(EQuestionDifficulty)
  difficulty?: EQuestionDifficulty;

  @ApiPropertyOptional({ 
    description: 'Media attachments',
    type: QuestionMediaDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => QuestionMediaDto)
  media?: QuestionMediaDto;

  @ApiPropertyOptional({ description: 'Legacy image prompt' })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  imagePrompt?: string;

  @ApiPropertyOptional({ description: 'Legacy image URL' })
  @IsOptional()
  @IsUrl()
  imageUrl?: string;

  @ApiPropertyOptional({ 
    description: 'Question status',
    enum: EQuestionStatus,
    default: EQuestionStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(EQuestionStatus)
  status?: EQuestionStatus;

  @ApiPropertyOptional({ description: 'Is question public' })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ 
    description: 'Question metadata',
    type: QuestionMetadataDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => QuestionMetadataDto)
  metadata?: QuestionMetadataDto;
}

/**
 * DTO for updating an exercise question
 */
export class UpdateExerciseQuestionDto extends PartialType(CreateExerciseQuestionDto) {
  @ApiProperty({ description: 'Question ID' })
  @IsString()
  @IsNotEmpty()
  id: string;
}

/**
 * DTO for worksheet question position/order
 */
export class QuestionPositionDto {
  @ApiProperty({ description: 'Question position in worksheet' })
  @IsNumber()
  @Min(1)
  position: number;

  @ApiPropertyOptional({ description: 'Points allocated for this question' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  points?: number;
}

/**
 * DTO for bulk question operations
 */
export class BulkQuestionOperationDto {
  @ApiProperty({ 
    description: 'Question IDs to operate on',
    type: [String]
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50)
  @IsString({ each: true })
  questionIds: string[];

  @ApiProperty({ 
    description: 'Operation type',
    enum: ['delete', 'reorder', 'update_status', 'move']
  })
  @IsEnum(['delete', 'reorder', 'update_status', 'move'])
  operation: 'delete' | 'reorder' | 'update_status' | 'move';

  @ApiPropertyOptional({ 
    description: 'Additional operation data',
    example: { newStatus: 'inactive', targetWorksheetId: 'worksheet-123' }
  })
  @IsOptional()
  operationData?: Record<string, any>;
}

/**
 * DTO for question search/filter
 */
export class QuestionSearchDto {
  @ApiPropertyOptional({ description: 'Question type filter', enum: EQuestionType })
  @IsOptional()
  @IsEnum(EQuestionType)
  type?: EQuestionType;

  @ApiPropertyOptional({ description: 'Subject filter' })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiPropertyOptional({ description: 'Topic filter' })
  @IsOptional()
  @IsString()
  topic?: string;

  @ApiPropertyOptional({ description: 'Grade filter' })
  @IsOptional()
  @IsString()
  grade?: string;

  @ApiPropertyOptional({ description: 'Difficulty filter', enum: EQuestionDifficulty })
  @IsOptional()
  @IsEnum(EQuestionDifficulty)
  difficulty?: EQuestionDifficulty;

  @ApiPropertyOptional({ description: 'Status filter', enum: EQuestionStatus })
  @IsOptional()
  @IsEnum(EQuestionStatus)
  status?: EQuestionStatus;

  @ApiPropertyOptional({ description: 'Search text in content' })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  searchText?: string;

  @ApiPropertyOptional({ description: 'Tags filter', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Page number for pagination' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @ApiPropertyOptional({ description: 'Items per page' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value))
  limit?: number;
}
