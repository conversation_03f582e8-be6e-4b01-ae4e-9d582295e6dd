import { IsOptional, IsUUID } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { BasePaginationRequest } from 'src/core/entities/base-pagination-request';

export class ListWorksheetDto extends BasePaginationRequest {
  @ApiPropertyOptional({
    description: 'Filter worksheets by school ID. Admin users can specify any school ID to filter worksheets. Non-admin users can only access worksheets from their own school. If not provided, non-admin users will see only their school\'s worksheets, while admin users will see all worksheets.',
    example: '123e4567-e89b-12d3-a456-************',
    type: String,
  })
  @IsOptional()
  @IsUUID('4', { message: 'schoolId must be a valid UUID' })
  schoolId?: string;
}
