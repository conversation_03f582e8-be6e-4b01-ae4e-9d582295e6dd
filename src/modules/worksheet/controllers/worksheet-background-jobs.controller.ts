import { Controller, Get, Post, Delete, Body, Param, Query, UseGuards, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { Roles } from '../../auth/decorators/role.decorator';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { EUserRole } from '../../user/dto/create-user.dto';
import { ActiveUser } from '../../auth/decorators/active-user.decorator';
import { User } from '../../user/entities/user.entity';
import { WorksheetQuestionBackgroundService } from '../services/worksheet-question-background.service';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';

/**
 * Controller for managing background job operations for worksheet questions
 */
@ApiTags('Worksheet Background Jobs')
@Controller('worksheets/jobs')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class WorksheetBackgroundJobsController {
  constructor(
    private readonly backgroundService: WorksheetQuestionBackgroundService
  ) {}

  /**
   * Queue bulk add operation
   */
  @Post('bulk-add')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Queue bulk add operation for worksheet questions',
    description: 'Queues a background job to add multiple questions to a worksheet'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        worksheetId: { type: 'string', description: 'ID of the worksheet' },
        questions: { 
          type: 'array', 
          items: { type: 'object' },
          description: 'Array of questions to add'
        }
      },
      required: ['worksheetId', 'questions']
    }
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Bulk add job queued successfully',
    schema: {
      type: 'object',
      properties: {
        jobId: { type: 'string', description: 'ID of the queued job' },
        message: { type: 'string' },
        estimatedDuration: { type: 'string' }
      }
    }
  })
  async queueBulkAdd(
    @Body() body: { worksheetId: string; questions: IExerciseQuestion[] },
    @ActiveUser() user: User
  ) {
    const { worksheetId, questions } = body;
    
    const jobId = await this.backgroundService.queueBulkAdd(
      worksheetId,
      questions,
      user.id,
      user.role,
      user.schoolId || undefined
    );

    return {
      jobId,
      message: `Queued bulk add operation for ${questions.length} questions`,
      estimatedDuration: this.estimateDuration(questions.length, 'add'),
      worksheetId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Queue bulk update operation
   */
  @Post('bulk-update')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Queue bulk update operation for worksheet questions',
    description: 'Queues a background job to update multiple questions in a worksheet'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        worksheetId: { type: 'string', description: 'ID of the worksheet' },
        updates: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              questionId: { type: 'string' },
              updates: { type: 'object' }
            }
          },
          description: 'Array of question updates'
        }
      },
      required: ['worksheetId', 'updates']
    }
  })
  @ApiResponse({ status: 201, description: 'Bulk update job queued successfully' })
  async queueBulkUpdate(
    @Body() body: { 
      worksheetId: string; 
      updates: Array<{ questionId: string; updates: Partial<IExerciseQuestion> }> 
    },
    @ActiveUser() user: User
  ) {
    const { worksheetId, updates } = body;
    
    const jobId = await this.backgroundService.queueBulkUpdate(
      worksheetId,
      updates,
      user.id,
      user.role,
      user.schoolId || undefined
    );

    return {
      jobId,
      message: `Queued bulk update operation for ${updates.length} questions`,
      estimatedDuration: this.estimateDuration(updates.length, 'update'),
      worksheetId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Queue bulk delete operation
   */
  @Post('bulk-delete')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Queue bulk delete operation for worksheet questions',
    description: 'Queues a background job to delete multiple questions from a worksheet'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        worksheetId: { type: 'string', description: 'ID of the worksheet' },
        questionIds: { 
          type: 'array', 
          items: { type: 'string' },
          description: 'Array of question IDs to delete'
        }
      },
      required: ['worksheetId', 'questionIds']
    }
  })
  @ApiResponse({ status: 201, description: 'Bulk delete job queued successfully' })
  async queueBulkDelete(
    @Body() body: { worksheetId: string; questionIds: string[] },
    @ActiveUser() user: User
  ) {
    const { worksheetId, questionIds } = body;
    
    const jobId = await this.backgroundService.queueBulkDelete(
      worksheetId,
      questionIds,
      user.id,
      user.role,
      user.schoolId || undefined
    );

    return {
      jobId,
      message: `Queued bulk delete operation for ${questionIds.length} questions`,
      estimatedDuration: this.estimateDuration(questionIds.length, 'delete'),
      worksheetId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Queue worksheet export operation
   */
  @Post('export')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Queue worksheet export operation',
    description: 'Queues a background job to export a worksheet in the specified format'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        worksheetId: { type: 'string', description: 'ID of the worksheet' },
        format: { 
          type: 'string', 
          enum: ['pdf', 'docx', 'json', 'csv'],
          description: 'Export format'
        },
        options: { 
          type: 'object',
          description: 'Export options'
        }
      },
      required: ['worksheetId', 'format']
    }
  })
  @ApiResponse({ status: 201, description: 'Export job queued successfully' })
  async queueExport(
    @Body() body: { 
      worksheetId: string; 
      format: 'pdf' | 'docx' | 'json' | 'csv';
      options?: any;
    },
    @ActiveUser() user: User
  ) {
    const { worksheetId, format, options = {} } = body;
    
    const jobId = await this.backgroundService.queueWorksheetExport(
      worksheetId,
      format,
      options,
      user.id,
      user.role,
      user.schoolId || undefined
    );

    return {
      jobId,
      message: `Queued worksheet export in ${format} format`,
      estimatedDuration: this.estimateDuration(1, 'export'),
      worksheetId,
      format,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get job progress
   */
  @Get(':jobId/progress')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Get job progress',
    description: 'Returns the current progress and status of a background job'
  })
  @ApiParam({ name: 'jobId', description: 'ID of the job' })
  @ApiResponse({ 
    status: 200, 
    description: 'Job progress retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        jobId: { type: 'string' },
        type: { type: 'string' },
        status: { type: 'string', enum: ['queued', 'active', 'completed', 'failed', 'delayed'] },
        progress: { type: 'number' },
        total: { type: 'number' },
        currentItem: { type: 'string' },
        startedAt: { type: 'string', format: 'date-time' },
        completedAt: { type: 'string', format: 'date-time' },
        error: { type: 'string' },
        result: { type: 'object' }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Job not found' })
  async getJobProgress(@Param('jobId') jobId: string) {
    const progress = await this.backgroundService.getJobProgress(jobId);
    
    if (!progress) {
      throw new Error('Job not found');
    }

    return progress;
  }

  /**
   * Get user's active jobs
   */
  @Get('active')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Get user\'s active jobs',
    description: 'Returns all active background jobs for the current user'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Active jobs retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        jobs: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              jobId: { type: 'string' },
              type: { type: 'string' },
              status: { type: 'string' },
              progress: { type: 'number' },
              total: { type: 'number' }
            }
          }
        },
        totalJobs: { type: 'number' }
      }
    }
  })
  async getUserActiveJobs(@ActiveUser() user: User) {
    const jobs = await this.backgroundService.getUserActiveJobs(user.id);

    return {
      jobs,
      totalJobs: jobs.length,
      userId: user.id,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Cancel a job
   */
  @Delete(':jobId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Cancel a background job',
    description: 'Cancels a queued or active background job'
  })
  @ApiParam({ name: 'jobId', description: 'ID of the job to cancel' })
  @ApiResponse({ status: 204, description: 'Job cancelled successfully' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  @ApiResponse({ status: 403, description: 'Unauthorized to cancel this job' })
  async cancelJob(
    @Param('jobId') jobId: string,
    @ActiveUser() user: User
  ) {
    const cancelled = await this.backgroundService.cancelJob(jobId, user.id);
    
    if (!cancelled) {
      throw new Error('Failed to cancel job');
    }

    return {
      message: `Job ${jobId} cancelled successfully`,
      cancelledBy: user.id,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get queue statistics (Admin only)
   */
  @Get('statistics')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ 
    summary: 'Get queue statistics',
    description: 'Returns statistics about the background job queue (Admin only)'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Queue statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        waiting: { type: 'number', description: 'Number of waiting jobs' },
        active: { type: 'number', description: 'Number of active jobs' },
        completed: { type: 'number', description: 'Number of completed jobs' },
        failed: { type: 'number', description: 'Number of failed jobs' },
        delayed: { type: 'number', description: 'Number of delayed jobs' },
        total: { type: 'number', description: 'Total number of jobs' }
      }
    }
  })
  async getQueueStatistics() {
    return this.backgroundService.getQueueStatistics();
  }

  /**
   * Estimate job duration based on operation type and size
   */
  private estimateDuration(itemCount: number, operation: string): string {
    const baseTime = {
      add: 100, // 100ms per question
      update: 50, // 50ms per question
      delete: 30, // 30ms per question
      export: 5000, // 5 seconds base for export
    };

    const estimatedMs = (baseTime[operation] || 100) * itemCount;
    
    if (estimatedMs < 1000) {
      return `${estimatedMs}ms`;
    } else if (estimatedMs < 60000) {
      return `${Math.round(estimatedMs / 1000)}s`;
    } else {
      return `${Math.round(estimatedMs / 60000)}m`;
    }
  }
}
