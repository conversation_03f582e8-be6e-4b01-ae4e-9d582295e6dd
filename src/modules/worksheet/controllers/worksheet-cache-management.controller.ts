import { Controller, Get, Post, Delete, Param, Query, UseGuards, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Roles } from '../../auth/decorators/role.decorator';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { EUserRole } from '../../user/dto/create-user.dto';
import { WorksheetRedisCacheService } from '../services/worksheet-redis-cache.service';
import { WorksheetQuestionEnhancedCacheService } from '../services/worksheet-question-enhanced-cache.service';
import { ActiveUser } from '../../auth/decorators/active-user.decorator';
import { User } from '../../user/entities/user.entity';

/**
 * Controller for managing worksheet question cache operations
 * Admin-only endpoints for cache monitoring and management
 */
@ApiTags('Worksheet Cache Management')
@Controller('worksheets/cache')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class WorksheetCacheManagementController {
  constructor(
    private readonly redisCacheService: WorksheetRedisCacheService,
    private readonly enhancedCacheService: WorksheetQuestionEnhancedCacheService
  ) {}

  /**
   * Get comprehensive cache statistics
   */
  @Get('statistics')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ 
    summary: 'Get comprehensive cache statistics',
    description: 'Returns detailed statistics about cache usage, hit ratios, and performance metrics'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Cache statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        redis: {
          type: 'object',
          properties: {
            questionMetadata: { type: 'number', description: 'Number of cached question metadata entries' },
            userPermissions: { type: 'number', description: 'Number of cached user permission entries' },
            worksheetSummaries: { type: 'number', description: 'Number of cached worksheet summaries' },
            popularWorksheets: { type: 'number', description: 'Number of cached popular worksheet lists' },
            collaborationStates: { type: 'number', description: 'Number of cached collaboration states' },
            totalKeys: { type: 'number', description: 'Total number of cache keys' },
            memoryUsage: { type: 'object', description: 'Redis memory usage information' },
            health: { type: 'object', description: 'Cache health status' }
          }
        },
        enhanced: {
          type: 'object',
          properties: {
            totalCachedWorksheets: { type: 'number' },
            totalMetadataEntries: { type: 'number' },
            totalPermissionEntries: { type: 'number' },
            cacheConfiguration: { type: 'object' }
          }
        },
        performance: {
          type: 'object',
          properties: {
            hitRate: { type: 'number', description: 'Overall cache hit rate percentage' },
            averageResponseTime: { type: 'number', description: 'Average cache response time in ms' }
          }
        }
      }
    }
  })
  async getCacheStatistics() {
    const [redisStats, enhancedStats] = await Promise.all([
      this.redisCacheService.getCacheStatistics(),
      this.enhancedCacheService.getCacheStatistics()
    ]);

    return {
      redis: redisStats,
      enhanced: enhancedStats,
      performance: {
        hitRate: this.calculateOverallHitRate(redisStats, enhancedStats),
        averageResponseTime: this.calculateAverageResponseTime()
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Warm cache for popular worksheets
   */
  @Post('warm')
  @Roles(EUserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Manually trigger cache warming',
    description: 'Triggers cache warming process for popular worksheets and frequently accessed data'
  })
  @ApiResponse({ status: 200, description: 'Cache warming initiated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async warmCache(@ActiveUser() user: User) {
    const startTime = Date.now();

    // Trigger cache warming for both services
    await Promise.all([
      this.redisCacheService.warmDistributedCache(),
      this.enhancedCacheService.warmPopularWorksheets()
    ]);

    const duration = Date.now() - startTime;

    return {
      message: 'Cache warming completed successfully',
      duration: `${duration}ms`,
      triggeredBy: user.id,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get cache health status
   */
  @Get('health')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ 
    summary: 'Get cache health status',
    description: 'Returns health status and diagnostics for all cache layers'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Cache health status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        overall: { type: 'string', enum: ['healthy', 'warning', 'critical'] },
        redis: { type: 'object', description: 'Redis cache health' },
        enhanced: { type: 'object', description: 'Enhanced cache health' },
        recommendations: { type: 'array', items: { type: 'string' } }
      }
    }
  })
  async getCacheHealth() {
    const [redisStats, enhancedStats] = await Promise.all([
      this.redisCacheService.getCacheStatistics(),
      this.enhancedCacheService.getCacheStatistics()
    ]);

    const health = this.assessCacheHealth(redisStats, enhancedStats);

    return {
      overall: health.status,
      redis: redisStats.health || { status: 'unknown' },
      enhanced: { status: 'operational' },
      recommendations: health.recommendations,
      lastChecked: new Date().toISOString()
    };
  }

  /**
   * Invalidate cache for a specific worksheet
   */
  @Delete('worksheet/:worksheetId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Invalidate cache for a specific worksheet',
    description: 'Removes all cached data related to a specific worksheet from all cache layers'
  })
  @ApiParam({ name: 'worksheetId', description: 'ID of the worksheet to invalidate cache for' })
  @ApiResponse({ status: 204, description: 'Cache invalidated successfully' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  async invalidateWorksheetCache(
    @Param('worksheetId') worksheetId: string,
    @ActiveUser() user: User
  ) {
    // Invalidate cache in both services
    await Promise.all([
      this.redisCacheService.invalidateWorksheetCache(worksheetId),
      this.enhancedCacheService.invalidateWorksheetCache(worksheetId)
    ]);

    return {
      message: `Cache invalidated for worksheet ${worksheetId}`,
      invalidatedBy: user.id,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get popular worksheets from cache
   */
  @Get('popular/:schoolId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER)
  @ApiOperation({ 
    summary: 'Get popular worksheets for a school',
    description: 'Returns cached list of popular worksheets for the specified school'
  })
  @ApiParam({ name: 'schoolId', description: 'ID of the school' })
  @ApiResponse({ 
    status: 200, 
    description: 'Popular worksheets retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        worksheets: { type: 'array', items: { type: 'object' } },
        cached: { type: 'boolean', description: 'Whether data was served from cache' },
        cacheAge: { type: 'string', description: 'Age of cached data' }
      }
    }
  })
  async getPopularWorksheets(
    @Param('schoolId') schoolId: string,
    @ActiveUser() user: User
  ) {
    // Check user access to school data
    if (user.role !== EUserRole.ADMIN && user.schoolId !== schoolId) {
      throw new Error('Access denied to school data');
    }

    const worksheets = await this.redisCacheService.getPopularWorksheets(schoolId);

    return {
      worksheets,
      cached: worksheets.length > 0,
      cacheAge: worksheets.length > 0 ? 'recent' : 'none',
      schoolId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get user's recent worksheets from cache
   */
  @Get('recent/:userId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Get user\'s recent worksheets from cache',
    description: 'Returns cached list of recently accessed worksheets for the specified user'
  })
  @ApiParam({ name: 'userId', description: 'ID of the user' })
  @ApiResponse({ 
    status: 200, 
    description: 'Recent worksheets retrieved successfully'
  })
  async getUserRecentWorksheets(
    @Param('userId') userId: string,
    @ActiveUser() user: User
  ) {
    // Check user access (users can only access their own data unless admin)
    if (user.role !== EUserRole.ADMIN && user.id !== userId) {
      throw new Error('Access denied to user data');
    }

    const worksheets = await this.redisCacheService.getUserRecentWorksheets(userId);

    return {
      worksheets,
      cached: worksheets.length > 0,
      userId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get collaboration state for a worksheet
   */
  @Get('collaboration/:worksheetId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ 
    summary: 'Get collaboration state for a worksheet',
    description: 'Returns real-time collaboration state including active users and locked questions'
  })
  @ApiParam({ name: 'worksheetId', description: 'ID of the worksheet' })
  @ApiResponse({ 
    status: 200, 
    description: 'Collaboration state retrieved successfully'
  })
  async getCollaborationState(@Param('worksheetId') worksheetId: string) {
    const state = await this.redisCacheService.getCollaborationState(worksheetId);

    return {
      collaborationState: state || {
        activeUsers: [],
        lockedQuestions: {},
        lastActivity: null
      },
      cached: !!state,
      worksheetId,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Clear all cache (emergency operation)
   */
  @Delete('clear-all')
  @Roles(EUserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Clear all worksheet cache (emergency operation)',
    description: 'Clears all cached data for worksheet questions. Use with caution as this will impact performance temporarily.'
  })
  @ApiResponse({ status: 200, description: 'All cache cleared successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async clearAllCache(@ActiveUser() user: User) {
    // This would implement a comprehensive cache clear
    // For now, we'll return a confirmation message
    
    return {
      message: 'Cache clear operation would be implemented here',
      warning: 'This operation would significantly impact performance',
      clearedBy: user.id,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Calculate overall hit rate from cache statistics
   */
  private calculateOverallHitRate(redisStats: any, enhancedStats: any): number {
    // This would calculate based on actual metrics
    // For now, return a placeholder
    return 75.5;
  }

  /**
   * Calculate average response time
   */
  private calculateAverageResponseTime(): number {
    // This would calculate based on actual metrics
    // For now, return a placeholder
    return 12.5;
  }

  /**
   * Assess overall cache health
   */
  private assessCacheHealth(redisStats: any, enhancedStats: any): { status: string; recommendations: string[] } {
    const recommendations: string[] = [];
    let status = 'healthy';

    // Check Redis health
    if (redisStats.error) {
      status = 'critical';
      recommendations.push('Redis cache is experiencing errors');
    }

    // Check memory usage
    if (redisStats.memoryUsage && redisStats.memoryUsage.used_memory > **********) { // 1GB
      status = status === 'healthy' ? 'warning' : status;
      recommendations.push('Redis memory usage is high, consider cache cleanup');
    }

    // Check key count
    if (redisStats.totalKeys > 100000) {
      status = status === 'healthy' ? 'warning' : status;
      recommendations.push('High number of cache keys, consider implementing LRU eviction');
    }

    if (recommendations.length === 0) {
      recommendations.push('Cache is operating optimally');
    }

    return { status, recommendations };
  }
}
