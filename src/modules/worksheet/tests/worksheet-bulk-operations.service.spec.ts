import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { WorksheetQuestionService, UserContext } from '../services/worksheet-question.service';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { WorksheetQuestionLockingService } from '../services/worksheet-question-locking.service';
import { WorksheetQuestionMetricsService } from '../services/worksheet-question-metrics.service';
import { WorksheetQuestionEnhancedCacheService } from '../services/worksheet-question-enhanced-cache.service';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto } from '../dto/worksheet-question.dto';

describe('WorksheetQuestionService - Bulk Operations', () => {
  let service: WorksheetQuestionService;
  let worksheetRepository: any;
  let worksheetQuestionModel: any;
  let auditService: WorksheetQuestionAuditService;
  let socketGateway: SocketGateway;
  let collaborationGateway: WorksheetQuestionCollaborationGateway;
  let lockingService: WorksheetQuestionLockingService;
  let metricsService: WorksheetQuestionMetricsService;
  let cacheService: WorksheetQuestionEnhancedCacheService;

  const mockUserContext: UserContext = {
    sub: 'user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-123'
  };

  const mockWorksheet: Worksheet = {
    id: 'worksheet-1',
    title: 'Test Worksheet',
    questions: [
      {
        id: 'question-1',
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is 2+2?',
        options: ['2', '3', '4', '5'],
        answer: ['4'],
        explain: '2+2 equals 4',
        audit: { version: 1, createdBy: 'user-123', createdAt: new Date() }
      }
    ],
    totalQuestions: 1,
    maxQuestions: 100,
    schoolId: 'school-123',
    createdBy: 'user-123'
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          }
        },
        {
          provide: getModelToken(WorksheetQuestionDocument.name),
          useValue: {
            findOneAndUpdate: jest.fn(),
            updateOne: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionAuditService,
          useValue: {
            logBulkQuestionOperation: jest.fn(),
          }
        },
        {
          provide: SocketGateway,
          useValue: {
            server: {
              to: jest.fn().mockReturnThis(),
              emit: jest.fn(),
            }
          }
        },
        {
          provide: WorksheetQuestionCollaborationGateway,
          useValue: {
            broadcastQuestionUpdate: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionLockingService,
          useValue: {
            canEditQuestion: jest.fn().mockResolvedValue(true),
          }
        },
        {
          provide: WorksheetQuestionMetricsService,
          useValue: {
            recordMetric: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionEnhancedCacheService,
          useValue: {
            cacheWorksheetQuestions: jest.fn(),
            invalidateWorksheetCache: jest.fn(),
          }
        }
      ],
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    worksheetRepository = module.get(getRepositoryToken(Worksheet));
    worksheetQuestionModel = module.get(getModelToken(WorksheetQuestionDocument.name));
    auditService = module.get<WorksheetQuestionAuditService>(WorksheetQuestionAuditService);
    socketGateway = module.get<SocketGateway>(SocketGateway);
    collaborationGateway = module.get<WorksheetQuestionCollaborationGateway>(WorksheetQuestionCollaborationGateway);
    lockingService = module.get<WorksheetQuestionLockingService>(WorksheetQuestionLockingService);
    metricsService = module.get<WorksheetQuestionMetricsService>(WorksheetQuestionMetricsService);
    cacheService = module.get<WorksheetQuestionEnhancedCacheService>(WorksheetQuestionEnhancedCacheService);
  });

  describe('bulkAddQuestions', () => {
    it('should successfully add multiple questions', async () => {
      const questions: AddQuestionToWorksheetDto[] = [
        {
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'What is 3+3?',
          options: ['5', '6', '7', '8'],
          answer: ['6'],
          explain: '3+3 equals 6',
          difficulty: EQuestionDifficulty.EASY
        },
        {
          type: EQuestionType.TRUE_FALSE,
          content: 'The sky is blue',
          options: ['True', 'False'],
          answer: ['True'],
          explain: 'The sky appears blue due to light scattering',
          difficulty: EQuestionDifficulty.EASY
        }
      ];

      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);
      worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});

      const result = await service.bulkAddQuestions(
        'worksheet-1',
        questions,
        mockUserContext,
        { reason: 'Adding test questions' }
      );

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(2);
      expect(result.failureCount).toBe(0);
      expect(result.totalCount).toBe(2);
      expect(result.successes).toHaveLength(2);
      expect(result.failures).toHaveLength(0);

      expect(worksheetRepository.save).toHaveBeenCalled();
      expect(auditService.logBulkQuestionOperation).toHaveBeenCalledWith(
        'worksheet-1',
        'bulk_add',
        mockUserContext,
        expect.objectContaining({
          successCount: 2,
          failureCount: 0,
          reason: 'Adding test questions'
        })
      );
    });

    it('should reject when question limit would be exceeded', async () => {
      const worksheetAtLimit = {
        ...mockWorksheet,
        questions: Array(100).fill(mockWorksheet.questions[0]),
        totalQuestions: 100
      };

      worksheetRepository.findOne.mockResolvedValue(worksheetAtLimit);

      const questions: AddQuestionToWorksheetDto[] = [
        {
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'What is 3+3?',
          options: ['5', '6', '7', '8'],
          answer: ['6'],
          explain: '3+3 equals 6',
          difficulty: EQuestionDifficulty.EASY
        }
      ];

      await expect(
        service.bulkAddQuestions('worksheet-1', questions, mockUserContext)
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle partial failures gracefully', async () => {
      const questions: AddQuestionToWorksheetDto[] = [
        {
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'Valid question',
          options: ['A', 'B', 'C', 'D'],
          answer: ['A'],
          explain: 'Valid explanation',
          difficulty: EQuestionDifficulty.EASY
        },
        {
          type: EQuestionType.MULTIPLE_CHOICE,
          content: '', // This will cause validation to fail
          options: ['A', 'B'],
          answer: ['A'],
          explain: 'Invalid question',
          difficulty: EQuestionDifficulty.EASY
        }
      ];

      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);
      worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});

      // Mock the createQuestion method to throw for the second question
      const originalCreateQuestion = service['createQuestion'];
      jest.spyOn(service as any, 'createQuestion')
        .mockImplementationOnce(originalCreateQuestion)
        .mockRejectedValueOnce(new Error('Question content cannot be empty'));

      const result = await service.bulkAddQuestions(
        'worksheet-1',
        questions,
        mockUserContext
      );

      expect(result.success).toBe(false);
      expect(result.successCount).toBe(1);
      expect(result.failureCount).toBe(1);
      expect(result.failures).toHaveLength(1);
      expect(result.failures[0].error).toBe('Question content cannot be empty');
    });
  });

  describe('bulkRemoveQuestions', () => {
    it('should successfully remove multiple questions', async () => {
      const worksheetWithMultipleQuestions = {
        ...mockWorksheet,
        questions: [
          mockWorksheet.questions[0],
          {
            id: 'question-2',
            type: EQuestionType.TRUE_FALSE,
            content: 'The sky is blue',
            options: ['True', 'False'],
            answer: ['True'],
            explain: 'The sky appears blue',
            audit: { version: 1 }
          },
          {
            id: 'question-3',
            type: EQuestionType.MULTIPLE_CHOICE,
            content: 'What is 5+5?',
            options: ['8', '9', '10', '11'],
            answer: ['10'],
            explain: '5+5 equals 10',
            audit: { version: 1 }
          }
        ],
        totalQuestions: 3
      };

      worksheetRepository.findOne.mockResolvedValue(worksheetWithMultipleQuestions);
      worksheetRepository.save.mockResolvedValue(worksheetWithMultipleQuestions);
      worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});

      const result = await service.bulkRemoveQuestions(
        'worksheet-1',
        ['question-2', 'question-3'],
        mockUserContext,
        { reason: 'Removing test questions' }
      );

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(2);
      expect(result.failureCount).toBe(0);
      expect(result.successes).toEqual(['question-2', 'question-3']);

      expect(worksheetRepository.save).toHaveBeenCalled();
      expect(auditService.logBulkQuestionOperation).toHaveBeenCalledWith(
        'worksheet-1',
        'bulk_remove',
        mockUserContext,
        expect.objectContaining({
          successCount: 2,
          failureCount: 0,
          reason: 'Removing test questions'
        })
      );
    });

    it('should reject when removal would violate minimum questions', async () => {
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);

      await expect(
        service.bulkRemoveQuestions(
          'worksheet-1',
          ['question-1'], // Only question in worksheet
          mockUserContext
        )
      ).rejects.toThrow(BadRequestException);
    });

    it('should handle partial failures when some questions are not found', async () => {
      const worksheetWithMultipleQuestions = {
        ...mockWorksheet,
        questions: [
          mockWorksheet.questions[0],
          {
            id: 'question-2',
            type: EQuestionType.TRUE_FALSE,
            content: 'The sky is blue',
            options: ['True', 'False'],
            answer: ['True'],
            explain: 'The sky appears blue',
            audit: { version: 1 }
          }
        ],
        totalQuestions: 2
      };

      worksheetRepository.findOne.mockResolvedValue(worksheetWithMultipleQuestions);
      worksheetRepository.save.mockResolvedValue(worksheetWithMultipleQuestions);
      worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});

      const result = await service.bulkRemoveQuestions(
        'worksheet-1',
        ['question-2', 'nonexistent-question'],
        mockUserContext
      );

      expect(result.success).toBe(false);
      expect(result.successCount).toBe(1);
      expect(result.failureCount).toBe(1);
      expect(result.successes).toEqual(['question-2']);
      expect(result.failures).toHaveLength(1);
      expect(result.failures[0].questionId).toBe('nonexistent-question');
    });
  });

  describe('bulkUpdateQuestions', () => {
    it('should successfully update multiple questions', async () => {
      const updates = [
        {
          questionId: 'question-1',
          updates: {
            content: 'Updated: What is 2+2?',
            version: 1
          } as UpdateWorksheetQuestionDto
        }
      ];

      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);
      worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});

      // Mock the applyQuestionUpdate method
      const updatedQuestion = {
        ...mockWorksheet.questions[0],
        content: 'Updated: What is 2+2?',
        audit: { ...mockWorksheet.questions[0].audit, version: 2 }
      };
      jest.spyOn(service as any, 'applyQuestionUpdate').mockResolvedValue(updatedQuestion);
      jest.spyOn(service as any, 'validateQuestionVersion').mockResolvedValue(undefined);

      const result = await service.bulkUpdateQuestions(
        'worksheet-1',
        updates,
        mockUserContext,
        { reason: 'Updating question content' }
      );

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(1);
      expect(result.failureCount).toBe(0);
      expect(result.successes[0].content).toBe('Updated: What is 2+2?');

      expect(auditService.logBulkQuestionOperation).toHaveBeenCalledWith(
        'worksheet-1',
        'bulk_update',
        mockUserContext,
        expect.objectContaining({
          successCount: 1,
          failureCount: 0,
          reason: 'Updating question content'
        })
      );
    });

    it('should handle version conflicts in bulk updates', async () => {
      const updates = [
        {
          questionId: 'question-1',
          updates: {
            content: 'Updated content',
            version: 2 // Wrong version
          } as UpdateWorksheetQuestionDto
        }
      ];

      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      jest.spyOn(service as any, 'validateQuestionVersion')
        .mockRejectedValue(new Error('Version mismatch'));

      const result = await service.bulkUpdateQuestions(
        'worksheet-1',
        updates,
        mockUserContext
      );

      expect(result.success).toBe(false);
      expect(result.successCount).toBe(0);
      expect(result.failureCount).toBe(1);
      expect(result.failures[0].error).toBe('Version mismatch');
    });
  });
});
