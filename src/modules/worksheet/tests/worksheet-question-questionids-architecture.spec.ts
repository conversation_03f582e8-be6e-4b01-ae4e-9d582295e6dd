import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Model } from 'mongoose';

import { WorksheetQuestionService, UserContext } from '../services/worksheet-question.service';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { EUserRole } from '../../user/dto/create-user.dto';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto, BulkReorderQuestionsDto } from '../dto/worksheet-question.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { WorksheetQuestionEnhancedCacheService } from '../services/worksheet-question-enhanced-cache.service';
import { QuestionPoolService } from '../../question-pool/question-pool.service';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';

describe('WorksheetQuestionService - QuestionIds Architecture', () => {
  let worksheetRepository: Repository<Worksheet>;
  let questionModel: Model<WorksheetQuestionDocument>;

  const mockUser: UserContext = {
    sub: 'user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-123'
  };

  const createMockWorksheet = (questionIds: string[] = []): Worksheet => ({
    id: 'worksheet-123',
    title: 'Test Worksheet',
    questionIds, // New architecture: only store IDs
    totalQuestions: questionIds.length,
    schoolId: mockUser.schoolId,
    lastModifiedBy: mockUser.sub,
    questionMetadata: {
      questionVersion: 1,
      lastQuestionUpdate: new Date()
    }
  } as Worksheet);

  const createMockQuestionDoc = (id: string, position: number): any => ({
    _id: id,
    worksheetId: 'worksheet-123',
    questionId: `worksheet-123_${position}`,
    position,
    type: EQuestionType.MULTIPLE_CHOICE,
    content: `Question ${id}`,
    options: ['A', 'B', 'C', 'D'],
    answer: ['A'],
    explain: 'Test explanation',
    points: 1,
    difficulty: EQuestionDifficulty.MEDIUM,
    status: 'ACTIVE',
    schoolId: mockUser.schoolId,
    audit: {
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: mockUser.sub,
      version: 1
    },
    save: jest.fn().mockResolvedValue(this)
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getModelToken(WorksheetQuestionDocument.name),
          useValue: {
            findById: jest.fn(),
            find: jest.fn(),
            findOneAndUpdate: jest.fn(),
            findByIdAndUpdate: jest.fn(),
            findByIdAndDelete: jest.fn(),
            deleteMany: jest.fn(),
            bulkWrite: jest.fn(),
            prototype: {
              save: jest.fn()
            }
          },
        },
      ],
    }).compile();

    worksheetRepository = module.get<Repository<Worksheet>>(getRepositoryToken(Worksheet));
    questionModel = module.get<Model<WorksheetQuestionDocument>>(getModelToken(WorksheetQuestionDocument.name));
  });

  describe('Worksheet Entity - New Architecture', () => {
    it('should store only questionIds array, not full question objects', () => {
      // Arrange
      const worksheet = createMockWorksheet(['q1', 'q2', 'q3']);

      // Assert - New architecture validation
      expect(worksheet.questionIds).toBeDefined();
      expect(worksheet.questionIds).toEqual(['q1', 'q2', 'q3']);
      expect(worksheet.totalQuestions).toBe(3);

      // Verify no full question objects are stored in PostgreSQL
      expect((worksheet as any).questions).toBeUndefined();
    });
  });

  describe('MongoDB Question Document - New Architecture', () => {
    it('should store question data with position field for ordering', () => {
      // Arrange
      const mockQuestionDoc = createMockQuestionDoc('q1', 2);

      // Assert - MongoDB document structure validation
      expect(mockQuestionDoc._id).toBe('q1');
      expect(mockQuestionDoc.worksheetId).toBe('worksheet-123');
      expect(mockQuestionDoc.position).toBe(2);
      expect(mockQuestionDoc.type).toBe(EQuestionType.MULTIPLE_CHOICE);
      expect(mockQuestionDoc.content).toBe('Question q1');
      expect(mockQuestionDoc.status).toBe('ACTIVE');
    });

    it('should validate MongoDB query patterns for fetching questions', () => {
      // Arrange
      const worksheetId = 'worksheet-123';
      const expectedQuery = { worksheetId, status: 'ACTIVE' };
      const expectedSort = { position: 1 as any };

      // Mock the MongoDB query chain
      const mockExec = jest.fn().mockResolvedValue([]);
      const mockSort = jest.fn().mockReturnValue({ exec: mockExec });
      jest.spyOn(questionModel, 'find').mockReturnValue({ sort: mockSort } as any);

      // Act
      questionModel.find(expectedQuery).sort(expectedSort).exec();

      // Assert
      expect(questionModel.find).toHaveBeenCalledWith(expectedQuery);
      expect(mockSort).toHaveBeenCalledWith(expectedSort);
      expect(mockExec).toHaveBeenCalled();
    });
  });

  describe('Question Reordering - New Architecture', () => {
    it('should validate reordering logic for questionIds array', () => {
      // Arrange
      const originalQuestionIds = ['q1', 'q2', 'q3'];
      const worksheet = createMockWorksheet([...originalQuestionIds]);

      // Simulate reordering: move q3 to position 1
      const questionToMove = 'q3';
      const newPosition = 1;
      const oldIndex = worksheet.questionIds!.findIndex(id => id === questionToMove);
      const oldPosition = oldIndex + 1;

      // Act - Simulate the reordering logic
      const [movedQuestionId] = worksheet.questionIds!.splice(oldIndex, 1);
      worksheet.questionIds!.splice(newPosition - 1, 0, movedQuestionId);

      // Assert
      expect(oldPosition).toBe(3); // q3 was at position 3
      expect(worksheet.questionIds).toEqual(['q3', 'q1', 'q2']); // New order
      expect(worksheet.questionIds[0]).toBe('q3'); // q3 is now at position 1
    });

    it('should validate MongoDB bulk update operations for position sync', () => {
      // Arrange
      const questionIds = ['q3', 'q1', 'q2']; // After reordering
      const expectedBulkOps = questionIds.map((questionId, index) => ({
        updateOne: {
          filter: { _id: questionId },
          update: {
            $set: {
              position: index + 1,
              'audit.updatedAt': expect.any(Date),
              'audit.updatedBy': mockUser.sub
            },
            $inc: { 'audit.version': 1 },
            $push: {
              'audit.changeLog': {
                timestamp: expect.any(Date),
                userId: mockUser.sub,
                action: 'reorder',
                changes: { newPosition: index + 1 },
                reason: 'Question reordered'
              }
            }
          }
        }
      }));

      // Act
      jest.spyOn(questionModel, 'bulkWrite').mockResolvedValue({} as any);
      questionModel.bulkWrite(expectedBulkOps);

      // Assert
      expect(questionModel.bulkWrite).toHaveBeenCalledWith(expectedBulkOps);
    });
  });

  describe('Question Removal - New Architecture', () => {
    it('should validate removal logic from questionIds array', () => {
      // Arrange
      const worksheet = createMockWorksheet(['q1', 'q2', 'q3']);
      const questionToRemove = 'q2';

      // Act - Simulate removal logic
      const questionIndex = worksheet.questionIds!.findIndex(id => id === questionToRemove);
      expect(questionIndex).toBe(1); // q2 is at index 1

      worksheet.questionIds!.splice(questionIndex, 1);
      worksheet.totalQuestions = worksheet.questionIds!.length;

      // Assert
      expect(worksheet.questionIds).toEqual(['q1', 'q3']);
      expect(worksheet.totalQuestions).toBe(2);
    });

    it('should validate MongoDB deletion and position reordering', () => {
      // Arrange
      const remainingQuestionIds = ['q1', 'q3']; // After q2 removal

      // Mock MongoDB operations
      jest.spyOn(questionModel, 'findByIdAndDelete').mockResolvedValue({} as any);
      jest.spyOn(questionModel, 'bulkWrite').mockResolvedValue({} as any);

      // Act
      questionModel.findByIdAndDelete('q2');

      // Simulate position reordering for remaining questions
      const bulkOps = remainingQuestionIds.map((questionId, index) => ({
        updateOne: {
          filter: { _id: questionId },
          update: {
            $set: {
              position: index + 1,
              'audit.updatedAt': expect.any(Date),
              'audit.updatedBy': mockUser.sub
            },
            $inc: { 'audit.version': 1 }
          }
        }
      }));

      questionModel.bulkWrite(bulkOps);

      // Assert
      expect(questionModel.findByIdAndDelete).toHaveBeenCalledWith('q2');
      expect(questionModel.bulkWrite).toHaveBeenCalledWith(bulkOps);
    });
  });

  describe('Data Consistency - New Architecture', () => {
    it('should validate that PostgreSQL and MongoDB stay in sync', () => {
      // Arrange
      const worksheet = createMockWorksheet(['q1', 'q2', 'q3']);
      const mockQuestionDocs = [
        createMockQuestionDoc('q1', 1),
        createMockQuestionDoc('q2', 2),
        createMockQuestionDoc('q3', 3)
      ];

      // Assert - Verify sync between PostgreSQL questionIds and MongoDB positions
      worksheet.questionIds!.forEach((questionId, index) => {
        const expectedPosition = index + 1;
        const correspondingDoc = mockQuestionDocs.find(doc => doc._id === questionId);

        expect(correspondingDoc).toBeDefined();
        expect(correspondingDoc!.position).toBe(expectedPosition);
        expect(correspondingDoc!.worksheetId).toBe(worksheet.id);
      });
    });

    it('should validate question type fetching from MongoDB for validation', () => {
      // Arrange
      const mockQuestionDoc = createMockQuestionDoc('q1', 1);
      mockQuestionDoc.type = EQuestionType.TRUE_FALSE;

      jest.spyOn(questionModel, 'findById').mockResolvedValue(mockQuestionDoc);

      // Act
      questionModel.findById('q1');

      // Assert - Verify MongoDB is source of truth for question type
      expect(questionModel.findById).toHaveBeenCalledWith('q1');
      expect(mockQuestionDoc.type).toBe(EQuestionType.TRUE_FALSE);
    });

    it('should validate architecture separation of concerns', () => {
      // Arrange
      const worksheet = createMockWorksheet(['q1', 'q2']);
      const questionDoc = createMockQuestionDoc('q1', 1);

      // Assert - PostgreSQL stores only metadata and references
      expect(worksheet.questionIds).toEqual(['q1', 'q2']);
      expect(worksheet.totalQuestions).toBe(2);
      expect((worksheet as any).questions).toBeUndefined(); // No full question objects

      // Assert - MongoDB stores full question data
      expect(questionDoc.type).toBeDefined();
      expect(questionDoc.content).toBeDefined();
      expect(questionDoc.options).toBeDefined();
      expect(questionDoc.answer).toBeDefined();
      expect(questionDoc.position).toBeDefined();
    });
  });
});
