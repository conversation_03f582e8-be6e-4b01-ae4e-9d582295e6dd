import { Test, TestingModule } from '@nestjs/testing';
import { WorksheetQuestionService, UserContext } from '../services/worksheet-question.service';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { WorksheetQuestionCollaborationGateway } from '../gateways/worksheet-question-collaboration.gateway';
import { WorksheetQuestionLockingService } from '../services/worksheet-question-locking.service';
import { WorksheetQuestionMetricsService } from '../services/worksheet-question-metrics.service';
import { WorksheetQuestionEnhancedCacheService } from '../services/worksheet-question-enhanced-cache.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto } from '../dto/worksheet-question.dto';
import { CollaborationEvent } from '../enums/collaboration-events.enum';

describe('WorksheetBulkOperations - WebSocket Tests', () => {
  let service: WorksheetQuestionService;
  let worksheetRepository: any;
  let worksheetQuestionModel: any;
  let socketGateway: jest.Mocked<SocketGateway>;
  let collaborationGateway: jest.Mocked<WorksheetQuestionCollaborationGateway>;

  const mockUserContext: UserContext = {
    sub: 'ws-user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'ws-school-123'
  };

  const mockWorksheet: Worksheet = {
    id: 'ws-worksheet-1',
    title: 'WebSocket Test Worksheet',
    questions: [{
      id: 'ws-question-1',
      type: EQuestionType.MULTIPLE_CHOICE,
      content: 'What is 2+2?',
      options: ['2', '3', '4', '5'],
      answer: ['4'],
      explain: '2+2 equals 4',
      audit: { version: 1, createdBy: 'ws-user-123', createdAt: new Date() }
    }],
    totalQuestions: 1,
    maxQuestions: 100,
    schoolId: 'ws-school-123',
    createdBy: 'ws-user-123'
  } as any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          }
        },
        {
          provide: getModelToken(WorksheetQuestionDocument.name),
          useValue: {
            findOneAndUpdate: jest.fn(),
            updateOne: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionAuditService,
          useValue: {
            logBulkQuestionOperation: jest.fn(),
          }
        },
        {
          provide: SocketGateway,
          useValue: {
            server: {
              to: jest.fn().mockReturnThis(),
              emit: jest.fn(),
            }
          }
        },
        {
          provide: WorksheetQuestionCollaborationGateway,
          useValue: {
            broadcastQuestionUpdate: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionLockingService,
          useValue: {
            canEditQuestion: jest.fn().mockResolvedValue(true),
          }
        },
        {
          provide: WorksheetQuestionMetricsService,
          useValue: {
            recordMetric: jest.fn(),
          }
        },
        {
          provide: WorksheetQuestionEnhancedCacheService,
          useValue: {
            cacheWorksheetQuestions: jest.fn(),
            invalidateWorksheetCache: jest.fn(),
          }
        }
      ],
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    worksheetRepository = module.get(getRepositoryToken(Worksheet));
    worksheetQuestionModel = module.get(getModelToken(WorksheetQuestionDocument.name));
    socketGateway = module.get(SocketGateway);
    collaborationGateway = module.get(WorksheetQuestionCollaborationGateway);

    // Setup common mocks
    worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
    worksheetRepository.save.mockResolvedValue(mockWorksheet);
    worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});
  });

  describe('Bulk Add WebSocket Events', () => {
    it('should emit questions_bulk_added event after successful bulk add', async () => {
      const questions: AddQuestionToWorksheetDto[] = [
        {
          type: EQuestionType.MULTIPLE_CHOICE,
          content: 'What is 3+3?',
          options: ['5', '6', '7', '8'],
          answer: ['6'],
          explain: '3+3 equals 6',
          difficulty: EQuestionDifficulty.EASY
        },
        {
          type: EQuestionType.TRUE_FALSE,
          content: 'The sky is blue',
          options: ['True', 'False'],
          answer: ['True'],
          explain: 'The sky appears blue due to light scattering',
          difficulty: EQuestionDifficulty.EASY
        }
      ];

      await service.bulkAddQuestions(
        'ws-worksheet-1',
        questions,
        mockUserContext,
        { reason: 'WebSocket test' }
      );

      // Verify legacy WebSocket event was emitted
      expect(socketGateway.server.to).toHaveBeenCalledWith('worksheet-ws-worksheet-1');
      expect(socketGateway.server.emit).toHaveBeenCalledWith(
        'questions_bulk_added',
        expect.objectContaining({
          addedQuestions: expect.any(Array),
          totalQuestions: expect.any(Number),
          worksheetId: 'ws-worksheet-1',
          reason: 'WebSocket test',
          timestamp: expect.any(String)
        })
      );

      // Verify the added questions array has the correct length
      const emitCall = socketGateway.server.emit.mock.calls.find(
        call => call[0] === 'questions_bulk_added'
      );
      expect(emitCall[1].addedQuestions).toHaveLength(2);
    });

    it('should include proper metadata in bulk add WebSocket events', async () => {
      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Metadata test question',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'Metadata test explanation',
        difficulty: EQuestionDifficulty.EASY
      }];

      await service.bulkAddQuestions(
        'ws-worksheet-1',
        questions,
        mockUserContext,
        { reason: 'Metadata test', insertPosition: 2 }
      );

      const emitCall = socketGateway.server.emit.mock.calls.find(
        call => call[0] === 'questions_bulk_added'
      );

      expect(emitCall[1]).toMatchObject({
        addedQuestions: expect.arrayContaining([
          expect.objectContaining({
            content: 'Metadata test question',
            schoolId: 'ws-school-123'
          })
        ]),
        totalQuestions: 2, // Original 1 + 1 added
        worksheetId: 'ws-worksheet-1',
        reason: 'Metadata test',
        timestamp: expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
      });
    });
  });

  describe('Bulk Remove WebSocket Events', () => {
    it('should emit questions_bulk_removed event after successful bulk remove', async () => {
      const worksheetWithMultipleQuestions = {
        ...mockWorksheet,
        questions: [
          mockWorksheet.questions[0],
          {
            id: 'ws-question-2',
            type: EQuestionType.TRUE_FALSE,
            content: 'Test question 2',
            options: ['True', 'False'],
            answer: ['True'],
            explain: 'Test explanation 2',
            audit: { version: 1 }
          }
        ],
        totalQuestions: 2
      };

      worksheetRepository.findOne.mockResolvedValue(worksheetWithMultipleQuestions);
      worksheetRepository.save.mockResolvedValue(worksheetWithMultipleQuestions);

      await service.bulkRemoveQuestions(
        'ws-worksheet-1',
        ['ws-question-2'],
        mockUserContext,
        { reason: 'WebSocket remove test' }
      );

      expect(socketGateway.server.to).toHaveBeenCalledWith('worksheet-ws-worksheet-1');
      expect(socketGateway.server.emit).toHaveBeenCalledWith(
        'questions_bulk_removed',
        expect.objectContaining({
          removedQuestionIds: ['ws-question-2'],
          totalQuestions: 1,
          worksheetId: 'ws-worksheet-1',
          reason: 'WebSocket remove test',
          timestamp: expect.any(String)
        })
      );
    });

    it('should handle partial failures in WebSocket events for bulk remove', async () => {
      const worksheetWithMultipleQuestions = {
        ...mockWorksheet,
        questions: [
          mockWorksheet.questions[0],
          {
            id: 'ws-question-2',
            type: EQuestionType.TRUE_FALSE,
            content: 'Test question 2',
            options: ['True', 'False'],
            answer: ['True'],
            explain: 'Test explanation 2',
            audit: { version: 1 }
          }
        ],
        totalQuestions: 2
      };

      worksheetRepository.findOne.mockResolvedValue(worksheetWithMultipleQuestions);
      worksheetRepository.save.mockResolvedValue(worksheetWithMultipleQuestions);

      // Try to remove one valid and one invalid question ID
      await service.bulkRemoveQuestions(
        'ws-worksheet-1',
        ['ws-question-2', 'nonexistent-question'],
        mockUserContext
      );

      const emitCall = socketGateway.server.emit.mock.calls.find(
        call => call[0] === 'questions_bulk_removed'
      );

      expect(emitCall[1].removedQuestionIds).toEqual(['ws-question-2']);
      expect(emitCall[1].totalQuestions).toBe(1);
    });
  });

  describe('Bulk Update WebSocket Events', () => {
    it('should emit questions_bulk_updated event after successful bulk update', async () => {
      // Mock the validation and update methods
      jest.spyOn(service as any, 'validateQuestionVersion').mockResolvedValue(undefined);
      jest.spyOn(service as any, 'applyQuestionUpdate').mockImplementation((existing, updates) => ({
        ...existing,
        ...updates,
        audit: { ...existing.audit, version: existing.audit.version + 1 }
      }));

      const updates = [{
        questionId: 'ws-question-1',
        updates: {
          content: 'Updated: What is 2+2?',
          version: 1
        } as UpdateWorksheetQuestionDto
      }];

      await service.bulkUpdateQuestions(
        'ws-worksheet-1',
        updates,
        mockUserContext,
        { reason: 'WebSocket update test' }
      );

      expect(socketGateway.server.to).toHaveBeenCalledWith('worksheet-ws-worksheet-1');
      expect(socketGateway.server.emit).toHaveBeenCalledWith(
        'questions_bulk_updated',
        expect.objectContaining({
          updatedQuestions: expect.arrayContaining([
            expect.objectContaining({
              id: 'ws-question-1',
              content: 'Updated: What is 2+2?'
            })
          ]),
          totalQuestions: 1,
          worksheetId: 'ws-worksheet-1',
          reason: 'WebSocket update test',
          timestamp: expect.any(String)
        })
      );
    });
  });

  describe('WebSocket Error Handling', () => {
    it('should not fail bulk operations when WebSocket emission fails', async () => {
      // Mock WebSocket to throw an error
      socketGateway.server.emit.mockImplementation(() => {
        throw new Error('WebSocket connection failed');
      });

      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Error handling test',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'This should succeed despite WebSocket error',
        difficulty: EQuestionDifficulty.EASY
      }];

      // The operation should still succeed
      const result = await service.bulkAddQuestions(
        'ws-worksheet-1',
        questions,
        mockUserContext
      );

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(1);
      expect(worksheetRepository.save).toHaveBeenCalled();
    });

    it('should log WebSocket errors without affecting the operation', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Mock WebSocket to throw an error
      socketGateway.server.to.mockImplementation(() => {
        throw new Error('WebSocket room join failed');
      });

      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Logging test',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'This should log the error',
        difficulty: EQuestionDifficulty.EASY
      }];

      await service.bulkAddQuestions('ws-worksheet-1', questions, mockUserContext);

      // The operation should succeed and the error should be logged
      expect(worksheetRepository.save).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('Real-time Collaboration Events', () => {
    it('should emit proper events for multiple connected users', async () => {
      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Collaboration test',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'Multi-user test',
        difficulty: EQuestionDifficulty.EASY
      }];

      await service.bulkAddQuestions(
        'ws-worksheet-1',
        questions,
        mockUserContext,
        { reason: 'Multi-user collaboration test' }
      );

      // Verify that the event is broadcast to the correct room
      expect(socketGateway.server.to).toHaveBeenCalledWith('worksheet-ws-worksheet-1');
      
      // Verify that the event includes user information for collaboration
      const emitCall = socketGateway.server.emit.mock.calls.find(
        call => call[0] === 'questions_bulk_added'
      );
      
      expect(emitCall[1]).toMatchObject({
        addedQuestions: expect.any(Array),
        worksheetId: 'ws-worksheet-1',
        reason: 'Multi-user collaboration test',
        timestamp: expect.any(String)
      });
    });

    it('should include proper timing information in WebSocket events', async () => {
      const startTime = Date.now();
      
      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'Timing test',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'Timing explanation',
        difficulty: EQuestionDifficulty.EASY
      }];

      await service.bulkAddQuestions('ws-worksheet-1', questions, mockUserContext);

      const emitCall = socketGateway.server.emit.mock.calls.find(
        call => call[0] === 'questions_bulk_added'
      );

      const eventTimestamp = new Date(emitCall[1].timestamp).getTime();
      expect(eventTimestamp).toBeGreaterThanOrEqual(startTime);
      expect(eventTimestamp).toBeLessThanOrEqual(Date.now());
    });
  });

  describe('WebSocket Event Filtering', () => {
    it('should not emit events to the user who initiated the operation', async () => {
      // This test verifies that the excludeUserId parameter works correctly
      // In the current implementation, we don't exclude the user, but this could be added
      
      const questions: AddQuestionToWorksheetDto[] = [{
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'User filtering test',
        options: ['A', 'B', 'C', 'D'],
        answer: ['A'],
        explain: 'User filtering explanation',
        difficulty: EQuestionDifficulty.EASY
      }];

      await service.bulkAddQuestions('ws-worksheet-1', questions, mockUserContext);

      // Verify that the WebSocket event was emitted
      expect(socketGateway.server.to).toHaveBeenCalledWith('worksheet-ws-worksheet-1');
      expect(socketGateway.server.emit).toHaveBeenCalled();
      
      // Note: In a real implementation, you might want to exclude the initiating user
      // This test documents the current behavior
    });
  });
});
