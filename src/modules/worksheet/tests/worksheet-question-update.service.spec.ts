import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { ConflictException, NotFoundException, BadRequestException } from '@nestjs/common';
import { WorksheetQuestionService, UserContext } from '../services/worksheet-question.service';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { EQuestionType } from '../../../shared/interfaces/exercise-question.interface';
import { UpdateWorksheetQuestionDto } from '../dto/worksheet-question.dto';

describe('WorksheetQuestionService - Update Operations', () => {
  let service: WorksheetQuestionService;
  let worksheetRepository: any;
  let worksheetQuestionModel: any;
  let auditService: WorksheetQuestionAuditService;
  let socketGateway: SocketGateway;

  const mockUserContext: UserContext = {
    sub: 'user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-123'
  };

  const mockWorksheet = {
    id: 'worksheet-1',
    title: 'Test Worksheet',
    schoolId: 'school-123',
    questions: [
      {
        id: 'question-1',
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is 2+2?',
        options: ['2', '3', '4', '5'],
        answer: ['4'],
        explain: '2+2 equals 4',
        order: 1,
        audit: {
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'user-123',
          version: 1
        }
      }
    ],
    totalQuestions: 1,
    createdBy: 'user-123',
    lastModifiedBy: 'user-123'
  };

  beforeEach(async () => {
    const mockRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    const mockModel = {
      findOne: jest.fn(),
      findOneAndUpdate: jest.fn(),
      updateOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockAuditService = {
      logQuestionUpdated: jest.fn(),
      logQuestionAdded: jest.fn(),
      logQuestionRemoved: jest.fn(),
    };

    const mockSocketGateway = {
      server: {
        to: jest.fn().mockReturnValue({
          emit: jest.fn()
        })
      }
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetQuestionService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: mockRepository,
        },
        {
          provide: getModelToken(WorksheetQuestionDocument.name),
          useValue: mockModel,
        },
        {
          provide: WorksheetQuestionAuditService,
          useValue: mockAuditService,
        },
        {
          provide: SocketGateway,
          useValue: mockSocketGateway,
        },
      ],
    }).compile();

    service = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    worksheetRepository = module.get(getRepositoryToken(Worksheet));
    worksheetQuestionModel = module.get(getModelToken(WorksheetQuestionDocument.name));
    auditService = module.get<WorksheetQuestionAuditService>(WorksheetQuestionAuditService);
    socketGateway = module.get<SocketGateway>(SocketGateway);
  });

  describe('updateQuestionInWorksheet', () => {
    it('should successfully update a question with partial data', async () => {
      // Mock worksheet access validation
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);
      worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});

      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'What is 3+3?',
        version: 1,
        updateReason: 'Updating question content'
      };

      const result = await service.updateQuestionInWorksheet(
        'worksheet-1',
        'question-1',
        updateDto,
        mockUserContext
      );

      expect(result.content).toBe('What is 3+3?');
      expect(result.audit?.version).toBe(2);
      expect(result.audit?.updatedBy).toBe(mockUserContext.sub);
      expect(worksheetRepository.save).toHaveBeenCalled();
      expect(auditService.logQuestionUpdated).toHaveBeenCalled();
    });

    it('should throw ConflictException for version mismatch', async () => {
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);

      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'What is 3+3?',
        version: 2 // Wrong version
      };

      await expect(
        service.updateQuestionInWorksheet('worksheet-1', 'question-1', updateDto, mockUserContext)
      ).rejects.toThrow(ConflictException);
    });

    it('should throw NotFoundException for non-existent question', async () => {
      const worksheetWithoutQuestion = {
        ...mockWorksheet,
        questions: []
      };
      worksheetRepository.findOne.mockResolvedValue(worksheetWithoutQuestion);

      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'What is 3+3?'
      };

      await expect(
        service.updateQuestionInWorksheet('worksheet-1', 'question-1', updateDto, mockUserContext)
      ).rejects.toThrow(NotFoundException);
    });

    it('should validate answer format for question type updates', async () => {
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);

      const updateDto: UpdateWorksheetQuestionDto = {
        type: EQuestionType.TRUE_FALSE,
        answer: ['True', 'False'] // Invalid - true/false should have only one answer
      };

      await expect(
        service.updateQuestionInWorksheet('worksheet-1', 'question-1', updateDto, mockUserContext)
      ).rejects.toThrow(BadRequestException);
    });

    it('should validate image prompt requirements', async () => {
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);

      const updateDto: UpdateWorksheetQuestionDto = {
        imagePrompt: 'Draw a rectangle with specific dimensions' // Missing actual dimensions
      };

      await expect(
        service.updateQuestionInWorksheet('worksheet-1', 'question-1', updateDto, mockUserContext)
      ).rejects.toThrow(BadRequestException);
    });
  });



  describe('Validation Methods', () => {
    it('should validate answer format for multiple choice questions', async () => {
      await expect(
        service['validateAnswerFormat'](EQuestionType.MULTIPLE_CHOICE, [])
      ).rejects.toThrow(BadRequestException);
    });

    it('should validate answer format for true/false questions', async () => {
      await expect(
        service['validateAnswerFormat'](EQuestionType.TRUE_FALSE, ['True', 'False'])
      ).rejects.toThrow(BadRequestException);

      await expect(
        service['validateAnswerFormat'](EQuestionType.TRUE_FALSE, ['Maybe'])
      ).rejects.toThrow(BadRequestException);
    });

    it('should validate question options for multiple choice', async () => {
      await expect(
        service['validateQuestionOptions'](['only-one'], EQuestionType.MULTIPLE_CHOICE)
      ).rejects.toThrow(BadRequestException);
    });

    it('should detect duplicate options', async () => {
      await expect(
        service['validateQuestionOptions'](['Option A', 'Option B', 'option a'], EQuestionType.MULTIPLE_CHOICE)
      ).rejects.toThrow(BadRequestException);
    });

    it('should validate image prompt with measurements', async () => {
      await expect(
        service['validateImagePrompt']('Draw a rectangle with specific dimensions')
      ).rejects.toThrow(BadRequestException);

      // Should pass with specific measurements
      await expect(
        service['validateImagePrompt']('Draw a rectangle 10cm by 5cm')
      ).resolves.not.toThrow();
    });

    it('should validate educational standards', async () => {
      const invalidGradeData = { grade: 'invalid-grade' };
      
      await expect(
        service['validateEducationalStandards'](invalidGradeData)
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('Real-time Updates', () => {
    it('should emit WebSocket events for question updates', async () => {
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet);
      worksheetRepository.save.mockResolvedValue(mockWorksheet);
      worksheetQuestionModel.findOneAndUpdate.mockResolvedValue({});

      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'Updated content'
      };

      await service.updateQuestionInWorksheet('worksheet-1', 'question-1', updateDto, mockUserContext);

      expect(socketGateway.server.to).toHaveBeenCalledWith('worksheet-worksheet-1');
    });
  });
});
