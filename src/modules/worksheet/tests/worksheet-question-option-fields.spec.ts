import { Test, TestingModule } from '@nestjs/testing';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { AddQuestionToWorksheetDto, UpdateWorksheetQuestionDto } from '../dto/worksheet-question.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';

describe('Worksheet Question DTOs - Option Fields', () => {
  describe('AddQuestionToWorksheetDto', () => {
    it('should validate successfully with optionTypeId and optionValueId', async () => {
      const dto = plainToClass(AddQuestionToWorksheetDto, {
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is the capital of France?',
        options: ['Paris', 'London', 'Berlin', 'Madrid'],
        answer: ['Paris'],
        explain: 'Paris is the capital and largest city of France.',
        optionTypeId: '123e4567-e89b-12d3-a456-************',
        optionValueId: '987fcdeb-51a2-43d1-b789-123456789abc',
        position: 1,
        points: 5
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.optionTypeId).toBe('123e4567-e89b-12d3-a456-************');
      expect(dto.optionValueId).toBe('987fcdeb-51a2-43d1-b789-123456789abc');
    });

    it('should validate successfully without optionTypeId and optionValueId (optional fields)', async () => {
      const dto = plainToClass(AddQuestionToWorksheetDto, {
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is the capital of France?',
        options: ['Paris', 'London', 'Berlin', 'Madrid'],
        answer: ['Paris'],
        explain: 'Paris is the capital and largest city of France.'
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.optionTypeId).toBeUndefined();
      expect(dto.optionValueId).toBeUndefined();
    });

    it('should fail validation with invalid UUID format for optionTypeId', async () => {
      const dto = plainToClass(AddQuestionToWorksheetDto, {
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is the capital of France?',
        options: ['Paris', 'London', 'Berlin', 'Madrid'],
        answer: ['Paris'],
        explain: 'Paris is the capital and largest city of France.',
        optionTypeId: 'invalid-uuid-format'
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'optionTypeId')).toBe(true);
    });

    it('should fail validation with invalid UUID format for optionValueId', async () => {
      const dto = plainToClass(AddQuestionToWorksheetDto, {
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is the capital of France?',
        options: ['Paris', 'London', 'Berlin', 'Madrid'],
        answer: ['Paris'],
        explain: 'Paris is the capital and largest city of France.',
        optionValueId: 'not-a-valid-uuid'
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.property === 'optionValueId')).toBe(true);
    });
  });

  describe('UpdateWorksheetQuestionDto', () => {
    it('should inherit optionTypeId and optionValueId fields from AddQuestionToWorksheetDto', async () => {
      const dto = plainToClass(UpdateWorksheetQuestionDto, {
        content: 'Updated question content',
        optionTypeId: '123e4567-e89b-12d3-a456-************',
        optionValueId: '987fcdeb-51a2-43d1-b789-123456789abc',
        version: 1,
        updateReason: 'Adding option references'
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
      expect(dto.optionTypeId).toBe('123e4567-e89b-12d3-a456-************');
      expect(dto.optionValueId).toBe('987fcdeb-51a2-43d1-b789-123456789abc');
    });
  });



  describe('Type Field Documentation', () => {
    it('should accept all valid EQuestionType enum values', async () => {
      const validTypes = [
        EQuestionType.MULTIPLE_CHOICE,
        EQuestionType.TRUE_FALSE,
        EQuestionType.SHORT_ANSWER,
        EQuestionType.LONG_ANSWER,
        EQuestionType.FILL_IN_THE_BLANK,
        EQuestionType.MATCHING,
        EQuestionType.ORDERING,
        EQuestionType.CALCULATION,
        EQuestionType.DIAGRAM,
        EQuestionType.ESSAY
      ];

      for (const type of validTypes) {
        const dto = plainToClass(AddQuestionToWorksheetDto, {
          type,
          content: 'Test question content',
          options: ['Option 1', 'Option 2'],
          answer: ['Option 1'],
          explain: 'Test explanation'
        });

        const errors = await validate(dto);
        expect(errors.filter(e => e.property === 'type')).toHaveLength(0);
      }
    });
  });
});
