import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import * as request from 'supertest';
import { WorksheetController } from '../worksheet.controller';
import { WorksheetService } from '../worksheet.service';
import { WorksheetQuestionService } from '../services/worksheet-question.service';
import { WorksheetQuestionAuditService } from '../services/worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument, WorksheetQuestionDocumentSchema } from '../../mongodb/schemas/worksheet-question-document.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { EQuestionType, EQuestionDifficulty } from '../../../shared/interfaces/exercise-question.interface';
import { UpdateWorksheetQuestionDto } from '../dto/worksheet-question.dto';

describe('WorksheetQuestionController - Update Operations (Integration)', () => {
  let app: INestApplication;
  let worksheetService: WorksheetService;
  let worksheetQuestionService: WorksheetQuestionService;
  let auditService: WorksheetQuestionAuditService;

  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    role: EUserRole.TEACHER,
    schoolId: 'school-123'
  };

  const mockAdminUser = {
    id: 'admin-123',
    email: '<EMAIL>',
    role: EUserRole.ADMIN,
    schoolId: undefined
  };

  const mockWorksheet = {
    id: 'worksheet-1',
    title: 'Test Worksheet',
    schoolId: 'school-123',
    questions: [
      {
        id: 'question-1',
        type: EQuestionType.MULTIPLE_CHOICE,
        content: 'What is 2+2?',
        options: ['2', '3', '4', '5'],
        answer: ['4'],
        explain: '2+2 equals 4',
        order: 1,
        audit: {
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'user-123',
          version: 1
        }
      }
    ],
    totalQuestions: 1,
    createdBy: 'user-123',
    lastModifiedBy: 'user-123'
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Worksheet],
          synchronize: true,
        }),
        TypeOrmModule.forFeature([Worksheet]),
        MongooseModule.forRoot('mongodb://localhost/test'),
        MongooseModule.forFeature([
          { name: WorksheetQuestionDocument.name, schema: WorksheetQuestionDocumentSchema }
        ])
      ],
      controllers: [WorksheetController],
      providers: [
        WorksheetService,
        WorksheetQuestionService,
        WorksheetQuestionAuditService,
        {
          provide: SocketGateway,
          useValue: {
            server: {
              to: jest.fn().mockReturnValue({
                emit: jest.fn()
              })
            }
          }
        }
      ],
    }).compile();

    app = module.createNestApplication();
    await app.init();

    worksheetService = module.get<WorksheetService>(WorksheetService);
    worksheetQuestionService = module.get<WorksheetQuestionService>(WorksheetQuestionService);
    auditService = module.get<WorksheetQuestionAuditService>(WorksheetQuestionAuditService);

    // Mock authentication middleware
    jest.spyOn(app.get('AuthGuard'), 'canActivate').mockImplementation(() => true);
  });

  afterEach(async () => {
    await app.close();
  });

  describe('PATCH /worksheets/:id/questions/:questionId', () => {
    it('should successfully update a question with partial data', async () => {
      // Mock the service methods
      jest.spyOn(worksheetQuestionService, 'updateQuestionInWorksheet')
        .mockResolvedValue({
          ...mockWorksheet.questions[0],
          content: 'What is 3+3?',
          audit: {
            ...mockWorksheet.questions[0].audit,
            version: 2,
            updatedAt: new Date()
          }
        });

      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'What is 3+3?',
        version: 1,
        updateReason: 'Updating question content'
      };

      const response = await request(app.getHttpServer())
        .patch('/worksheets/worksheet-1/questions/question-1')
        .set('Authorization', 'Bearer mock-token')
        .send(updateDto)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.content).toBe('What is 3+3?');
      expect(worksheetQuestionService.updateQuestionInWorksheet)
        .toHaveBeenCalledWith('worksheet-1', 'question-1', updateDto, expect.any(Object));
    });

    it('should handle version conflict (optimistic locking)', async () => {
      jest.spyOn(worksheetQuestionService, 'updateQuestionInWorksheet')
        .mockRejectedValue(new Error('Question has been modified by another user'));

      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'What is 3+3?',
        version: 1
      };

      await request(app.getHttpServer())
        .patch('/worksheets/worksheet-1/questions/question-1')
        .set('Authorization', 'Bearer mock-token')
        .send(updateDto)
        .expect(500); // Should be handled by global exception filter
    });

    it('should validate required permissions for school isolation', async () => {
      // Mock user from different school
      const differentSchoolUser = {
        ...mockUser,
        schoolId: 'different-school'
      };

      jest.spyOn(worksheetQuestionService, 'updateQuestionInWorksheet')
        .mockRejectedValue(new Error('Access denied to worksheet from different school'));

      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'What is 3+3?'
      };

      await request(app.getHttpServer())
        .patch('/worksheets/worksheet-1/questions/question-1')
        .set('Authorization', 'Bearer mock-token')
        .send(updateDto)
        .expect(500);
    });

    it('should validate question data format', async () => {
      const invalidUpdateDto = {
        type: EQuestionType.MULTIPLE_CHOICE,
        options: ['only-one-option'], // Invalid for multiple choice
        answer: []  // Empty answers not allowed
      };

      await request(app.getHttpServer())
        .patch('/worksheets/worksheet-1/questions/question-1')
        .set('Authorization', 'Bearer mock-token')
        .send(invalidUpdateDto)
        .expect(400);
    });
  });



  describe('Access Control Tests', () => {
    it('should allow admin to update questions in any worksheet', async () => {
      jest.spyOn(worksheetQuestionService, 'updateQuestionInWorksheet')
        .mockResolvedValue(mockWorksheet.questions[0]);

      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'Admin updated content'
      };

      // Mock admin user context
      const response = await request(app.getHttpServer())
        .patch('/worksheets/worksheet-1/questions/question-1')
        .set('Authorization', 'Bearer admin-token')
        .send(updateDto)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should prevent unauthorized users from updating questions', async () => {
      // This would be handled by the AuthGuard and RoleGuard
      // In a real test, we'd mock the guards to return false
      
      const updateDto: UpdateWorksheetQuestionDto = {
        content: 'Unauthorized update'
      };

      // Without proper authorization header
      await request(app.getHttpServer())
        .patch('/worksheets/worksheet-1/questions/question-1')
        .send(updateDto)
        .expect(401);
    });
  });
});
