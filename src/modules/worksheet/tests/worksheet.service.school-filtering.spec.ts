import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getModelToken } from '@nestjs/mongoose';
import { ForbiddenException } from '@nestjs/common';
import { Repository } from 'typeorm';
import { Model } from 'mongoose';

import { WorksheetService } from '../worksheet.service';
import { Worksheet } from '../entities/worksheet.entity';
import { OptionType } from '../../options/entities/option-type.entity';
import { OptionValue } from '../../options/entities/option-value.entity';
import { WorksheetOption } from '../entities/worksheet-option.entity';
import { WorksheetQueueService } from '../worksheet-queue.service';
import { WorksheetPromptResult } from '../../mongodb/schemas/worksheet-prompt-result.schema';
import { WorksheetDocumentCacheService } from '../services/worksheet-document-cache.service';
import { WorksheetDocument } from '../../mongodb/schemas/worksheet-document.schema';
import { EUserRole } from '../../user/dto/create-user.dto';
import { User } from '../../user/entities/user.entity';
import { ListWorksheetDto } from '../dto/list-worksheets.dto';
import { WorksheetGeneratingStatus } from '../entities/worksheet.entity';

describe('WorksheetService - School-Based Filtering', () => {
  let service: WorksheetService;
  let worksheetRepository: jest.Mocked<Repository<Worksheet>>;
  let optionTypeRepository: jest.Mocked<Repository<OptionType>>;
  let optionValueRepository: jest.Mocked<Repository<OptionValue>>;
  let worksheetOptionRepository: jest.Mocked<Repository<WorksheetOption>>;
  let worksheetQueueService: jest.Mocked<WorksheetQueueService>;
  let worksheetPromptResultModel: jest.Mocked<Model<WorksheetPromptResult>>;
  let worksheetDocumentCacheService: jest.Mocked<WorksheetDocumentCacheService>;
  let worksheetDocumentModel: jest.Mocked<Model<WorksheetDocument>>;

  // Mock users
  const mockAdminUser: Partial<User> = {
    id: 'admin-id',
    role: EUserRole.ADMIN,
    schoolId: 'admin-school-id',
    email: '<EMAIL>',
  };

  const mockTeacherUser: Partial<User> = {
    id: 'teacher-id',
    role: EUserRole.TEACHER,
    schoolId: 'school-a-id',
    email: '<EMAIL>',
  };

  const mockIndependentTeacherUser: Partial<User> = {
    id: 'independent-teacher-id',
    role: EUserRole.INDEPENDENT_TEACHER,
    schoolId: 'school-b-id',
    email: '<EMAIL>',
  };

  const mockUserWithoutSchool: Partial<User> = {
    id: 'no-school-user-id',
    role: EUserRole.TEACHER,
    schoolId: null,
    email: '<EMAIL>',
  };

  // Mock worksheets
  const mockWorksheets: Partial<Worksheet>[] = [
    {
      id: 'worksheet-1',
      title: 'Math Worksheet 1',
      description: 'Math worksheet description',
      schoolId: 'school-a-id',
      generatingStatus: WorksheetGeneratingStatus.GENERATED,
      subjectData: null,
      selectedOptions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'worksheet-2',
      title: 'Science Worksheet 1',
      description: 'Science worksheet description',
      schoolId: 'school-b-id',
      generatingStatus: WorksheetGeneratingStatus.GENERATED,
      subjectData: null,
      selectedOptions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'worksheet-3',
      title: 'English Worksheet 1',
      description: 'English worksheet description',
      schoolId: 'school-a-id',
      generatingStatus: WorksheetGeneratingStatus.GENERATED,
      subjectData: null,
      selectedOptions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorksheetService,
        {
          provide: getRepositoryToken(Worksheet),
          useValue: {
            findAndCount: jest.fn(),
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            softDelete: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(OptionType),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(OptionValue),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(WorksheetOption),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
          },
        },
        {
          provide: WorksheetQueueService,
          useValue: {
            addWorksheetGenerationJob: jest.fn(),
          },
        },
        {
          provide: getModelToken(WorksheetPromptResult.name),
          useValue: {
            findOne: jest.fn(),
            deleteMany: jest.fn(),
          },
        },
        {
          provide: WorksheetDocumentCacheService,
          useValue: {
            getByWorksheetId: jest.fn(),
          },
        },
        {
          provide: getModelToken(WorksheetDocument.name),
          useValue: {
            deleteMany: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<WorksheetService>(WorksheetService);
    worksheetRepository = module.get(getRepositoryToken(Worksheet));
    optionTypeRepository = module.get(getRepositoryToken(OptionType));
    optionValueRepository = module.get(getRepositoryToken(OptionValue));
    worksheetOptionRepository = module.get(getRepositoryToken(WorksheetOption));
    worksheetQueueService = module.get(WorksheetQueueService);
    worksheetPromptResultModel = module.get(getModelToken(WorksheetPromptResult.name));
    worksheetDocumentCacheService = module.get(WorksheetDocumentCacheService);
    worksheetDocumentModel = module.get(getModelToken(WorksheetDocument.name));
  });

  describe('findAll - School-Based Filtering', () => {
    const listDto: ListWorksheetDto = { page: 1, pageSize: 10 };

    beforeEach(() => {
      worksheetRepository.findAndCount.mockResolvedValue([mockWorksheets as Worksheet[], mockWorksheets.length]);
    });

    describe('Admin User Scenarios', () => {
      it('should allow admin to filter by specific schoolId', async () => {
        const dtoWithSchoolId = { ...listDto, schoolId: 'school-a-id' };
        
        await service.findAll(dtoWithSchoolId, mockAdminUser as User);

        expect(worksheetRepository.findAndCount).toHaveBeenCalledWith(
          expect.objectContaining({
            where: { schoolId: 'school-a-id' },
          })
        );
      });

      it('should allow admin to see all worksheets when no schoolId provided', async () => {
        await service.findAll(listDto, mockAdminUser as User);

        expect(worksheetRepository.findAndCount).toHaveBeenCalledWith(
          expect.objectContaining({
            where: {}, // No schoolId filter
          })
        );
      });
    });

    describe('Non-Admin User Scenarios', () => {
      it('should allow non-admin user to filter by their own schoolId', async () => {
        const dtoWithSchoolId = { ...listDto, schoolId: 'school-a-id' };
        
        await service.findAll(dtoWithSchoolId, mockTeacherUser as User);

        expect(worksheetRepository.findAndCount).toHaveBeenCalledWith(
          expect.objectContaining({
            where: { schoolId: 'school-a-id' },
          })
        );
      });

      it('should throw ForbiddenException when non-admin user tries to access different school', async () => {
        const dtoWithDifferentSchoolId = { ...listDto, schoolId: 'school-b-id' };
        
        await expect(
          service.findAll(dtoWithDifferentSchoolId, mockTeacherUser as User)
        ).rejects.toThrow(ForbiddenException);
        await expect(
          service.findAll(dtoWithDifferentSchoolId, mockTeacherUser as User)
        ).rejects.toThrow('Access denied: Cannot access worksheets from a different school');
      });

      it('should implicitly filter by user schoolId when no schoolId provided', async () => {
        await service.findAll(listDto, mockTeacherUser as User);

        expect(worksheetRepository.findAndCount).toHaveBeenCalledWith(
          expect.objectContaining({
            where: { schoolId: 'school-a-id' },
          })
        );
      });

      it('should throw ForbiddenException when user without school tries to filter by schoolId', async () => {
        const dtoWithSchoolId = { ...listDto, schoolId: 'school-a-id' };
        
        await expect(
          service.findAll(dtoWithSchoolId, mockUserWithoutSchool as User)
        ).rejects.toThrow(ForbiddenException);
        await expect(
          service.findAll(dtoWithSchoolId, mockUserWithoutSchool as User)
        ).rejects.toThrow('User is not associated with any school');
      });

      it('should return empty results for user without school when no schoolId provided', async () => {
        const result = await service.findAll(listDto, mockUserWithoutSchool as User);

        expect(result).toEqual({
          items: [],
          meta: {
            page: 1,
            pageSize: 10,
            total: 0,
            totalPages: 0,
          },
        });
        expect(worksheetRepository.findAndCount).not.toHaveBeenCalled();
      });
    });

    describe('Different User Roles', () => {
      it('should work correctly for INDEPENDENT_TEACHER role', async () => {
        await service.findAll(listDto, mockIndependentTeacherUser as User);

        expect(worksheetRepository.findAndCount).toHaveBeenCalledWith(
          expect.objectContaining({
            where: { schoolId: 'school-b-id' },
          })
        );
      });
    });
  });

  describe('findOne - School-Based Access Control', () => {
    const worksheetId = 'worksheet-1';
    const mockWorksheet: Partial<Worksheet> = {
      id: worksheetId,
      title: 'Test Worksheet',
      description: 'Test worksheet description',
      schoolId: 'school-a-id',
      generatingStatus: WorksheetGeneratingStatus.GENERATED,
      subjectData: null,
      selectedOptions: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    beforeEach(() => {
      worksheetRepository.findOne.mockResolvedValue(mockWorksheet as Worksheet);
      worksheetPromptResultModel.findOne.mockResolvedValue(null);
      worksheetDocumentCacheService.getByWorksheetId.mockResolvedValue(null);
    });

    it('should allow admin to access worksheet from any school', async () => {
      const result = await service.findOne(worksheetId, mockAdminUser as User);

      expect(result).toBeDefined();
      expect(result?.id).toBe(worksheetId);
      expect(worksheetRepository.findOne).toHaveBeenCalledWith({
        where: { id: worksheetId },
        relations: [
          'selectedOptions',
          'selectedOptions.optionType',
          'selectedOptions.optionValue',
        ],
      });
    });

    it('should allow non-admin user to access worksheet from their own school', async () => {
      const result = await service.findOne(worksheetId, mockTeacherUser as User);

      expect(result).toBeDefined();
      expect(result?.id).toBe(worksheetId);
    });

    it('should throw NotFoundException when non-admin user tries to access worksheet from different school', async () => {
      const worksheetFromDifferentSchool = {
        ...mockWorksheet,
        schoolId: 'school-b-id',
      };
      worksheetRepository.findOne.mockResolvedValue(worksheetFromDifferentSchool as Worksheet);

      await expect(
        service.findOne(worksheetId, mockTeacherUser as User)
      ).rejects.toThrow('Worksheet not found');
    });

    it('should return null when non-admin user tries to access worksheet from different school with throwError=false', async () => {
      const worksheetFromDifferentSchool = {
        ...mockWorksheet,
        schoolId: 'school-b-id',
      };
      worksheetRepository.findOne.mockResolvedValue(worksheetFromDifferentSchool as Worksheet);

      const result = await service.findOne(worksheetId, mockTeacherUser as User, false);

      expect(result).toBeNull();
    });
  });
});
