import { <PERSON><PERSON><PERSON>, <PERSON>ToOne, Column } from 'typeorm';
import { Worksheet } from './worksheet.entity';
import { OptionType } from '../../options/entities/option-type.entity';
import { OptionValue } from '../../options/entities/option-value.entity';
import BaseEntity from '../../../core/entities/base-entity';

@Entity('worksheet_options')
export class WorksheetOption extends BaseEntity {
  @ManyToOne(() => Worksheet, (worksheet) => worksheet.selectedOptions, {
    onDelete: 'CASCADE',
  })
  worksheet: Worksheet;

  @ManyToOne(() => OptionType)
  optionType: OptionType;

  @ManyToOne(() => OptionValue)
  optionValue: OptionValue;

  @Column({ nullable: true })
  text: string;

  @Column({ nullable: true })
  count: number;
}
