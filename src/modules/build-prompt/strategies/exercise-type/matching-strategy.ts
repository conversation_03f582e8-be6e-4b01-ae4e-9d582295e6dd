import { ExerciseTypeStrategy } from './exercise-type-strategy.interface';

/**
 * Strategy for generating Matching exercise instructions
 */
export class MatchingStrategy implements ExerciseTypeStrategy {
  generateInstructions(): string {
    return `
    ### MATCHING GUIDELINES
    - Include more items in one column than the other to reduce process of elimination
    - Ensure all items and matches are clearly related to the same concept
    - Keep the list of items to match at a reasonable length (5-10 items)
    - Provide clear instructions on how to indicate matches
    - Ensure there is only one correct match for each item
    - Keep all items in each column homogeneous (all dates, all people, etc.)`;
  }
}
