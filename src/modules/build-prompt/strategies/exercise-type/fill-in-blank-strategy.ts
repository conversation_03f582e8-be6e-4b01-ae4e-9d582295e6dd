import { ExerciseTypeStrategy } from './exercise-type-strategy.interface';

/**
 * Strategy for generating Fill-in-the-Blank exercise instructions
 */
export class FillInBlankStrategy implements ExerciseTypeStrategy {
  generateInstructions(): string {
    return `
    ### FILL-IN-THE-<PERSON>LANK GUIDELINES
    - Ensure blanks test key concepts or vocabulary
    - Provide clear context clues in the surrounding text
    - Limit the number of blanks in a single sentence
    - Ensure there is only one correct answer for each blank
    - Place blanks at strategic points to test understanding
    - Avoid excessive use of blanks that disrupt reading flow`;
  }
}
