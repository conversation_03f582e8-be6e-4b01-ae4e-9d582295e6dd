import { ExerciseTypeStrategy } from './exercise-type-strategy.interface';
import { CompositeExerciseTypeStrategy } from './composite-exercise-type-strategy';
import { MultipleChoiceStrategy } from './multiple-choice-strategy';
import { SingleChoiceStrategy } from './single-choice-strategy';
import { OpenEndedStrategy } from './open-ended-strategy';
import { FillInBlankStrategy } from './fill-in-blank-strategy';
import { MatchingStrategy } from './matching-strategy';
import { CreativeWritingStrategy } from './creative-writing-strategy';

/**
 * Factory for creating exercise type instruction strategies
 */
export class ExerciseTypeFactory {
  /**
   * Create a composite strategy based on the exercise types
   * @param exerciseTypes Array of exercise types
   * @returns A composite strategy containing all relevant exercise type strategies
   */
  static createStrategy(exerciseTypes: string[]): ExerciseTypeStrategy {
    const composite = new CompositeExerciseTypeStrategy();
    
    if (!exerciseTypes || exerciseTypes.length === 0) {
      return composite; // Return empty composite if no exercise types
    }
    
    // Check for multiple choice questions
    if (exerciseTypes.some(type => 
      type.toLowerCase().includes('multiple choice') || 
      type.toLowerCase().includes('mcq')
    )) {
      composite.addStrategy(new MultipleChoiceStrategy());
    }
    
    // Check for single choice questions
    if (exerciseTypes.some(type => 
      type.toLowerCase().includes('single choice') || 
      type.toLowerCase() === 'scq'
    )) {
      composite.addStrategy(new SingleChoiceStrategy());
    }
    
    // Check for open-ended questions
    if (exerciseTypes.some(type => 
      type.toLowerCase().includes('open-ended') || 
      type.toLowerCase().includes('free response')
    )) {
      composite.addStrategy(new OpenEndedStrategy());
    }
    
    // Check for fill-in-the-blank questions
    if (exerciseTypes.some(type => 
      type.toLowerCase().includes('fill blank') || 
      type.toLowerCase().includes('fill_blank') ||
      type.toLowerCase().includes('fill in the blank') || 
      type.toLowerCase().includes('completion')
    )) {
      composite.addStrategy(new FillInBlankStrategy());
    }
    
    // Check for matching questions
    if (exerciseTypes.some(type => 
      type.toLowerCase().includes('matching')
    )) {
      composite.addStrategy(new MatchingStrategy());
    }
    
    // Check for creative writing questions
    if (exerciseTypes.some(type => 
      type.toLowerCase().includes('creative writing') || 
      type.toLowerCase().includes('creative') ||
      type.toLowerCase().includes('writing') ||
      type.toLowerCase().includes('essay')
    )) {
      composite.addStrategy(new CreativeWritingStrategy());
    }
    
    return composite;
  }
}
