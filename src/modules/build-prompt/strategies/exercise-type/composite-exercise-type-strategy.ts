import { ExerciseTypeStrategy } from './exercise-type-strategy.interface';

/**
 * Composite strategy for combining multiple exercise type strategies
 */
export class CompositeExerciseTypeStrategy implements ExerciseTypeStrategy {
  private strategies: ExerciseTypeStrategy[] = [];
  
  /**
   * Add a strategy to the composite
   * @param strategy The strategy to add
   */
  addStrategy(strategy: ExerciseTypeStrategy): void {
    this.strategies.push(strategy);
  }
  
  /**
   * Generate instructions by combining all strategies
   * @returns Combined instructions from all strategies
   */
  generateInstructions(): string {
    let instructions = '## EXERCISE TYPE-SPECIFIC GUIDELINES';
    
    // If no strategies, return empty guidelines
    if (this.strategies.length === 0) {
      return instructions;
    }
    
    // Combine instructions from all strategies
    for (const strategy of this.strategies) {
      instructions += strategy.generateInstructions();
    }
    
    return instructions;
  }
}
