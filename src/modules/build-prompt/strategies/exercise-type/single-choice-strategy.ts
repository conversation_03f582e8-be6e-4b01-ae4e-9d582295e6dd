import { ExerciseTypeStrategy } from './exercise-type-strategy.interface';

/**
 * Strategy for generating Single Choice exercise instructions
 */
export class SingleChoiceStrategy implements ExerciseTypeStrategy {
  generateInstructions(): string {
    return `
    ### SINGLE CHOICE GUIDELINES
    - Include exactly 4 options for each question
    - Ensure only one option is correct
    - Make all distractors plausible and relevant
    - Avoid giving clues to the correct answer in the question
    - Make options similar in length and grammatical structure
    - Ensure the correct answer is not always in the same position`;
  }
}
