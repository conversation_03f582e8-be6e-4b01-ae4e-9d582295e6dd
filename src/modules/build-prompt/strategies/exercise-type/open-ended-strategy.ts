import { ExerciseTypeStrategy } from './exercise-type-strategy.interface';

/**
 * Strategy for generating Open-Ended exercise instructions
 */
export class OpenEndedStrategy implements ExerciseTypeStrategy {
  generateInstructions(): string {
    return `
    ### OPEN-ENDED GUIDELINES
    - Provide clear parameters for expected response length and format
    - Include specific criteria for what constitutes a complete answer
    - Ensure questions are specific enough to guide student responses
    - Provide detailed rubrics or answer guidelines in the explanation
    - Include sample responses of varying quality in the explanation
    - Ensure questions promote higher-order thinking skills`;
  }
}
