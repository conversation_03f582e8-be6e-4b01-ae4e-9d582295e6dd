import { ExerciseTypeStrategy } from './exercise-type-strategy.interface';

/**
 * Strategy for generating Multiple Choice exercise instructions
 */
export class MultipleChoiceStrategy implements ExerciseTypeStrategy {
  generateInstructions(): string {
    return `
    ### MULTIPLE CHOICE GUIDELINES
    - Include exactly 4 options for each question
    - Include 2-3 correct answers per question
    - Ensure all options are plausible and relevant to the question
    - Avoid obvious incorrect options or "filler" options
    - Make options similar in length and grammatical structure
    - Avoid using "All of the above" or "None of the above" options
    - Ensure options are mutually exclusive when appropriate`;
  }
}
