import { SvgPromptStrategy } from './svg-prompt-strategy.interface';

/**
 * Default strategy for generating SVG prompts when no specific subject is provided
 */
export class DefaultSvgStrategy implements SvgPromptStrategy {
  getSubjectName(): string {
    return 'Educational';
  }

  generateInstructions(): string {
    return `
    # SUBJECT-SPECIFIC INSTRUCTIONS
    - Create clear, simple visual representations of key concepts
    - Label all important elements with appropriate terminology
    - Use consistent visual style throughout the diagram
    - Organize information in a logical, easy-to-follow manner
    - Include only elements explicitly mentioned in the question
    - Maintain appropriate proportions for all visual elements
    - Use standard conventions for the subject area when applicable
    
    ## For Diagrams:
    - Use simple shapes to represent objects or concepts
    - Connect related elements with appropriate lines or arrows
    - Label all important parts clearly
    - Maintain proper spatial relationships between elements
    
    ## For Charts and Graphs:
    - Use appropriate chart type for the data being represented
    - Label all axes, data points, and categories clearly
    - Ensure visual proportions match the numerical data
    - Include a simple legend if multiple data series are shown
    
    ## For Process Flows:
    - Use directional arrows to show sequence or relationships
    - Number steps if order is important
    - Group related elements visually
    - Use consistent symbols for similar concepts`;
  }
}
