import { SvgPromptStrategy } from './svg-prompt-strategy.interface';
import { MathematicsSvgStrategy } from './mathematics-svg-strategy';
import { ScienceSvgStrategy } from './science-svg-strategy';
import { SocialStudiesSvgStrategy } from './social-studies-svg-strategy';
import { LanguageArtsSvgStrategy } from './language-arts-svg-strategy';
import { ArtsSvgStrategy } from './arts-svg-strategy';
import { DefaultSvgStrategy } from './default-svg-strategy';

/**
 * Factory for creating SVG prompt strategies based on subject
 */
export class SvgStrategyFactory {
  /**
   * Create a strategy based on the subject
   * @param topic The subject/topic
   * @returns The appropriate SVG prompt strategy
   */
  static createStrategy(topic?: string): SvgPromptStrategy {
    if (!topic) {
      return new DefaultSvgStrategy();
    }

    const normalizedTopic = topic.toLowerCase();

    // Match the topic to the appropriate strategy
    if (normalizedTopic === 'mathematics' || normalizedTopic === 'math') {
      return new MathematicsSvgStrategy();
    } else if (['science', 'physics', 'chemistry', 'biology'].includes(normalizedTopic)) {
      return new ScienceSvgStrategy();
    } else if (['geography', 'history', 'social studies'].includes(normalizedTopic)) {
      return new SocialStudiesSvgStrategy();
    } else if (['english', 'language arts', 'literature'].includes(normalizedTopic)) {
      return new LanguageArtsSvgStrategy();
    } else if (['art', 'music', 'visual arts', 'performing arts'].includes(normalizedTopic)) {
      return new ArtsSvgStrategy();
    } else {
      // For any other subject, use the default strategy
      return new DefaultSvgStrategy();
    }
  }
}
