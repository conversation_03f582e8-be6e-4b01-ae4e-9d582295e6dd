import { SvgPromptStrategy } from './svg-prompt-strategy.interface';

/**
 * Strategy for generating Mathematics-specific SVG prompts
 */
export class MathematicsSvgStrategy implements SvgPromptStrategy {
  getSubjectName(): string {
    return 'Mathematics';
  }

  generateInstructions(): string {
    return `
    # MATHEMATICS-SPECIFIC INSTRUCTIONS
    - Draw all mathematical objects with precise proportions and measurements
    - Maintain exact scale for all measurements (e.g., 8m should be exactly half of 16m)
    - For geometric shapes, label all vertices with capital letters (A, B, C, etc.)
    - For graphs, use properly scaled and labeled axes with clear tick marks
    - For fractions, show clear divisions with exact proportional areas
    - For statistics, ensure data visualizations accurately represent the values
    - For word problems, extract only the key visual elements mentioned
    - Include all measurements explicitly stated in the problem
    - Never include solutions or calculations that would answer the question
    - Position all labels with WHITE BACKGROUND rectangles to ensure readability
    - Ensure NO overlapping of labels, lines, or shapes in mathematical diagrams
    - DO NOT include the full question text inside the SVG - focus only on the visual representation

    ## IMPROVED TEXT HANDLING
    - Use a structured layout with clear separation between diagram elements
    - For text labels, always use a white background rectangle that extends 4px beyond the text on all sides
    - Position text labels with at least 15px clearance from any lines or shapes
    - Use consistent font sizes: 16px for main labels, 14px for secondary labels, 12px for small annotations
    - For longer text, break it into multiple lines rather than creating one long text element
    - Use dominant-baseline="middle" and text-anchor="middle" for centered text positioning
    - Group related text elements together using <g> tags

    ## For Coordinate Systems/Graphs:
    - X and Y axes must be perfectly straight with 90° angle at origin
    - Use consistent tick marks with equal spacing (10px or 20px intervals)
    - Label origin as (0,0) and include axis labels (x, y) at the ends
    - Place axis labels with sufficient spacing from tick marks and tick labels
    - For multiple functions on the same graph, use different line styles and a legend
    - Include a grid background with light gray lines (stroke="#e0e0e0") for better readability
    - Ensure all functions are drawn with mathematically accurate points

    ## For Geometric Shapes:
    - Maintain perfect proportions (squares must have equal sides, circles must be perfect)
    - Label all vertices with letters (A, B, C...) and all sides with their measurements
    - For angles, use small arc symbols with proper angle measurements (if specified)
    - Position vertex labels OUTSIDE the shape with sufficient spacing (at least 10px)
    - For complex shapes, ensure all labels are clearly associated with their vertices
    - Use leader lines when necessary to connect labels to vertices in crowded areas
    - For 3D shapes, use consistent perspective and dashed lines for hidden edges

    ## For Pie Charts:
    - Use ONLY ONE circle with precisely calculated sectors based on percentages
    - Each sector must have mathematically accurate angles (e.g., 25% = exactly 90°)
    - Label each sector clearly outside the circle with a thin connector line
    - Position labels radially outward from their sectors with NO OVERLAP
    - Use consistent spacing between labels and the edge of the circle
    - Use light fill colors to distinguish sectors (e.g., #e6f7ff, #e6ffe6, #fff0e6)
    - Include percentage or fraction values in the labels

    ## For Word Problems with Multiple Elements:
    - Organize elements with clear spatial separation between different objects
    - Use a logical layout that matches the problem description
    - Label all elements mentioned in the problem with consistent formatting
    - For distance problems, maintain exact proportional distances
    - Ensure all labels have white background rectangles for readability
    - Use a structured layout with proper alignment of related elements
    - Group related items visually (e.g., using light background rectangles)

    ## For Fractions and Ratios:
    - Show fractions as divided rectangles or circles with precisely proportioned sections
    - Label each section clearly with its value or representation
    - For equivalent fractions, show multiple representations side by side with equal signs
    - For comparing fractions, use equal-sized wholes with different divisions
    - Use consistent coloring for numerators and denominators
    - Include a visual representation of the whole unit for context

    ## For Probability:
    - For probability problems, clearly show all possible outcomes
    - Use tree diagrams with properly labeled branches and probabilities
    - For dice or card problems, show accurate representations of the items
    - Label each outcome with its probability value (as fraction or decimal)
    - Use color coding to distinguish between different events or outcomes
    - Ensure the sum of all probabilities equals 1 for complete sample spaces

    ## VISUAL STRUCTURE AND LAYOUT
    - Use a clean, organized layout with clear visual hierarchy
    - Implement a light background (#f8f9fa) for the entire SVG
    - Group related elements together using <g> tags
    - Position the main diagram centrally in the viewBox
    - Use consistent spacing between elements (minimum 20px)
    - Add subtle visual enhancements like rounded corners (rx="4" ry="4") for rectangles
    - Use a larger viewBox (600x400 or 600x500) to prevent cutoffs and allow proper spacing
    - Ensure the diagram is properly centered and scaled to fit the viewBox`;
  }
}
