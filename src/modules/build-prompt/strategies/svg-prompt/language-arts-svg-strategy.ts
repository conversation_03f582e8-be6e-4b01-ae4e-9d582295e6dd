import { SvgPromptStrategy } from './svg-prompt-strategy.interface';

/**
 * Strategy for generating Language Arts-specific SVG prompts
 */
export class LanguageArtsSvgStrategy implements SvgPromptStrategy {
  getSubjectName(): string {
    return 'Language Arts';
  }

  generateInstructions(): string {
    return `
    # LANGUAGE ARTS-SPECIFIC INSTRUCTIONS
    - For grammar concepts, use clear visual organization (sentence diagrams, etc.)
    - For story elements, use simple icons or symbols to represent concepts
    - For text structure, use flowcharts or organizational diagrams
    - For character relationships, use connection diagrams with labeled lines
    - For vocabulary, pair words with simple visual representations
    - For writing process, use clear sequential steps with directional arrows
    - Keep text minimal and focused on key terms or concepts
    - Use visual hierarchy to show relationships between elements
    
    ## For Grammar Diagrams:
    - For sentence structure, use standard sentence diagramming techniques
    - Label parts of speech clearly (noun, verb, adjective, etc.)
    - Use different line styles to show different grammatical relationships
    - Position related elements close to each other
    
    ## For Literary Analysis:
    - For plot diagrams, use standard exposition-conflict-climax-resolution structure
    - For character maps, use nodes for characters and labeled edges for relationships
    - For thematic elements, use symbolic representations connected to text evidence
    - For comparative analysis, use clear visual organization showing similarities/differences
    
    ## For Writing Structure:
    - For essay organization, show clear hierarchical structure
    - For argument flow, use directional arrows between connected ideas
    - For revision process, use cyclical or sequential flow diagrams
    - Label all components with relevant writing terminology`;
  }
}
