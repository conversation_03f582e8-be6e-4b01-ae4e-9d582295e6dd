import { SvgPromptStrategy } from './svg-prompt-strategy.interface';

/**
 * Strategy for generating Arts-specific SVG prompts
 */
export class ArtsSvgStrategy implements SvgPromptStrategy {
  getSubjectName(): string {
    return 'Arts';
  }

  generateInstructions(): string {
    return `
    # ARTS-SPECIFIC INSTRUCTIONS
    - For color theory, use accurate color representations
    - For music notation, follow standard musical notation practices
    - For art techniques, show simplified examples of the technique
    - For instrument diagrams, label key parts accurately
    - For composition concepts, use visual examples with clear organization
    - For artistic movements, use representative visual elements
    - For rhythm or tempo, use standard notation or visual patterns
    - Keep diagrams clean and focused on the specific concept
    
    ## For Visual Arts:
    - For color wheels, use accurate color relationships and spacing
    - For composition principles, show clear examples of balance, contrast, etc.
    - For perspective, use proper vanishing points and horizon lines
    - For art elements, clearly label line, shape, form, texture, etc.
    
    ## For Music:
    - For musical notation, use standard staff, clefs, and note symbols
    - For chord diagrams, use standard notation for the instrument
    - For musical structure, use clear sections with proper labeling
    - For rhythm patterns, use standard note values and time signatures
    
    ## For Drama/Theater:
    - For stage layouts, use standard stage direction terminology
    - For blocking diagrams, show clear actor positions and movements
    - For lighting designs, use standard symbols and directional indicators
    - For set designs, use clear spatial relationships and measurements`;
  }
}
