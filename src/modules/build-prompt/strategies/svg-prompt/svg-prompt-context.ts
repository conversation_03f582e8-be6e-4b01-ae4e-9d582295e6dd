import { SvgPromptStrategy } from './svg-prompt-strategy.interface';

/**
 * Context class for the SVG prompt strategy pattern
 */
export class SvgPromptContext {
  private strategy: SvgPromptStrategy;

  /**
   * Set the strategy to use
   * @param strategy The SVG prompt strategy to use
   */
  setStrategy(strategy: SvgPromptStrategy): void {
    this.strategy = strategy;
  }

  /**
   * Generate the complete SVG prompt using the current strategy
   * @param imagePrompt The question content to illustrate
   * @returns The complete SVG prompt
   */
  generatePrompt(imagePrompt: string): string {
    const subjectName = this.strategy.getSubjectName();
    const subjectInstructions = this.strategy.generateInstructions();

    // Extract any additional instructions that might have been added to the imagePrompt
    let questionContent = imagePrompt;
    let additionalInstructions = '';

    // Check if the imagePrompt contains additional instructions (marked by # or IMPORTANT:)
    if (imagePrompt.includes('\n\n#') || imagePrompt.includes('\n\nIMPORTANT:')) {
      // Split the content at the first occurrence of double newline followed by # or IMPORTANT:
      const splitIndex = Math.min(
        imagePrompt.indexOf('\n\n#') >= 0 ? imagePrompt.indexOf('\n\n#') : Number.MAX_SAFE_INTEGER,
        imagePrompt.indexOf('\n\nIMPORTANT:') >= 0 ? imagePrompt.indexOf('\n\nIMPORTANT:') : Number.MAX_SAFE_INTEGER
      );

      if (splitIndex < Number.MAX_SAFE_INTEGER) {
        questionContent = imagePrompt.substring(0, splitIndex);
        additionalInstructions = imagePrompt.substring(splitIndex);
      }
    }

    return `You are an expert SVG diagram creator for educational content. Create a precise SVG diagram that illustrates the following ${subjectName.toLowerCase()} question:

    QUESTION TO ILLUSTRATE: ${questionContent}
    SUBJECT: ${subjectName}
${subjectInstructions}

    ${additionalInstructions ? `# ADDITIONAL SPECIFIC INSTRUCTIONS FOR THIS QUESTION
${additionalInstructions}` : ''}

    # DIAGRAM CREATION INSTRUCTIONS
    1. First, carefully analyze the question to identify what needs to be visualized
    2. Extract all measurements, shapes, and spatial relationships mentioned
    3. Create ONLY the visual elements explicitly mentioned in the question
    4. NEVER include solutions, calculations, or hints that would answer the question
    5. Focus on creating a clear, accurate representation that helps understand the problem
    6. DO NOT include the full question text inside the SVG - focus only on the visual representation
    7. Create a structured layout with clear visual hierarchy and proper spacing

    # SVG TECHNICAL SPECIFICATIONS
    - viewBox="0 0 600 400" with preserveAspectRatio="xMidYMid meet" (LARGER CANVAS to prevent cutoffs)
    - width="100%" height="100%"
    - Use a light background for the entire SVG: <rect width="600" height="400" fill="#f8f9fa" rx="8" ry="8"/>
    - All elements must use these exact attributes:
      * Lines: stroke="#000000" stroke-width="1.5" fill="none"
      * Shapes: stroke="#000000" stroke-width="1.5" fill="none"
      * Text: font-family="Arial, sans-serif" font-size="14px" fill="#000000"
      * Text positioning: dominant-baseline="middle" text-anchor="middle"
      * Text background (REQUIRED for ALL text): <rect> with fill="white" stroke="none" width and height +8px larger than text, with rx="4" ry="4" for rounded corners
      * Text labels: ALWAYS place in clear space with NO overlapping elements

    # IMPROVED TEXT HANDLING
    - DO NOT include the full question text inside the SVG - focus only on the visual representation
    - For text labels, always use a white background rectangle that extends 4px beyond the text on all sides
    - Position text labels with at least 15px clearance from any lines or shapes
    - Use consistent font sizes: 16px for main labels, 14px for secondary labels, 12px for small annotations
    - For longer text, break it into multiple lines rather than creating one long text element
    - Group related text elements together using <g> tags
    - Use proper text anchoring: text-anchor="middle" for centered text, "start" for left-aligned, "end" for right-aligned

    # PRECISE MEASUREMENTS & SCALING
    - ALL measurements must be EXACTLY proportional (e.g., 8m should be EXACTLY half of 16m)
    - ALL angles must be mathematically accurate (e.g., 90° must be EXACTLY perpendicular)
    - Use absolute coordinates for all elements (M100,100 L200,200 not relative commands)
    - Maintain 50px minimum padding on all sides from the viewBox edges to prevent cutoffs
    - Scale all elements appropriately to fit within the viewBox while maintaining proportions
    - If the diagram is complex, simplify it while preserving the essential elements

    # ELEMENT SEPARATION & POSITIONING
    - NO OVERLAPPING ELEMENTS EVER - maintain minimum 20px spacing between any elements
    - Position ALL labels with minimum 15px clearance from any line, point, or shape
    - For parallel lines, maintain equal and consistent spacing of at least 30px
    - For angle markings, use small arcs positioned at least 15px from the vertex
    - Place labels in logical positions that clearly indicate what they're labeling
    - Use leader lines (thin connecting lines) when necessary to connect labels to their elements
    - Stagger labels at different distances when multiple labels are needed in the same area
    - Group related elements together with consistent spacing

    # VISUAL STRUCTURE AND LAYOUT
    - Use a clean, organized layout with clear visual hierarchy
    - Implement a light background (#f8f9fa) for the entire SVG
    - Group related elements together using <g> tags
    - Position the main diagram centrally in the viewBox
    - Use consistent spacing between elements (minimum 20px)
    - Add subtle visual enhancements like rounded corners (rx="4" ry="4") for rectangles
    - Use a larger viewBox (600x400 or 600x500) to prevent cutoffs and allow proper spacing
    - Ensure the diagram is properly centered and scaled to fit the viewBox

    # COMMON ERRORS TO AVOID
    - NEVER place text directly on top of lines or shapes - ALWAYS use text background rectangles
    - NEVER use random spacing between elements - maintain consistent distances
    - NEVER include elements not mentioned in the question
    - NEVER include calculations or answers that would solve the problem
    - NEVER use decorative elements or unnecessary styling
    - NEVER use relative positioning that could cause overlap
    - NEVER allow labels to overlap with each other or with diagram elements
    - NEVER place elements too close to the edge of the viewBox
    - NEVER cut off any part of the diagram - ensure everything is fully visible
    - NEVER create diagrams that are too cluttered - simplify when necessary

    # LABEL PLACEMENT TECHNIQUES
    - For crowded areas, use these techniques to prevent overlap:
      * Use leader lines with small dots or arrows to connect labels to elements
      * Place labels outside the main diagram area and connect with thin lines
      * Use a consistent labeling scheme (e.g., A, B, C or 1, 2, 3) with a legend if needed
      * For multiple labels in the same area, stagger them at different distances
      * Use different text alignments (left, center, right) to optimize space usage

    # OUTPUT REQUIREMENTS
    1. Your response MUST contain ONLY valid SVG code starting with <svg and ending with </svg>
    2. DO NOT include any explanations, comments, or text outside the SVG code block
    3. DO NOT use markdown formatting or code blocks
    4. DO NOT include any HTML except the SVG element itself
    5. DO NOT include the full question text inside the SVG - focus only on the visual representation
    6. Ensure the SVG is valid, self-contained, and ready to be directly embedded in an HTML document
    7. VERIFY before submitting that NO elements overlap and NOTHING is cut off
    8. Use a larger viewBox (600x400 or 600x500) to ensure nothing is cut off

    RESPOND ONLY WITH VALID SVG CODE AND NOTHING ELSE.`;
  }
}
