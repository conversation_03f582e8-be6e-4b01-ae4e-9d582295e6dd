import { SvgPromptStrategy } from './svg-prompt-strategy.interface';

/**
 * Strategy for generating Science-specific SVG prompts
 */
export class ScienceSvgStrategy implements SvgPromptStrategy {
  getSubjectName(): string {
    return 'Science';
  }

  generateInstructions(): string {
    return `
    # SCIENCE-SPECIFIC INSTRUCTIONS
    - For physics diagrams, use standard symbols for forces, energy, and motion
    - For chemical structures, use proper bond angles and atomic representations
    - For biological diagrams, use simplified but accurate representations of structures
    - For experimental setups, show equipment in proper arrangement
    - For processes/cycles, use clear directional arrows to show sequence
    - Label all components clearly with scientific terminology
    - Include a scale or reference measurement when appropriate
    - For graphs, ensure axes are properly labeled with units
    
    ## For Physics Diagrams:
    - Use standard vector notation for forces with proper direction and magnitude
    - For motion diagrams, show clear trajectory paths with direction indicators
    - For circuit diagrams, use standard electrical symbols and connection lines
    - For optics, show light rays with proper reflection/refraction angles
    
    ## For Chemistry Diagrams:
    - Use proper molecular geometry and bond angles
    - For reactions, use clear arrow notation between reactants and products
    - For lab equipment, use standard representations of glassware and apparatus
    - Label atoms, functional groups, and important molecular features
    
    ## For Biology Diagrams:
    - For cellular structures, use clear boundaries and internal components
    - For anatomical diagrams, use proper proportions and relationships
    - For ecological relationships, use directional arrows to show interactions
    - For classification, use proper hierarchical organization`;
  }
}
