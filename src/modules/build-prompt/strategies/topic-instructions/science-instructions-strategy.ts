import { TopicInstructionsStrategy } from './topic-instructions-strategy.interface';

/**
 * Strategy for generating Science-specific instructions
 */
export class ScienceInstructionsStrategy implements TopicInstructionsStrategy {
  generateInstructions(): string {
    return `
    ## SCIENCE-SPECIFIC GUIDELINES
    - Balance conceptual understanding with factual knowledge
    - Include questions that test scientific reasoning and process skills
    - Use accurate scientific terminology appropriate for the grade level
    - For experimental scenarios, include clear procedures and observations
    - Include questions about scientific methods and inquiry processes
    - Ensure all scientific facts and concepts are accurate and up-to-date
    - For diagrams or models, ensure they accurately represent scientific concepts
    - Include questions that connect scientific concepts to everyday phenomena
    - For classification questions, use clear categories and examples`;
  }
}
