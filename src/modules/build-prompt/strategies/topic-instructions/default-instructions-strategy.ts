import { TopicInstructionsStrategy } from './topic-instructions-strategy.interface';

/**
 * Default strategy for generating topic instructions when no specific topic is matched
 */
export class DefaultInstructionsStrategy implements TopicInstructionsStrategy {
  generateInstructions(): string {
    return `
    ## SUBJECT-SPECIFIC GUIDELINES
    - Balance factual knowledge with conceptual understanding
    - Include questions that promote critical thinking and application
    - Use terminology and concepts appropriate for the grade level
    - Include a variety of question formats to assess different skills
    - Ensure content is accurate, up-to-date, and educationally relevant
    - Include questions that connect concepts to real-world applications
    - Balance lower-order and higher-order thinking skills
    - Include questions that assess both breadth and depth of understanding`;
  }
}
