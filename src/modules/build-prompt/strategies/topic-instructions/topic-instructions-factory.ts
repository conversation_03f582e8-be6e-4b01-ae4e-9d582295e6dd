import { TopicInstructionsStrategy } from './topic-instructions-strategy.interface';
import { MathematicsInstructionsStrategy } from './mathematics-instructions-strategy';
import { ScienceInstructionsStrategy } from './science-instructions-strategy';
import { LanguageArtsInstructionsStrategy } from './language-arts-instructions-strategy';
import { SocialStudiesInstructionsStrategy } from './social-studies-instructions-strategy';
import { DefaultInstructionsStrategy } from './default-instructions-strategy';

/**
 * Factory for creating topic-specific instruction strategies
 */
export class TopicInstructionsFactory {
  /**
   * Create a strategy based on the topic
   * @param topic The subject/topic
   * @returns The appropriate topic instructions strategy
   */
  static createStrategy(topic: string): TopicInstructionsStrategy {
    if (!topic) {
      return new DefaultInstructionsStrategy();
    }

    const normalizedTopic = topic.toLowerCase();
    
    // Mathematics-related topics
    if (
      normalizedTopic.includes('math') || 
      normalizedTopic.includes('algebra') || 
      normalizedTopic.includes('geometry') || 
      normalizedTopic.includes('calculus') ||
      normalizedTopic.includes('arithmetic') ||
      normalizedTopic.includes('statistics') ||
      normalizedTopic.includes('probability')
    ) {
      return new MathematicsInstructionsStrategy();
    }
    
    // Science-related topics
    else if (
      normalizedTopic.includes('science') || 
      normalizedTopic.includes('biology') || 
      normalizedTopic.includes('chemistry') || 
      normalizedTopic.includes('physics') ||
      normalizedTopic.includes('environment')
    ) {
      return new ScienceInstructionsStrategy();
    }
    
    // Language Arts/English topics
    else if (
      normalizedTopic.includes('english') || 
      normalizedTopic.includes('language') || 
      normalizedTopic.includes('literature') || 
      normalizedTopic.includes('writing') ||
      normalizedTopic.includes('reading') ||
      normalizedTopic.includes('grammar')
    ) {
      return new LanguageArtsInstructionsStrategy();
    }
    
    // Social Studies/History topics
    else if (
      normalizedTopic.includes('history') || 
      normalizedTopic.includes('social studies') || 
      normalizedTopic.includes('geography') || 
      normalizedTopic.includes('economics') ||
      normalizedTopic.includes('civics') ||
      normalizedTopic.includes('culture')
    ) {
      return new SocialStudiesInstructionsStrategy();
    }
    
    // Default for other subjects
    else {
      return new DefaultInstructionsStrategy();
    }
  }
}
