import { TopicInstructionsStrategy } from './topic-instructions-strategy.interface';

/**
 * Strategy for generating Mathematics-specific instructions
 */
export class MathematicsInstructionsStrategy implements TopicInstructionsStrategy {
  generateInstructions(): string {
    return `
    ## MATHEMATICS-SPECIFIC GUIDELINES
    - Focus on practical problem-solving rather than theoretical concepts
    - Include real-world applications and contexts relevant to students' lives
    - Ensure all numerical values are reasonable and calculations are accurate
    - For word problems, provide clear scenarios with all necessary information
    - Include appropriate visual representations (graphs, diagrams, etc.) when helpful
    - Ensure mathematical notation is consistent and correct
    - For geometry questions, include precise measurements and clear spatial relationships
    - For algebraic expressions, ensure they are properly formatted and solvable
    - Vary question types to assess different mathematical skills (computation, reasoning, application)
    - Don't include math formulas in the options if the questions is asking to calculate something.
    `;
  }
}
