import { Injectable, Logger } from '@nestjs/common';
import {
  ValidationResult,
  BatchValidationResult,
  ValidationConfig,
  QuestionForValidation,
  ValidationIssue
} from './interfaces/validation.interface';
import { PoolMonitoringService } from '../monitoring/services/pool-monitoring.service';
import { ValidationAttemptEvent } from '../monitoring/interfaces/monitoring-events.interface';
import { v4 as uuidv4 } from 'uuid';

/**
 * Content validation service for educational questions
 * This is a basic implementation that can be extended with more sophisticated validation logic
 */
@Injectable()
export class ContentValidationService {
  private readonly logger = new Logger(ContentValidationService.name);

  constructor(
    private readonly poolMonitoringService: PoolMonitoringService,
  ) {}

  private readonly defaultConfig: ValidationConfig = {
    enableFormatValidation: true,
    enableContentValidation: true,
    enableAppropriatenessCheck: true,
    enableStructureValidation: true,
    enableCulturalSensitivityCheck: true,
    minimumPassingScore: 0.7,
    strictMode: false,
  };

  /**
   * Validate a single question
   * @param question The question to validate
   * @param config Optional validation configuration
   * @returns Validation result
   */
  async validateQuestion(
    question: QuestionForValidation,
    config: Partial<ValidationConfig> = {}
  ): Promise<ValidationResult> {
    const startTime = Date.now();
    const validationConfig = { ...this.defaultConfig, ...config };
    const issues: ValidationIssue[] = [];
    let score = 1.0;

    try {
      // Format validation
      if (validationConfig.enableFormatValidation) {
        const formatIssues = this.validateFormat(question);
        issues.push(...formatIssues);
        score -= formatIssues.length * 0.1;
      }

      // Structure validation
      if (validationConfig.enableStructureValidation) {
        const structureIssues = this.validateStructure(question);
        issues.push(...structureIssues);
        score -= structureIssues.length * 0.15;
      }

      // Content validation
      if (validationConfig.enableContentValidation) {
        const contentIssues = this.validateContent(question);
        issues.push(...contentIssues);
        score -= contentIssues.length * 0.2;
      }

      // Appropriateness check
      if (validationConfig.enableAppropriatenessCheck) {
        const appropriatenessIssues = this.validateAppropriateness(question);
        issues.push(...appropriatenessIssues);
        score -= appropriatenessIssues.length * 0.25;
      }

      // Cultural sensitivity check
      if (validationConfig.enableCulturalSensitivityCheck) {
        const culturalIssues = this.validateCulturalSensitivity(question);
        issues.push(...culturalIssues);
        score -= culturalIssues.length * 0.3;
      }

      // Ensure score doesn't go below 0
      score = Math.max(0, score);

      // Check if validation passes
      const hasCriticalIssues = issues.some(issue => issue.severity === 'critical');
      const isValid = validationConfig.strictMode
        ? !hasCriticalIssues && score >= validationConfig.minimumPassingScore
        : score >= validationConfig.minimumPassingScore;

      const result = {
        isValid,
        score,
        issues,
        metadata: {
          questionId: question.id,
          questionType: question.type,
          validationTime: new Date().toISOString(),
        },
      };

      // Emit monitoring event for analytics
      await this.emitValidationEvent(question, result, Date.now() - startTime);

      return result;
    } catch (error) {
      this.logger.error(`Error validating question: ${error.message}`, error.stack);
      return {
        isValid: false,
        score: 0,
        issues: [{
          type: 'format',
          severity: 'critical',
          message: `Validation error: ${error.message}`,
        }],
      };
    }
  }

  /**
   * Validate multiple questions in batch
   * @param questions Array of questions to validate
   * @param config Optional validation configuration
   * @returns Batch validation result
   */
  async validateQuestions(
    questions: QuestionForValidation[],
    config: Partial<ValidationConfig> = {}
  ): Promise<BatchValidationResult> {
    const startTime = Date.now();
    const results: ValidationResult[] = [];

    for (const question of questions) {
      const result = await this.validateQuestion(question, config);
      results.push(result);
    }

    const passed = results.filter(r => r.isValid).length;
    const failed = results.length - passed;
    const successRate = results.length > 0 ? passed / results.length : 0;
    const averageScore = results.length > 0 
      ? results.reduce((sum, r) => sum + r.score, 0) / results.length 
      : 0;

    return {
      results,
      summary: {
        totalValidated: results.length,
        passed,
        failed,
        successRate,
        averageScore,
      },
      processingTime: Date.now() - startTime,
    };
  }

  /**
   * Validate question format
   */
  private validateFormat(question: QuestionForValidation): ValidationIssue[] {
    const issues: ValidationIssue[] = [];

    if (!question.content || question.content.trim().length === 0) {
      issues.push({
        type: 'format',
        severity: 'critical',
        message: 'Question content is required',
        field: 'content',
      });
    }

    if (!question.type || question.type.trim().length === 0) {
      issues.push({
        type: 'format',
        severity: 'critical',
        message: 'Question type is required',
        field: 'type',
      });
    }

    // Validate multiple choice questions have options
    if (question.type === 'multiple_choice' || question.type === 'single_choice') {
      if (!question.options || question.options.length < 2) {
        issues.push({
          type: 'format',
          severity: 'high',
          message: 'Multiple choice questions must have at least 2 options',
          field: 'options',
        });
      }
    }

    return issues;
  }

  /**
   * Validate question structure
   */
  private validateStructure(question: QuestionForValidation): ValidationIssue[] {
    const issues: ValidationIssue[] = [];

    // Check content length
    if (question.content && question.content.length > 1000) {
      issues.push({
        type: 'structure',
        severity: 'medium',
        message: 'Question content is very long and may be difficult to read',
        field: 'content',
        suggestion: 'Consider breaking into smaller parts',
      });
    }

    // Check for proper question format
    if (question.content && !question.content.trim().endsWith('?') && 
        !question.content.includes('Choose') && !question.content.includes('Select')) {
      issues.push({
        type: 'structure',
        severity: 'low',
        message: 'Question should end with a question mark or be a clear instruction',
        field: 'content',
      });
    }

    return issues;
  }

  /**
   * Validate question content
   */
  private validateContent(question: QuestionForValidation): ValidationIssue[] {
    const issues: ValidationIssue[] = [];

    // Basic content checks
    if (question.content) {
      const content = question.content.toLowerCase();
      
      // Check for placeholder text
      if (content.includes('lorem ipsum') || content.includes('placeholder')) {
        issues.push({
          type: 'content',
          severity: 'high',
          message: 'Question contains placeholder text',
          field: 'content',
        });
      }

      // Check for very short content
      if (question.content.trim().length < 10) {
        issues.push({
          type: 'content',
          severity: 'medium',
          message: 'Question content is very short',
          field: 'content',
        });
      }
    }

    return issues;
  }

  /**
   * Validate age appropriateness
   */
  private validateAppropriateness(question: QuestionForValidation): ValidationIssue[] {
    const issues: ValidationIssue[] = [];

    if (question.content) {
      const content = question.content.toLowerCase();
      
      // Basic inappropriate content check
      const inappropriateWords = ['violence', 'weapon', 'drug', 'alcohol'];
      for (const word of inappropriateWords) {
        if (content.includes(word)) {
          issues.push({
            type: 'appropriateness',
            severity: 'high',
            message: `Content may contain inappropriate material: ${word}`,
            field: 'content',
          });
        }
      }
    }

    return issues;
  }

  /**
   * Validate cultural sensitivity
   */
  private validateCulturalSensitivity(question: QuestionForValidation): ValidationIssue[] {
    const issues: ValidationIssue[] = [];

    // Basic cultural sensitivity checks
    if (question.content) {
      const content = question.content.toLowerCase();
      
      // Check for potentially sensitive terms
      const sensitiveTerms = ['race', 'religion', 'ethnicity'];
      for (const term of sensitiveTerms) {
        if (content.includes(term)) {
          issues.push({
            type: 'cultural_sensitivity',
            severity: 'medium',
            message: `Content mentions potentially sensitive topic: ${term}`,
            field: 'content',
            suggestion: 'Review for cultural sensitivity',
          });
        }
      }
    }

    return issues;
  }

  /**
   * Emit monitoring event for validation analytics
   */
  private async emitValidationEvent(
    question: QuestionForValidation,
    result: ValidationResult,
    executionTimeMs: number
  ): Promise<void> {
    try {
      const event: ValidationAttemptEvent = {
        eventId: uuidv4(),
        type: 'validation_attempt',
        timestamp: new Date(),
        validationParams: {
          questionId: question.id || '',
          questionType: question.type || '',
          validationType: 'content', // This could be made more specific based on validation config
        },
        result: {
          isValid: result.isValid,
          score: result.score,
          issues: result.issues.map(issue => ({
            type: issue.type,
            severity: issue.severity as 'low' | 'medium' | 'high' | 'critical',
            message: issue.message,
          })),
          executionTimeMs,
        },
      };

      await this.poolMonitoringService.emitEvent(event);
    } catch (error) {
      // Don't throw error to avoid disrupting main flow
      this.logger.error(`Error emitting validation event: ${error.message}`, error.stack);
    }
  }
}
