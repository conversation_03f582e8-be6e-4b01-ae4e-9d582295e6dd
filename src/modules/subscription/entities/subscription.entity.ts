import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  user_id: string; // <PERSON>ên kết với bảng users

  @Column({ unique: true })
  stripe_subscription_id: string;

  @Column()
  stripe_customer_id: string;

  @Column()
  stripe_price_id: string;

  @Column()
  stripe_product_id: string;

  @Column({ type: 'timestamp' })
  start_date: Date;

  @Column({ type: 'timestamp' })
  current_period_start: Date;

  @Column({ type: 'timestamp' , nullable: true })
  current_period_end: Date;

  @Column({ type: 'timestamp', nullable: true })
  cancel_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  canceled_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  ended_at: Date;

  @Column({ default: false })
  cancel_at_period_end: boolean;

  @Column()
  status: string; // active, canceled, incomplete, past_due...

  @Column({ type: 'int' })
  price_unit_amount: number;

  @Column()
  currency: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @Column({ nullable: true })
  latest_invoice_id: string;

  @Column({ nullable: true })
  payment_method: string;

  @Column({ type: 'jsonb', nullable: true })
  raw_data: any;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
