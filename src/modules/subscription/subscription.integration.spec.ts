import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionModule } from './subscription.module';
import { Subscription } from './entities/subscription.entity';
import { Package } from '../packages/entities/package.entity';
import { SubscriptionService } from './subscription.service';
import { StripeService } from '../stripe/stripe.service';
import { AuthGuard } from '../auth/guards/auth.guard';

describe('SubscriptionModule Integration', () => {
  let app: INestApplication;
  let subscriptionService: SubscriptionService;

  const mockStripeService = {
    getStripeInstance: jest.fn(() => ({
      invoices: {
        list: jest.fn().mockResolvedValue({
          data: [
            {
              id: 'in_test',
              amount_paid: 2000,
              currency: 'usd',
              status: 'paid',
              created: 1640995200,
            },
          ],
        }),
      },
    })),
  };

  const mockUser = {
    sub: 'user-123',
    email: '<EMAIL>',
    role: 'TEACHER',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Subscription, Package],
          synchronize: true,
        }),
        SubscriptionModule,
      ],
    })
      .overrideProvider(StripeService)
      .useValue(mockStripeService)
      .overrideGuard(AuthGuard)
      .useValue({
        canActivate: (context) => {
          const request = context.switchToHttp().getRequest();
          request.user = mockUser;
          return true;
        },
      })
      .compile();

    app = module.createNestApplication();
    await app.init();

    subscriptionService = module.get<SubscriptionService>(SubscriptionService);
  });

  afterEach(async () => {
    await app.close();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
    expect(subscriptionService).toBeDefined();
  });

  describe('SubscriptionController Integration', () => {
    it('should have proper module configuration', () => {
      // This test verifies that the module is properly configured with all dependencies
      const controller = app.get('SubscriptionController');
      expect(controller).toBeDefined();
    });

    it('should integrate SubscriptionService with SubscriptionController', () => {
      // This test verifies that the service is properly injected into the controller
      const service = app.get(SubscriptionService);
      expect(service).toBeDefined();
      expect(service.findByUserId).toBeDefined();
      expect(service.getUserInvoices).toBeDefined();
    });
  });

  describe('Dependencies Integration', () => {
    it('should have TypeORM repositories available', () => {
      const subscriptionRepo = app.get('SubscriptionRepository');
      const packageRepo = app.get('PackageRepository');
      expect(subscriptionRepo).toBeDefined();
      expect(packageRepo).toBeDefined();
    });

    it('should have StripeService integrated', () => {
      const stripeService = app.get(StripeService);
      expect(stripeService).toBeDefined();
      expect(stripeService.getStripeInstance).toBeDefined();
    });
  });
}); 