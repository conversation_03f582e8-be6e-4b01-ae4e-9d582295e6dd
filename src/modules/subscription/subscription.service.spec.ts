import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { Subscription } from './entities/subscription.entity';
import { Package } from '../packages/entities/package.entity';
import { StripeService } from '../stripe/stripe.service';

describe('SubscriptionService', () => {
  let service: SubscriptionService;
  let subscriptionRepo: jest.Mocked<Repository<Subscription>>;
  let packageRepo: jest.Mocked<Repository<Package>>;
  let stripeService: jest.Mocked<StripeService>;

  const mockSubscription = {
    id: 'sub-uuid',
    user_id: 'user-123',
    stripe_subscription_id: 'sub_stripe_123',
    stripe_customer_id: 'cus_stripe_123',
    stripe_product_id: 'prod_stripe_123',
    status: 'active',
  };

  const mockPackage = {
    id: 'pkg-uuid',
    name: 'Premium Package',
    description: 'Premium subscription package',
    stripeProductId: 'prod_stripe_123',
    prices: [],
  };

  const mockStripeInvoices = {
    data: [
      {
        id: 'in_stripe_123',
        amount_paid: 2000,
        currency: 'usd',
        status: 'paid',
        created: **********,
      },
    ],
  };

  beforeEach(async () => {
    const mockSubscriptionRepo = {
      findOne: jest.fn(),
      save: jest.fn(),
      create: jest.fn(),
    };

    const mockPackageRepo = {
      findOne: jest.fn(),
    };

    const mockStripeService = {
      getStripeInstance: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionService,
        {
          provide: getRepositoryToken(Subscription),
          useValue: mockSubscriptionRepo,
        },
        {
          provide: getRepositoryToken(Package),
          useValue: mockPackageRepo,
        },
        {
          provide: StripeService,
          useValue: mockStripeService,
        },
      ],
    }).compile();

    service = module.get<SubscriptionService>(SubscriptionService);
    subscriptionRepo = module.get(getRepositoryToken(Subscription));
    packageRepo = module.get(getRepositoryToken(Package));
    stripeService = module.get(StripeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findByUserId', () => {
    it('should return subscription with package details when found', async () => {
      subscriptionRepo.findOne.mockResolvedValue(mockSubscription as any);
      packageRepo.findOne.mockResolvedValue(mockPackage as any);

      const result = await service.findByUserId('user-123');

      expect(subscriptionRepo.findOne).toHaveBeenCalledWith({
        where: { user_id: 'user-123' },
      });
      expect(packageRepo.findOne).toHaveBeenCalledWith({
        where: { stripeProductId: 'prod_stripe_123' },
        relations: ['prices'],
      });
      expect(result).toEqual({
        ...mockSubscription,
        package: mockPackage,
      });
    });

    it('should throw NotFoundException when subscription not found', async () => {
      subscriptionRepo.findOne.mockResolvedValue(null);

      await expect(service.findByUserId('user-123')).rejects.toThrow(
        new NotFoundException('No subscription found for user ID: user-123'),
      );

      expect(subscriptionRepo.findOne).toHaveBeenCalledWith({
        where: { user_id: 'user-123' },
      });
      expect(packageRepo.findOne).not.toHaveBeenCalled();
    });

    it('should handle database errors and throw InternalServerErrorException', async () => {
      subscriptionRepo.findOne.mockRejectedValue(new Error('Database error'));

      await expect(service.findByUserId('user-123')).rejects.toThrow(
        InternalServerErrorException,
      );
    });

    it('should return subscription with null package when package not found', async () => {
      subscriptionRepo.findOne.mockResolvedValue(mockSubscription as any);
      packageRepo.findOne.mockResolvedValue(null);

      const result = await service.findByUserId('user-123');

      expect(result).toEqual({
        ...mockSubscription,
        package: null,
      });
    });
  });

  describe('getUserInvoices', () => {
    it('should return user invoices when subscription and customer ID exist', async () => {
      const mockStripe = {
        invoices: {
          list: jest.fn().mockResolvedValue(mockStripeInvoices),
        },
      };

      subscriptionRepo.findOne.mockResolvedValue(mockSubscription as any);
      stripeService.getStripeInstance.mockReturnValue(mockStripe as any);

      const result = await service.getUserInvoices('user-123');

      expect(subscriptionRepo.findOne).toHaveBeenCalledWith({
        where: { user_id: 'user-123' },
      });
      expect(stripeService.getStripeInstance).toHaveBeenCalled();
      expect(mockStripe.invoices.list).toHaveBeenCalledWith({
        customer: 'cus_stripe_123',
        limit: 100,
      });
      expect(result).toEqual(mockStripeInvoices.data);
    });

    it('should throw NotFoundException when subscription not found', async () => {
      subscriptionRepo.findOne.mockResolvedValue(null);

      await expect(service.getUserInvoices('user-123')).rejects.toThrow(
        new NotFoundException('No subscription found for user ID: user-123'),
      );

      expect(stripeService.getStripeInstance).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException when stripe_customer_id is missing', async () => {
      const subscriptionWithoutCustomer = {
        ...mockSubscription,
        stripe_customer_id: null,
      };
      subscriptionRepo.findOne.mockResolvedValue(subscriptionWithoutCustomer as any);

      await expect(service.getUserInvoices('user-123')).rejects.toThrow(
        new BadRequestException('No Stripe customer ID found for user subscription.'),
      );

      expect(stripeService.getStripeInstance).not.toHaveBeenCalled();
    });

    it('should handle Stripe API errors and throw InternalServerErrorException', async () => {
      const mockStripe = {
        invoices: {
          list: jest.fn().mockRejectedValue(new Error('Stripe API error')),
        },
      };

      subscriptionRepo.findOne.mockResolvedValue(mockSubscription as any);
      stripeService.getStripeInstance.mockReturnValue(mockStripe as any);

      await expect(service.getUserInvoices('user-123')).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });
});
