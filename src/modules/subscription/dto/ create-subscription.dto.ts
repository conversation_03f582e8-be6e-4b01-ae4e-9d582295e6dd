// src/subscription/dto/create-subscription.dto.ts
import { IsString, IsBoolean, IsOptional, IsInt, IsJSON, IsDateString } from 'class-validator';

export class CreateSubscriptionDto {
  @IsString()
  user_id: string;

  @IsString()
  stripe_subscription_id: string;

  @IsString()
  stripe_customer_id: string;

  @IsString()
  stripe_price_id: string;

  @IsString()
  stripe_product_id: string;

  @IsDateString()
  start_date: string;

  @IsOptional()
  @IsDateString()
  current_period_start: string;

  // @IsOptional()
  // @IsDateString()
  // current_period_end: string;

  @IsOptional()
  @IsDateString()
  cancel_at?: string;

  @IsOptional()
  @IsDateString()
  canceled_at?: string;

  @IsOptional()
  @IsDateString()
  ended_at?: string;

  @IsBoolean()
  cancel_at_period_end: boolean;

  @IsString()
  status: string;

  @IsInt()
  price_unit_amount: number;

  @IsString()
  currency: string;

  @IsOptional()
  @IsJSON()
  metadata?: Record<string, any>;

  @IsOptional()
  @IsString()
  latest_invoice_id?: string;

  @IsOptional()
  @IsString()
  payment_method?: string;

  @IsOptional()
  @IsJSON()
  raw_data?: any;
}
