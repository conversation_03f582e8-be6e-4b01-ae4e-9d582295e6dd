import { Injectable, Logger } from '@nestjs/common';
import { AiService } from '../../ai/ai.service';
import { ModelConfigService } from '../../ai/model-config.service';
import OpenAI from 'openai';

export interface ExtractionResult {
  success: boolean;
  narrativeStructure?: string;
  confidence?: number;
  processingTime?: number;
  error?: string;
}

@Injectable()
export class NarrativeStructureExtractorService {
  private readonly logger = new Logger(NarrativeStructureExtractorService.name);

  constructor(
    private readonly aiService: AiService,
    private readonly modelConfigService: ModelConfigService,
  ) {}

  /**
   * Extract a core narrative structure from multiple examination formats
   * @param examinationFormats Array of examination format texts
   * @param schoolId School identifier for logging purposes
   * @returns Extraction result with narrative structure or error
   */
  async extractNarrativeStructure(
    examinationFormats: string[],
    schoolId: string,
  ): Promise<ExtractionResult> {
    const startTime = Date.now();
    
    try {
      this.logger.log(`Starting narrative structure extraction for school ${schoolId} with ${examinationFormats.length} formats`);

      if (!examinationFormats || examinationFormats.length === 0) {
        return {
          success: false,
          error: 'No examination formats provided for analysis',
        };
      }

      // Filter out empty or very short formats
      const validFormats = examinationFormats.filter(format => 
        format && format.trim().length > 50
      );

      if (validFormats.length === 0) {
        return {
          success: false,
          error: 'No valid examination formats found (all formats too short or empty)',
        };
      }

      this.logger.log(`Processing ${validFormats.length} valid examination formats`);

      const systemPrompt = this.buildSystemPrompt();
      const userPrompt = this.buildUserPrompt(validFormats, schoolId);

      const messages: OpenAI.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: systemPrompt,
        },
        {
          role: 'user',
          content: userPrompt,
        },
      ];

      const model = this.modelConfigService.getNarrativeStructureModel();

      this.logger.log(`Making AI request with model: ${model}`);
      const response = await this.aiService.chat(model, messages);

      if (!response || !response.choices || response.choices.length === 0) {
        throw new Error('Empty or invalid response from AI service');
      }

      const content = response.choices[0].message.content;
      if (!content) {
        throw new Error('Empty content in AI response');
      }

      // Parse the JSON response
      let parsedResponse;
      try {
        parsedResponse = JSON.parse(content);
      } catch (parseError) {
        this.logger.error(`Failed to parse AI response as JSON: ${parseError.message}`);
        throw new Error(`Invalid JSON response from AI: ${parseError.message}`);
      }

      // Validate the response structure
      if (!parsedResponse.narrativeStructure || typeof parsedResponse.narrativeStructure !== 'string') {
        throw new Error('AI response missing required narrativeStructure field');
      }

      const narrativeStructure = parsedResponse.narrativeStructure.trim();

      // Validate format (should follow the specific structure)
      const expectedFormat = /^.+\s*→\s*.+\s*→\s*.+\s*→\s*.+$/;
      if (!expectedFormat.test(narrativeStructure)) {
        this.logger.warn(`Generated narrative structure does not follow the expected format: "Professional/Technical Context → Precise Initial Conditions → System Changes → Mathematical Precision Requirements"`);
        // Still proceed but log the warning
      }

      // Validate that it contains the arrow separators
      const arrowCount = (narrativeStructure.match(/→/g) || []).length;
      if (arrowCount !== 3) {
        this.logger.warn(`Generated narrative structure should contain exactly 3 arrows (→), found ${arrowCount}`);
      }

      const processingTime = Date.now() - startTime;
      const confidence = 0.9; // Default confidence since we're not expecting it from AI

      this.logger.log(`Successfully extracted narrative structure for school ${schoolId} in ${processingTime}ms`);

      return {
        success: true,
        narrativeStructure,
        confidence,
        processingTime,
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Failed to extract narrative structure for school ${schoolId}: ${error.message}`, error.stack);
      
      return {
        success: false,
        error: error.message,
        processingTime,
      };
    }
  }

  /**
   * Build the system prompt for narrative structure extraction
   */
  private buildSystemPrompt(): string {
    return `
# ROLE AND EXPERTISE
You are an expert educational assessment analyst. Your task is to analyze ONLY the questions (not answers) within examination formats and extract how this school structures their questions.

# OBJECTIVE
Analyze the questions and output ONE simple structured format that captures this school's question-writing pattern.

# ANALYSIS FOCUS - QUESTIONS ONLY
- ONLY analyze the questions, ignore all answers, solutions, marking schemes, headers, or instructions
- Focus on how questions are written and structured
- Look for patterns in question phrasing and presentation

# OUTPUT REQUIREMENTS
Provide a JSON response with ONLY this structure:
{
  "narrativeStructure": "Professional/Technical Context → Precise Initial Conditions → System Changes → Mathematical Precision Requirements"
}

# NARRATIVE STRUCTURE FORMAT
The narrativeStructure field must be ONE line following this EXACT format:
"Professional/Technical Context → Precise Initial Conditions → System Changes → Mathematical Precision Requirements"

Where each part describes how THIS SCHOOL writes questions:
- Professional/Technical Context: How this school frames questions in real-world contexts
- Precise Initial Conditions: How this school presents given information
- System Changes: How this school describes what happens or needs analysis
- Mathematical Precision Requirements: How this school specifies answer precision/format
`.trim();
  }

  /**
   * Build the user prompt with examination formats
   */
  private buildUserPrompt(examinationFormats: string[], schoolId: string): string {
    const formatsText = examinationFormats
      .map((format, index) => `## Examination Format ${index + 1}\n${format}`)
      .join('\n\n');

    return `
# QUESTION ANALYSIS REQUEST

Analyze ONLY the questions (not answers) in these ${examinationFormats.length} examination format(s) from School ID: ${schoolId}.

${formatsText}

# TASK
Look at how this school writes questions and output ONE line in this format:
"Professional/Technical Context → Precise Initial Conditions → System Changes → Mathematical Precision Requirements"

# FOCUS
- ONLY analyze questions, ignore answers, solutions, headers, instructions
- How does this school frame questions?
- How does this school present given information?
- How does this school describe what needs to be analyzed?
- How does this school specify answer requirements?

# OUTPUT
Return JSON with only:
{
  "narrativeStructure": "Professional/Technical Context → Precise Initial Conditions → System Changes → Mathematical Precision Requirements"
}
`.trim();
  }
}
