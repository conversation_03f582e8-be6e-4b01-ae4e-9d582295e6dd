import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NarrativeStructure } from '../entities/narrative-structure.entity';
import { CreateNarrativeStructureDto, UpdateNarrativeStructureDto } from '../dto/narrative-structure.dto';
import { DocumentsService } from '../../documents/documents.service';
import { NarrativeStructureExtractorService, ExtractionResult } from './narrative-structure-extractor.service';
import { SchoolService } from '../school.service';

@Injectable()
export class NarrativeStructureService {
  private readonly logger = new Logger(NarrativeStructureService.name);

  constructor(
    @InjectRepository(NarrativeStructure)
    private narrativeStructureRepository: Repository<NarrativeStructure>,
    private documentsService: DocumentsService,
    private narrativeExtractorService: NarrativeStructureExtractorService,
    private schoolService: SchoolService,
  ) {}

  /**
   * Create a new narrative structure
   */
  async create(createDto: CreateNarrativeStructureDto): Promise<NarrativeStructure> {
    try {
      // Validate that the school exists
      await this.schoolService.findOne(createDto.schoolId);

      // Delete any existing narrative structure for this school
      await this.deleteBySchoolId(createDto.schoolId);

      const narrativeStructure = this.narrativeStructureRepository.create({
        ...createDto,
        extractedAt: new Date(),
      });

      const saved = await this.narrativeStructureRepository.save(narrativeStructure);

      // Also store in vector database for search capabilities
      await this.documentsService.storeNarrativeStructure(
        createDto.schoolId,
        createDto.content,
        createDto.extractionMetadata
      );

      this.logger.log(`Created narrative structure for school ${createDto.schoolId}`);
      return saved;
    } catch (error) {
      this.logger.error(`Failed to create narrative structure: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find narrative structure by school ID
   */
  async findBySchoolId(schoolId: string): Promise<NarrativeStructure | null> {
    try {
      const narrativeStructure = await this.narrativeStructureRepository.findOne({
        where: { schoolId },
        relations: ['school'],
      });

      return narrativeStructure;
    } catch (error) {
      this.logger.error(`Failed to find narrative structure for school ${schoolId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Update narrative structure
   */
  async update(id: string, updateDto: UpdateNarrativeStructureDto): Promise<NarrativeStructure> {
    try {
      const existing = await this.narrativeStructureRepository.findOne({
        where: { id },
      });

      if (!existing) {
        throw new NotFoundException(`Narrative structure with ID ${id} not found`);
      }

      // Update the database record
      const updated = await this.narrativeStructureRepository.save({
        ...existing,
        ...updateDto,
        version: existing.version + 1,
        extractedAt: new Date(),
      });

      // Update in vector database if content changed
      if (updateDto.content) {
        await this.documentsService.storeNarrativeStructure(
          existing.schoolId,
          updateDto.content,
          updateDto.extractionMetadata
        );
      }

      this.logger.log(`Updated narrative structure ${id} for school ${existing.schoolId}`);
      return updated;
    } catch (error) {
      this.logger.error(`Failed to update narrative structure ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete narrative structure by school ID
   */
  async deleteBySchoolId(schoolId: string): Promise<boolean> {
    try {
      const existing = await this.findBySchoolId(schoolId);
      if (!existing) {
        return true; // Already deleted or doesn't exist
      }

      // Delete from database
      await this.narrativeStructureRepository.delete({ schoolId });

      // Delete from vector database
      await this.documentsService.deleteNarrativeStructure(schoolId);

      this.logger.log(`Deleted narrative structure for school ${schoolId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete narrative structure for school ${schoolId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Extract narrative structure for a specific school
   */
  async extractForSchool(schoolId: string): Promise<{
    success: boolean;
    error?: string;
    narrativeStructure?: NarrativeStructure;
    confidence?: number;
    processingTime?: number;
  }> {
    try {
      this.logger.log(`Starting narrative structure extraction for school ${schoolId}`);

      // Validate that the school exists
      await this.schoolService.findOne(schoolId);

      // Get all examination formats for the school
      const examinationFormats = await this.documentsService.getAllExaminationFormats(schoolId);

      if (examinationFormats.length === 0) {
        return {
          success: false,
          error: 'No examination formats found for this school',
        };
      }

      // Extract narrative structure using AI
      const extractionResult = await this.narrativeExtractorService.extractNarrativeStructure(
        examinationFormats,
        schoolId
      );

      if (!extractionResult.success || !extractionResult.narrativeStructure) {
        return {
          success: false,
          error: extractionResult.error || 'Failed to extract narrative structure',
        };
      }

      // Save the extracted narrative structure
      const createDto: CreateNarrativeStructureDto = {
        schoolId,
        content: extractionResult.narrativeStructure,
        sourceFormatsCount: examinationFormats.length,
        extractionMetadata: {
          confidence: extractionResult.confidence,
          processingTime: extractionResult.processingTime,
          extractedAt: new Date().toISOString(),
          sourceFormatsCount: examinationFormats.length,
        },
      };

      const narrativeStructure = await this.create(createDto);

      this.logger.log(`Successfully extracted and saved narrative structure for school ${schoolId}`);

      return {
        success: true,
        narrativeStructure,
        confidence: extractionResult.confidence,
        processingTime: extractionResult.processingTime,
      };
    } catch (error) {
      this.logger.error(`Failed to extract narrative structure for school ${schoolId}: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Extract narrative structures for all schools that have examination formats
   */
  async extractForAllSchools(): Promise<{
    totalSchools: number;
    successfulExtractions: number;
    failedExtractions: number;
    results: Array<{
      schoolId: string;
      success: boolean;
      message: string;
      narrativeStructureId?: string;
    }>;
  }> {
    try {
      this.logger.log('Starting bulk narrative structure extraction for all schools');

      // Get all schools
      const schools = await this.schoolService.findAll();
      const results: Array<{
        schoolId: string;
        success: boolean;
        message: string;
        narrativeStructureId?: string;
      }> = [];
      let successfulExtractions = 0;
      let failedExtractions = 0;

      for (const school of schools) {
        try {
          this.logger.log(`Processing school ${school.id} (${school.name})`);

          const extractionResult = await this.extractForSchool(school.id);

          if (extractionResult.success && extractionResult.narrativeStructure) {
            results.push({
              schoolId: school.id,
              success: true,
              message: 'Narrative structure extracted successfully',
              narrativeStructureId: extractionResult.narrativeStructure.id,
            });
            successfulExtractions++;
          } else {
            results.push({
              schoolId: school.id,
              success: false,
              message: extractionResult.error || 'Unknown error occurred',
            });
            failedExtractions++;
          }
        } catch (error) {
          this.logger.error(`Failed to process school ${school.id}: ${error.message}`);
          results.push({
            schoolId: school.id,
            success: false,
            message: error.message,
          });
          failedExtractions++;
        }

        // Add a small delay to avoid overwhelming the AI service
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      this.logger.log(`Bulk extraction completed: ${successfulExtractions} successful, ${failedExtractions} failed`);

      return {
        totalSchools: schools.length,
        successfulExtractions,
        failedExtractions,
        results,
      };
    } catch (error) {
      this.logger.error(`Failed to perform bulk narrative structure extraction: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all narrative structures
   */
  async findAll(): Promise<NarrativeStructure[]> {
    return this.narrativeStructureRepository.find({
      relations: ['school'],
      order: { extractedAt: 'DESC' },
    });
  }
}
