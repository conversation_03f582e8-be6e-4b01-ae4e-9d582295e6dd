import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import BaseEntity from 'src/core/entities/base-entity';
import { ApiProperty } from '@nestjs/swagger';
import { School } from './school.entity';

@Entity('narrative_structures')
export class NarrativeStructure extends BaseEntity {
  @ApiProperty({
    description: 'The school this narrative structure belongs to',
    type: () => School,
  })
  @ManyToOne(() => School, { nullable: false })
  @JoinColumn({ name: 'schoolId' })
  school: School;

  @Column()
  schoolId: string;

  @ApiProperty({
    description: 'The extracted narrative structure content (500-1000 words)',
    example: 'This school follows a structured examination format with emphasis on...',
  })
  @Column('text')
  content: string;

  @ApiProperty({
    description: 'Number of examination formats analyzed to create this structure',
    example: 3,
  })
  @Column({ default: 0 })
  sourceFormatsCount: number;

  @ApiProperty({
    description: 'Timestamp when the narrative structure was last extracted',
  })
  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  extractedAt: Date;

  @ApiProperty({
    description: 'Version number for tracking narrative structure updates',
    example: 1,
  })
  @Column({ default: 1 })
  version: number;

  @ApiProperty({
    description: 'Metadata about the extraction process',
    example: { aiModel: 'gpt-4', processingTime: 1500, confidence: 0.95 },
  })
  @Column('jsonb', { nullable: true })
  extractionMetadata: Record<string, any>;
}
