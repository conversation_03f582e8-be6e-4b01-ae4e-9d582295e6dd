import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { School } from './entities/school.entity';
import { CreateSchoolDto } from './dto/create-school.dto';
import { UpdateSchoolDto } from './dto/update-school.dto';
import { BrandService } from '../brand/brand.service';
import { UserService } from '../user/user.service';
import { EUserRole } from '../user/dto/create-user.dto';
import { User } from '../user/entities/user.entity';

@Injectable()
export class SchoolService {
  constructor(
    @InjectRepository(School)
    private schoolRepository: Repository<School>,
    private brandService: BrandService,
    private userService: UserService,
  ) {}

  async create(createSchoolDto: CreateSchoolDto, userId?: string, userRole?: EUserRole): Promise<School> {
    const { adminId, brandId, ...schoolData } = createSchoolDto;

    // Handle INDEPENDENT_TEACHER school creation
    if (userRole === EUserRole.INDEPENDENT_TEACHER && userId) {
      // Check if the INDEPENDENT_TEACHER already owns a school
      const existingSchool = await this.schoolRepository.findOne({
        where: { adminId: userId }
      });

      if (existingSchool) {
        throw new ForbiddenException('Independent teachers can only create and manage one school.');
      }

      // Validate brand if provided
      if (brandId) {
        await this.brandService.findOne(brandId);
      }

      // For INDEPENDENT_TEACHER, set adminId to the creating user's ID
      const school = this.schoolRepository.create({
        ...schoolData,
        adminId: userId,
        brandId,
      });

      const createdSchool = await this.schoolRepository.save(school);

      // Update the user's schoolId to link them to their created school
      await this.userService.updateSchoolId(userId, createdSchool.id);

      return createdSchool;
    }

    // Original validation logic for other roles
    if (adminId) {
      const admin = await this.userService.findOne(adminId);
      if (!admin){
        throw new NotFoundException(`Admin user with ID ${adminId} not found`);
      }

      if (admin.role !== EUserRole.SCHOOL_MANAGER && admin.role !== EUserRole.INDEPENDENT_TEACHER) {
        throw new BadRequestException('Admin user must have SCHOOL_MANAGER or INDEPENDENT_TEACHER role');
      }
    }

    // Validate brand if provided
    if (brandId) {
      await this.brandService.findOne(brandId);
    }

    const school = this.schoolRepository.create({
      ...schoolData,
      adminId,
      brandId,
    });

    return this.schoolRepository.save(school);
  }

  async findAll(): Promise<School[]> {
    return this.schoolRepository.find({
      relations: ['admin', 'brand'],
    });
  }

  async findOne(id: string): Promise<School> {
    const school = await this.schoolRepository.findOne({
      where: { id },
      relations: ['admin', 'brand'],
    });
    
    if (!school) {
      throw new NotFoundException(`School with ID ${id} not found`);
    }
    
    return school;
  }

  async update(id: string, updateSchoolDto: UpdateSchoolDto): Promise<School> {
    const { adminId, brandId, ...schoolData } = updateSchoolDto;

    // Validate admin user if provided
    if (adminId) {
      const admin = await this.userService.findOne(adminId);
      if (!admin){
        throw new NotFoundException(`Admin user with ID ${adminId} not found`);
      }
      if (admin.role !== EUserRole.SCHOOL_MANAGER) {
        throw new BadRequestException('Admin user must have SCHOOL_MANAGER role');
      }
    }

    // Validate brand if provided
    if (brandId) {
      await this.brandService.findOne(brandId);
    }

    const school = await this.findOne(id);
    this.schoolRepository.merge(school, {
      ...schoolData,
      adminId,
      brandId,
    });
    
    return this.schoolRepository.save(school);
  }

  async updateOwnSchool(userId: string, updateSchoolDto: UpdateSchoolDto): Promise<School> {
    // Find the school administered by this user
    const school = await this.schoolRepository.findOne({
      where: { adminId: userId },
      relations: ['admin', 'brand']
    });

    if (!school) {
      throw new NotFoundException('School not found or you are not the administrator of any school.');
    }

    // Prevent adminId modification
    if ('adminId' in updateSchoolDto) {
      throw new BadRequestException('Cannot change school administrator via this route.');
    }

    const { brandId, ...schoolData } = updateSchoolDto;

    // Validate brand if provided
    if (brandId) {
      await this.brandService.findOne(brandId);
    }

    // Apply updates
    this.schoolRepository.merge(school, {
      ...schoolData,
      brandId,
    });

    return this.schoolRepository.save(school);
  }

  async remove(id: string, requestingUser?: { role: EUserRole }): Promise<void> {
    // Prevent INDEPENDENT_TEACHER from deleting schools
    if (requestingUser?.role === EUserRole.INDEPENDENT_TEACHER) {
      throw new ForbiddenException('Independent teachers cannot delete schools.');
    }

    const school = await this.findOne(id);
    await this.schoolRepository.remove(school);
  }
}