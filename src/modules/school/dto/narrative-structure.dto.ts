import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsN<PERSON>ber, IsOptional, IsObject, IsDate, Min } from 'class-validator';

export class CreateNarrativeStructureDto {
  @ApiProperty({
    description: 'The school ID this narrative structure belongs to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  schoolId: string;

  @ApiProperty({
    description: 'The extracted narrative structure content',
    example: 'This school follows a structured examination format with emphasis on...',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Number of examination formats analyzed',
    example: 3,
  })
  @IsNumber()
  @Min(0)
  sourceFormatsCount: number;

  @ApiProperty({
    description: 'Metadata about the extraction process',
    example: { aiModel: 'gpt-4', processingTime: 1500, confidence: 0.95 },
    required: false,
  })
  @IsOptional()
  @IsObject()
  extractionMetadata?: Record<string, any>;
}

export class UpdateNarrativeStructureDto {
  @ApiProperty({
    description: 'The updated narrative structure content',
    example: 'This school follows a structured examination format with emphasis on...',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: 'Updated number of examination formats analyzed',
    example: 4,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sourceFormatsCount?: number;

  @ApiProperty({
    description: 'Updated metadata about the extraction process',
    example: { aiModel: 'gpt-4', processingTime: 1200, confidence: 0.98 },
    required: false,
  })
  @IsOptional()
  @IsObject()
  extractionMetadata?: Record<string, any>;
}

export class NarrativeStructureResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the narrative structure',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The school ID this narrative structure belongs to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  schoolId: string;

  @ApiProperty({
    description: 'The extracted narrative structure content',
    example: 'This school follows a structured examination format with emphasis on...',
  })
  content: string;

  @ApiProperty({
    description: 'Number of examination formats analyzed',
    example: 3,
  })
  sourceFormatsCount: number;

  @ApiProperty({
    description: 'Timestamp when the narrative structure was extracted',
  })
  extractedAt: Date;

  @ApiProperty({
    description: 'Version number of the narrative structure',
    example: 1,
  })
  version: number;

  @ApiProperty({
    description: 'Metadata about the extraction process',
    example: { aiModel: 'gpt-4', processingTime: 1500, confidence: 0.95 },
    required: false,
  })
  extractionMetadata?: Record<string, any>;

  @ApiProperty({
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}

export class ExtractNarrativeStructureResponseDto {
  @ApiProperty({
    description: 'Success status of the extraction',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Status message',
    example: 'Narrative structure extracted successfully',
  })
  message: string;

  @ApiProperty({
    description: 'The extracted narrative structure',
    type: NarrativeStructureResponseDto,
    required: false,
  })
  narrativeStructure?: NarrativeStructureResponseDto;

  @ApiProperty({
    description: 'Number of examination formats processed',
    example: 3,
    required: false,
  })
  processedFormatsCount?: number;
}

export class BulkExtractNarrativeStructureResponseDto {
  @ApiProperty({
    description: 'Overall success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Overall status message',
    example: 'Bulk narrative structure extraction completed',
  })
  message: string;

  @ApiProperty({
    description: 'Total number of schools processed',
    example: 10,
  })
  totalSchools: number;

  @ApiProperty({
    description: 'Number of schools successfully processed',
    example: 8,
  })
  successfulExtractions: number;

  @ApiProperty({
    description: 'Number of schools that failed processing',
    example: 2,
  })
  failedExtractions: number;

  @ApiProperty({
    description: 'Details of the extraction results per school',
    type: [Object],
    example: [
      { schoolId: '123', success: true, message: 'Success' },
      { schoolId: '456', success: false, message: 'No examination formats found' }
    ],
  })
  results: Array<{
    schoolId: string;
    success: boolean;
    message: string;
    narrativeStructureId?: string;
  }>;
}
