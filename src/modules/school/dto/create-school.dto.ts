import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateSchoolDto {
  @ApiProperty({
    description: 'The official name of the school',
    example: 'Springfield Elementary School',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'The physical location of the school',
    example: '123 Education St, Springfield',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: 'Contact number for the school',
    example: '******-123-4567',
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Official registration or license number',
    example: 'REG12345678',
  })
  @IsOptional()
  @IsString()
  registeredNumber?: string;

  @ApiProperty({
    description: 'Official email address for communications',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'ID of the school administrator (School Manager user)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  adminId?: string;

  @ApiPropertyOptional({
    description: 'ID of the associated brand',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  brandId?: string;
}