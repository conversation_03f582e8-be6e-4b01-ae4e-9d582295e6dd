# Multimodal PDF Processing Module

This module provides multimodal processing capabilities for PDF documents, allowing the system to extract and analyze both text and images from PDFs.

## Features

- Extract text content from PDF documents
- Convert PDF pages to images
- Process images with the Qwen 2.5 VL multimodal AI model
- Enhance document understanding with image analysis

## Requirements

### System Dependencies

- **poppler-utils**: Required for PDF to image conversion
  - Ubuntu/Debian: `sudo apt-get install poppler-utils`
  - macOS: `brew install poppler`

### Environment Variables

Add these to your `.env` file:

```
# Multimodal Processing
USE_MULTIMODAL_PROCESSING=true
IMAGE_STORAGE_PATH=./uploads/images
OPENROUTER_API_KEY=your_openrouter_api_key
```

## Usage

The multimodal processing is integrated with the document processing pipeline. When enabled, PDFs will be processed to extract both text and images, and the Qwen 2.5 VL model will analyze the images to provide enhanced understanding.

### API

- `processPdfWithMultimodalAI(fileBuffer: Buffer, documentId: string)`: Process a PDF file with multimodal AI
- `processWithQwen(text: string, imageUrl: string | null)`: Process text and image with the Qwen 2.5 VL model

## Troubleshooting

If you encounter issues with image extraction:

1. Ensure poppler-utils is installed on your system
2. Check that the IMAGE_STORAGE_PATH directory exists and is writable
3. Verify your OPENROUTER_API_KEY is valid
4. Check the logs for specific error messages

## Implementation Details

The module uses:
- pdf.js for text extraction
- pdftoppm (from poppler-utils) for image extraction
- Qwen 2.5 VL model via OpenRouter API for multimodal analysis
