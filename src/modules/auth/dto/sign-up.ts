import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>num, IsUUID, ValidateIf } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EUserRole } from 'src/modules/user/dto/create-user.dto';

export class SignUpDto {
  @ApiProperty({
    description: 'User role',
    enum: EUserRole,
    default: EUserRole.TEACHER,
  })
  @IsOptional()
  @IsEnum(EUserRole)
  role?: EUserRole;

  @ApiPropertyOptional({
    description: 'School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles, must be null or omitted for INDEPENDENT_TEACHER and ADMIN)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ValidateIf(o => o.role !== EUserRole.INDEPENDENT_TEACHER && o.role !== EUserRole.ADMIN)
  @IsNotEmpty()
  @IsUUID()
  schoolId?: string;
  @ApiProperty({
    description: 'User full name',
    example: '<PERSON>',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User password (minimum 6 characters)',
    example: 'password123',
    minLength: 6,
  })
  @IsNotEmpty()
  @MinLength(6)
  password: string;
}
