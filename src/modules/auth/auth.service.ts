import {forwardRef, Inject, Injectable, UnauthorizedException, BadRequestException, Logger, InternalServerErrorException} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../user/user.service';
import { SchoolService } from '../school/school.service';
import { SignInDto } from './dto/sign-in.dto';
import { SignUpDto } from './dto/sign-up';
import { EUserRole } from '../user/dto/create-user.dto';
import { Bcrypt } from 'src/core/utils/bcrypt';
import { MailService } from 'src/mail/mail.service';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import * as crypto from 'crypto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    private readonly mailService: MailService,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => SchoolService))
    private readonly schoolService: SchoolService,
  ) {}

  async signIn(loginDto: SignInDto) {
    const user = await this.userService.findByEmail(loginDto.email);
    if (!user) {
      console.log('User not found'); // Debug log
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await Bcrypt.compare(
      loginDto.password,
      user.password,
    );

    if (!isPasswordValid) {
      // Ensure hashing is correct
      throw new UnauthorizedException('Invalid credentials');
    }

    // Include school information in the token if available
    const payload = { 
      sub: user.id, 
      email: user.email, 
      role: user.role,
      schoolId: user.schoolId || null
    };

    // Prepare user response object
    const userResponse = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      schoolId: user.schoolId || null
    };

    // Fetch school information if user has a schoolId
    if (user.schoolId) {
      try {
        const school = await this.schoolService.findOne(user.schoolId);
        // Add school information to the response
        userResponse['school'] = school;
      } catch (error) {
        // If school not found, just continue without adding school info
        console.log(`School with ID ${user.schoolId} not found`);
      }
    }

    return { 
      accessToken: this.jwtService.sign(payload),
      user: userResponse
    };
  }

  async signUp(registerDto: SignUpDto) {
    const { role = EUserRole.TEACHER, schoolId, ...userData } = registerDto;

    // Validate role and school combinations
    if (role === EUserRole.INDEPENDENT_TEACHER) {
      // INDEPENDENT_TEACHER must not have a schoolId
      if (schoolId) {
        throw new Error('Independent Teacher cannot be assigned to an existing school');
      }
    } else if ((role === EUserRole.TEACHER || role === EUserRole.STUDENT || role === EUserRole.SCHOOL_MANAGER) && !schoolId) {
      throw new Error(`School ID is required for ${role} role`);
    }

    // Create user with specified role and school
    const user = await this.userService.create({
      ...userData,
      role,
      schoolId: role === EUserRole.INDEPENDENT_TEACHER ? null : schoolId,
    });

    // Auto-create school for independent teachers
    if (role === EUserRole.INDEPENDENT_TEACHER) {
      try {
        // Create a default school for the independent teacher
        await this.schoolService.create(
          {
            name: `${userData.name}'s School`,
            email: userData.email,
            // Optional fields are left undefined/null
          },
          user.id,
          EUserRole.INDEPENDENT_TEACHER
        );

        // The school service already updates the user's schoolId,
        // but we need to return the updated user
        const updatedUser = await this.userService.findById(user.id);
        return updatedUser || user;
      } catch (error) {
        // If school creation fails, we should still return the user
        // but log the error for debugging
        console.error('Failed to auto-create school for independent teacher:', error);
        return user;
      }
    }

    return user;
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    try {
      this.logger.log(`Password reset requested for email: ${forgotPasswordDto.email}`);

      const user = await this.userService.findByEmail(forgotPasswordDto.email);
      if (!user) {
        // To prevent email enumeration, we don't throw an error.
        // We just don't send an email but still log for monitoring
        this.logger.warn(`Password reset requested for non-existent email: ${forgotPasswordDto.email}`);
        return;
      }

      const resetToken = crypto.randomBytes(32).toString('hex');
      
      const passwordResetExpires = new Date(Date.now() + 3600000); // 1 hour

      await this.userService.update(user.id, {
        passwordResetToken: resetToken,
        passwordResetExpires,
      });

      const resetUrl = `https://app.edusg.co/reset-password?token=${resetToken}`;

      try {
        await this.mailService.sendUserPasswordReset(user, resetUrl);
        this.logger.log(`Password reset email sent successfully for user: ${user.id}`);
      } catch (mailError) {
        // If email fails, clean up the reset token to prevent security issues
        await this.userService.update(user.id, {
          passwordResetToken: null,
          passwordResetExpires: null,
        });
        this.logger.error(`Failed to send password reset email for user: ${user.id}`, mailError.stack);
        throw new InternalServerErrorException('Failed to send password reset email. Please try again later.');
      }
    } catch (error) {
      // Log the error but don't reveal information to the client unless it's a known error
      if (error instanceof InternalServerErrorException) {
        throw error;
      }
      this.logger.error(`Unexpected error in forgotPassword for email: ${forgotPasswordDto.email}`, error.stack);
      throw new InternalServerErrorException('An unexpected error occurred. Please try again later.');
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    try {
      const { token, newPassword } = resetPasswordDto;
      const user = await this.userService.findByPasswordResetToken(token.trim());
      if (!user || !user.passwordResetExpires || user.passwordResetExpires < new Date()) {
        this.logger.warn('Password reset attempted with invalid or expired token');
        throw new BadRequestException('Password reset token is invalid or has expired.');
      }

      await this.userService.update(user.id, {
        password: newPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
      });

      this.logger.log(`Password reset completed successfully for user: ${user.id}`);
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error('Unexpected error during password reset', error.stack);
      throw new InternalServerErrorException('An unexpected error occurred during password reset. Please try again.');
    }
  }
}
