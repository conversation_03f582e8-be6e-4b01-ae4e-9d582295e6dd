import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { PUBLIC_KEY } from '../decorators/public.decorator';
import { RbacExceptions } from '../exceptions/rbac-exceptions';

@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true; // Skip guard for public routes
    }

    const request = context.switchToHttp().getRequest<Request>();
    const endpoint = `${request.method} ${request.route?.path || request.url}`;
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      this.logger.warn(`Authentication failed: Missing token for ${endpoint}`);
      throw RbacExceptions.authenticationRequired();
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });

      // Validate payload structure
      if (!payload.sub || !payload.email || !payload.role) {
        this.logger.warn(`Authentication failed: Invalid token payload structure for ${endpoint}`);
        throw RbacExceptions.invalidToken();
      }

      request['user'] = payload; // Attach the user payload to the request object
      this.logger.debug(`Authentication successful: User ${payload.sub} accessing ${endpoint}`);

    } catch (error) {
      if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
        this.logger.warn(`Authentication failed: Invalid/expired token for ${endpoint}`);
        throw RbacExceptions.invalidToken();
      }
      // Re-throw if it's already one of our custom exceptions
      throw error;
    }

    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [_, token] = request.headers.authorization?.split(' ') ?? [];
    return token;
  }
}
