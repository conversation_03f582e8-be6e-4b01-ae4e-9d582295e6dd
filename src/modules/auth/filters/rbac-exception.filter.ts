import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  Logger,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';
import { Response } from 'express';

/**
 * Global exception filter for RBAC-related errors
 * Provides consistent error responses and logging for authorization failures
 */
@Catch(ForbiddenException, UnauthorizedException)
export class RbacExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(RbacExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();
    const status = exception.getStatus();

    // Extract user information if available
    const user = request.user;
    const userId = user?.sub || 'anonymous';
    const userRole = user?.role || 'none';
    const endpoint = `${request.method} ${request.route?.path || request.url}`;

    // Get exception response
    const exceptionResponse = exception.getResponse();
    let errorResponse: any;

    if (typeof exceptionResponse === 'object') {
      errorResponse = exceptionResponse;
    } else {
      errorResponse = {
        message: exceptionResponse,
        error: status === 403 ? 'Forbidden' : 'Unauthorized',
        statusCode: status,
      };
    }

    // Add timestamp if not present
    if (!errorResponse.timestamp) {
      errorResponse.timestamp = new Date().toISOString();
    }

    // Add request ID for tracking (if available)
    if (request.id) {
      errorResponse.requestId = request.id;
    }

    // Log the security event
    this.logSecurityEvent(status, userId, userRole, endpoint, errorResponse);

    // Send standardized error response
    response.status(status).json(errorResponse);
  }

  private logSecurityEvent(
    status: number,
    userId: string,
    userRole: string,
    endpoint: string,
    errorResponse: any,
  ): void {
    const logMessage = `Security Event: ${status} - User ${userId} (${userRole}) denied access to ${endpoint}`;
    
    if (status === 401) {
      this.logger.warn(`${logMessage} - Authentication failure`);
    } else if (status === 403) {
      this.logger.warn(`${logMessage} - Authorization failure: ${errorResponse.message}`);
    }

    // Log additional details for debugging (only in development)
    if (process.env.NODE_ENV === 'development') {
      this.logger.debug(`Security Event Details:`, {
        userId,
        userRole,
        endpoint,
        status,
        error: errorResponse,
      });
    }
  }
}
