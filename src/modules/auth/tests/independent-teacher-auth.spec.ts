import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from '../auth.service';
import { UserService } from '../../user/user.service';
import { SchoolService } from '../../school/school.service';
import { JwtService } from '@nestjs/jwt';
import { EUserRole } from '../../user/dto/create-user.dto';
import { SignUpDto } from '../dto/sign-up';

// Mock bcrypt at the module level
jest.mock('src/core/utils/bcrypt', () => ({
  Bcrypt: {
    compare: jest.fn(),
    hash: jest.fn(),
  },
}));

describe('AuthService - INDEPENDENT_TEACHER Role', () => {
  let authService: AuthService;
  let userService: UserService;
  let schoolService: SchoolService;
  let jwtService: JwtService;

  const mockUserService = {
    create: jest.fn(),
    findByEmail: jest.fn(),
    findById: jest.fn(),
  };

  const mockSchoolService = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: SchoolService,
          useValue: mockSchoolService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
      ],
    }).compile();

    authService = module.get<AuthService>(AuthService);
    userService = module.get<UserService>(UserService);
    schoolService = module.get<SchoolService>(SchoolService);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('signUp', () => {
    it('should successfully register an INDEPENDENT_TEACHER without schoolId and auto-create school', async () => {
      const signUpDto: SignUpDto = {
        name: 'John Independent',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.INDEPENDENT_TEACHER,
        // schoolId is intentionally omitted
      };

      const initialUser = {
        id: 'user-id',
        name: 'John Independent',
        email: '<EMAIL>',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      };

      const createdSchool = {
        id: 'school-id',
        name: "John Independent's School",
        email: '<EMAIL>',
        adminId: 'user-id',
      };

      const updatedUser = {
        ...initialUser,
        schoolId: 'school-id',
      };

      mockUserService.create.mockResolvedValue(initialUser);
      mockSchoolService.create.mockResolvedValue(createdSchool);
      mockUserService.findById.mockResolvedValue(updatedUser);

      const result = await authService.signUp(signUpDto);

      expect(mockUserService.create).toHaveBeenCalledWith({
        name: 'John Independent',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      });

      expect(mockSchoolService.create).toHaveBeenCalledWith(
        {
          name: "John Independent's School",
          email: '<EMAIL>',
        },
        'user-id',
        EUserRole.INDEPENDENT_TEACHER
      );

      expect(mockUserService.findById).toHaveBeenCalledWith('user-id');
      expect(result).toEqual(updatedUser);
    });

    it('should successfully register an INDEPENDENT_TEACHER with explicit null schoolId and auto-create school', async () => {
      const signUpDto: SignUpDto = {
        name: 'Jane Independent',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: undefined, // explicitly undefined
      };

      const initialUser = {
        id: 'user-id-2',
        name: 'Jane Independent',
        email: '<EMAIL>',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      };

      const createdSchool = {
        id: 'school-id-2',
        name: "Jane Independent's School",
        email: '<EMAIL>',
        adminId: 'user-id-2',
      };

      const updatedUser = {
        ...initialUser,
        schoolId: 'school-id-2',
      };

      mockUserService.create.mockResolvedValue(initialUser);
      mockSchoolService.create.mockResolvedValue(createdSchool);
      mockUserService.findById.mockResolvedValue(updatedUser);

      const result = await authService.signUp(signUpDto);

      expect(mockUserService.create).toHaveBeenCalledWith({
        name: 'Jane Independent',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      });

      expect(mockSchoolService.create).toHaveBeenCalledWith(
        {
          name: "Jane Independent's School",
          email: '<EMAIL>',
        },
        'user-id-2',
        EUserRole.INDEPENDENT_TEACHER
      );

      expect(result).toEqual(updatedUser);
    });

    it('should still return user if school creation fails for INDEPENDENT_TEACHER', async () => {
      const signUpDto: SignUpDto = {
        name: 'John Independent',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.INDEPENDENT_TEACHER,
      };

      const initialUser = {
        id: 'user-id',
        name: 'John Independent',
        email: '<EMAIL>',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      };

      mockUserService.create.mockResolvedValue(initialUser);
      mockSchoolService.create.mockRejectedValue(new Error('School creation failed'));

      // Mock console.error to avoid test output noise
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const result = await authService.signUp(signUpDto);

      expect(mockUserService.create).toHaveBeenCalledWith({
        name: 'John Independent',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      });

      expect(mockSchoolService.create).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith('Failed to auto-create school for independent teacher:', expect.any(Error));
      expect(result).toEqual(initialUser);

      consoleSpy.mockRestore();
    });

    it('should use fallback schoolId if findById fails after school creation', async () => {
      const signUpDto: SignUpDto = {
        name: 'John Independent',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.INDEPENDENT_TEACHER,
      };

      const initialUser = {
        id: 'user-id',
        name: 'John Independent',
        email: '<EMAIL>',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      };

      const createdSchool = {
        id: 'school-id',
        name: "John Independent's School",
        email: '<EMAIL>',
        adminId: 'user-id',
      };

      const expectedUserWithSchool = {
        ...initialUser,
        schoolId: 'school-id',
      };

      mockUserService.create.mockResolvedValue(initialUser);
      mockSchoolService.create.mockResolvedValue(createdSchool);
      mockUserService.findById.mockResolvedValue(null); // Simulate findById failure

      const result = await authService.signUp(signUpDto);

      expect(mockUserService.create).toHaveBeenCalled();
      expect(mockSchoolService.create).toHaveBeenCalled();
      expect(mockUserService.findById).toHaveBeenCalledWith('user-id');
      expect(result).toEqual(expectedUserWithSchool);
    });

    it('should reject INDEPENDENT_TEACHER registration with schoolId provided', async () => {
      const signUpDto: SignUpDto = {
        name: 'John Independent',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: 'some-school-id',
      };

      await expect(authService.signUp(signUpDto)).rejects.toThrow(
        'Independent Teacher cannot be assigned to an existing school'
      );

      expect(mockUserService.create).not.toHaveBeenCalled();
    });

    it('should still require schoolId for TEACHER role', async () => {
      const signUpDto: SignUpDto = {
        name: 'Regular Teacher',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.TEACHER,
        // schoolId is intentionally omitted
      };

      await expect(authService.signUp(signUpDto)).rejects.toThrow(
        'School ID is required for teacher role'
      );

      expect(mockUserService.create).not.toHaveBeenCalled();
    });

    it('should still require schoolId for SCHOOL_MANAGER role', async () => {
      const signUpDto: SignUpDto = {
        name: 'School Manager',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.SCHOOL_MANAGER,
        // schoolId is intentionally omitted
      };

      await expect(authService.signUp(signUpDto)).rejects.toThrow(
        'School ID is required for school_manager role'
      );

      expect(mockUserService.create).not.toHaveBeenCalled();
    });

    it('should still require schoolId for STUDENT role', async () => {
      const signUpDto: SignUpDto = {
        name: 'Student',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.STUDENT,
        // schoolId is intentionally omitted
      };

      await expect(authService.signUp(signUpDto)).rejects.toThrow(
        'School ID is required for student role'
      );

      expect(mockUserService.create).not.toHaveBeenCalled();
    });
  });

  describe('signIn', () => {
    it('should successfully sign in an INDEPENDENT_TEACHER with null schoolId', async () => {
      const mockUser = {
        id: 'user-id',
        name: 'John Independent',
        email: '<EMAIL>',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
        password: 'hashed-password',
      };

      const signInDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      mockUserService.findByEmail.mockResolvedValue(mockUser);
      mockJwtService.sign.mockReturnValue('jwt-token');

      // Mock bcrypt comparison using the module-level mock
      const { Bcrypt } = require('src/core/utils/bcrypt');
      (Bcrypt.compare as jest.Mock).mockResolvedValue(true);

      const result = await authService.signIn(signInDto);

      expect(result.accessToken).toBe('jwt-token');
      expect(result.user).toEqual({
        id: 'user-id',
        name: 'John Independent',
        email: '<EMAIL>',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      });

      expect(mockJwtService.sign).toHaveBeenCalledWith({
        sub: 'user-id',
        email: '<EMAIL>',
        role: EUserRole.INDEPENDENT_TEACHER,
        schoolId: null,
      });

      // Should not try to fetch school information
      expect(mockSchoolService.findOne).not.toHaveBeenCalled();
    });
  });
});
