import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { AuthService } from '../auth.service';
import { UserService } from '../../user/user.service';
import { MailService } from '../../../mail/mail.service';
import { JwtService } from '@nestjs/jwt';
import { SchoolService } from '../../school/school.service';
import { ForgotPasswordDto } from '../dto/forgot-password.dto';
import { ResetPasswordDto } from '../dto/reset-password.dto';
import { User } from '../../user/entities/user.entity';
import * as crypto from 'crypto';

describe('AuthService - Password Reset', () => {
  let service: AuthService;
  let userService: UserService;
  let mailService: MailService;
  let configService: ConfigService;

  const mockUser: Partial<User> = {
    id: '123',
    email: '<EMAIL>',
    name: 'Test User',
    passwordResetToken: undefined,
    passwordResetExpires: undefined,
  };

  const mockUserService = {
    findByEmail: jest.fn(),
    findByPasswordResetToken: jest.fn(),
    update: jest.fn(),
  };

  const mockMailService = {
    sendUserPasswordReset: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockSchoolService = {
    // Add mock methods as needed
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UserService,
          useValue: mockUserService,
        },
        {
          provide: MailService,
          useValue: mockMailService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: SchoolService,
          useValue: mockSchoolService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userService = module.get<UserService>(UserService);
    mailService = module.get<MailService>(MailService);
    configService = module.get<ConfigService>(ConfigService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  describe('forgotPassword', () => {
    const forgotPasswordDto: ForgotPasswordDto = {
      email: '<EMAIL>',
    };

    it('should send password reset email for existing user', async () => {
      // Arrange
      mockUserService.findByEmail.mockResolvedValue(mockUser);
      mockUserService.update.mockResolvedValue(undefined);
      mockMailService.sendUserPasswordReset.mockResolvedValue(undefined);
      mockConfigService.get.mockReturnValue('http://localhost:3000');

      // Act
      await service.forgotPassword(forgotPasswordDto);

      // Assert
      expect(mockUserService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUserService.update).toHaveBeenCalledWith('123', {
        passwordResetToken: expect.any(String),
        passwordResetExpires: expect.any(Date),
      });
      expect(mockMailService.sendUserPasswordReset).toHaveBeenCalledWith(
        mockUser,
        expect.stringContaining('http://localhost:3000/reset-password?token=')
      );
    });

    it('should not send email for non-existent user', async () => {
      // Arrange
      mockUserService.findByEmail.mockResolvedValue(null);

      // Act
      await service.forgotPassword(forgotPasswordDto);

      // Assert
      expect(mockUserService.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUserService.update).not.toHaveBeenCalled();
      expect(mockMailService.sendUserPasswordReset).not.toHaveBeenCalled();
    });

    it('should clean up reset token if email fails', async () => {
      // Arrange
      mockUserService.findByEmail.mockResolvedValue(mockUser);
      mockUserService.update.mockResolvedValue(undefined);
      mockMailService.sendUserPasswordReset.mockRejectedValue(new Error('Email failed'));
      mockConfigService.get.mockReturnValue('http://localhost:3000');

      // Act & Assert
      await expect(service.forgotPassword(forgotPasswordDto)).rejects.toThrow(InternalServerErrorException);
      
      // Verify cleanup was called
      expect(mockUserService.update).toHaveBeenCalledTimes(2);
      expect(mockUserService.update).toHaveBeenLastCalledWith('123', {
        passwordResetToken: null,
        passwordResetExpires: null,
      });
    });
  });

  describe('resetPassword', () => {
    const resetPasswordDto: ResetPasswordDto = {
      token: 'valid-token',
      newPassword: 'newPassword123',
    };

    const mockUserWithResetToken: Partial<User> = {
      ...mockUser,
      passwordResetToken: 'hashed-token',
      passwordResetExpires: new Date(Date.now() + 3600000), // 1 hour from now
    };

    it('should reset password with valid token', async () => {
      // Arrange
      mockUserService.findByPasswordResetToken.mockResolvedValue(mockUserWithResetToken);
      mockUserService.update.mockResolvedValue(undefined);

      // Act
      await service.resetPassword(resetPasswordDto);

      // Assert
      expect(mockUserService.findByPasswordResetToken).toHaveBeenCalledWith(
        expect.any(String) // hashed token
      );
      expect(mockUserService.update).toHaveBeenCalledWith('123', {
        password: expect.any(String), // hashed password
        passwordResetToken: null,
        passwordResetExpires: null,
      });
    });

    it('should throw error for invalid token', async () => {
      // Arrange
      mockUserService.findByPasswordResetToken.mockResolvedValue(null);

      // Act & Assert
      await expect(service.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
      expect(mockUserService.update).not.toHaveBeenCalled();
    });

    it('should throw error for expired token', async () => {
      // Arrange
      const expiredUser = {
        ...mockUserWithResetToken,
        passwordResetExpires: new Date(Date.now() - 3600000), // 1 hour ago
      };
      mockUserService.findByPasswordResetToken.mockResolvedValue(expiredUser);

      // Act & Assert
      await expect(service.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
      expect(mockUserService.update).not.toHaveBeenCalled();
    });
  });

  describe('token generation and hashing', () => {
    it('should generate different tokens for each request', async () => {
      // Arrange
      mockUserService.findByEmail.mockResolvedValue(mockUser);
      mockUserService.update.mockResolvedValue(undefined);
      mockMailService.sendUserPasswordReset.mockResolvedValue(undefined);
      mockConfigService.get.mockReturnValue('http://localhost:3000');

      const forgotPasswordDto: ForgotPasswordDto = { email: '<EMAIL>' };

      // Act
      await service.forgotPassword(forgotPasswordDto);
      const firstCall = mockUserService.update.mock.calls[0][1];
      
      jest.clearAllMocks();
      mockUserService.findByEmail.mockResolvedValue(mockUser);
      mockUserService.update.mockResolvedValue(undefined);
      mockMailService.sendUserPasswordReset.mockResolvedValue(undefined);
      
      await service.forgotPassword(forgotPasswordDto);
      const secondCall = mockUserService.update.mock.calls[0][1];

      // Assert
      expect(firstCall.passwordResetToken).not.toBe(secondCall.passwordResetToken);
    });
  });
});
