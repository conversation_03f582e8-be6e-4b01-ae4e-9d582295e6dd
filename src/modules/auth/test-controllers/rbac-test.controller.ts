import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '../guards/auth.guard';
import { RoleGuard } from '../guards/role.guard';
import { Roles } from '../decorators/role.decorator';
import { EUserRole } from '../../user/dto/create-user.dto';
import { ActiveUser } from '../decorators/active-user.decorator';

/**
 * Test controller to validate RBAC infrastructure
 * This controller should be removed after RBAC implementation is complete
 */
@ApiTags('RBAC Test')
@Controller('test/rbac')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class RbacTestController {

  @Get('admin-only')
  @Roles(EUserRole.ADMIN)
  @ApiOperation({ summary: 'Test endpoint - Admin only access' })
  @ApiResponse({ status: 200, description: 'Success - Admin access confirmed' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  adminOnly(@ActiveUser() user: any) {
    return {
      message: 'Admin access confirmed',
      user: {
        id: user.sub,
        email: user.email,
        role: user.role,
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('admin-or-school-manager')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER)
  @ApiOperation({ summary: 'Test endpoint - Admin or School Manager access' })
  @ApiResponse({ status: 200, description: 'Success - Admin or School Manager access confirmed' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin or School Manager role required' })
  adminOrSchoolManager(@ActiveUser() user: any) {
    return {
      message: 'Admin or School Manager access confirmed',
      user: {
        id: user.sub,
        email: user.email,
        role: user.role,
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('all-roles')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.STUDENT)
  @ApiOperation({ summary: 'Test endpoint - All authenticated users' })
  @ApiResponse({ status: 200, description: 'Success - Any authenticated user access confirmed' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Authentication required' })
  allRoles(@ActiveUser() user: any) {
    return {
      message: 'Authenticated user access confirmed',
      user: {
        id: user.sub,
        email: user.email,
        role: user.role,
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('no-role-restriction')
  @ApiOperation({ summary: 'Test endpoint - No role restriction (authenticated users only)' })
  @ApiResponse({ status: 200, description: 'Success - Any authenticated user access confirmed' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Authentication required' })
  noRoleRestriction(@ActiveUser() user: any) {
    return {
      message: 'No role restriction - any authenticated user can access',
      user: {
        id: user.sub,
        email: user.email,
        role: user.role,
      },
      timestamp: new Date().toISOString(),
    };
  }
}
