import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { EUserRole } from 'src/modules/user/dto/create-user.dto';

export interface ActiveUserData {
  sub: string;
  email: string;
  role: EUserRole;
}

export const ActiveUser = createParamDecorator(
  (field: keyof ActiveUserData | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user: ActiveUserData | undefined = request['user'];
    return field ? user?.[field] : user;
  },
);
