import { Test, TestingModule } from '@nestjs/testing';
import { RbacService, UserContext } from './rbac.service';
import { EUserRole } from '../../user/dto/create-user.dto';
import { RbacExceptions } from '../exceptions/rbac-exceptions';

describe('RbacService', () => {
  let service: RbacService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RbacService],
    }).compile();

    service = module.get<RbacService>(RbacService);
  });

  describe('hasAnyRole', () => {
    it('should return true for admin with any role requirement', () => {
      const adminUser: UserContext = {
        sub: '1',
        email: '<EMAIL>',
        role: EUserRole.ADMIN,
      };

      expect(service.hasAnyRole(adminUser, [EUserRole.TEACHER])).toBe(true);
      expect(service.hasAnyRole(adminUser, [EUserRole.SCHOOL_MANAGER])).toBe(true);
    });

    it('should return true when user has one of the required roles', () => {
      const teacherUser: UserContext = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.TEACHER,
      };

      expect(service.hasAnyRole(teacherUser, [EUserRole.TEACHER, EUserRole.STUDENT])).toBe(true);
    });

    it('should return false when user does not have any required role', () => {
      const studentUser: UserContext = {
        sub: '3',
        email: '<EMAIL>',
        role: EUserRole.STUDENT,
      };

      expect(service.hasAnyRole(studentUser, [EUserRole.ADMIN, EUserRole.TEACHER])).toBe(false);
    });

    it('should return false for null user or empty roles', () => {
      const user: UserContext = {
        sub: '1',
        email: '<EMAIL>',
        role: EUserRole.TEACHER,
      };

      expect(service.hasAnyRole(null as any, [EUserRole.TEACHER])).toBe(false);
      expect(service.hasAnyRole(user, [])).toBe(false);
      expect(service.hasAnyRole(user, null as any)).toBe(false);
    });
  });

  describe('role checking methods', () => {
    const adminUser: UserContext = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
    const schoolManagerUser: UserContext = { sub: '2', email: '<EMAIL>', role: EUserRole.SCHOOL_MANAGER };
    const teacherUser: UserContext = { sub: '3', email: '<EMAIL>', role: EUserRole.TEACHER };
    const studentUser: UserContext = { sub: '4', email: '<EMAIL>', role: EUserRole.STUDENT };

    it('should correctly identify admin users', () => {
      expect(service.isAdmin(adminUser)).toBe(true);
      expect(service.isAdmin(schoolManagerUser)).toBe(false);
    });

    it('should correctly identify school manager users', () => {
      expect(service.isSchoolManager(schoolManagerUser)).toBe(true);
      expect(service.isSchoolManager(teacherUser)).toBe(false);
    });

    it('should correctly identify teacher users', () => {
      expect(service.isTeacher(teacherUser)).toBe(true);
      expect(service.isTeacher(studentUser)).toBe(false);
    });

    it('should correctly identify student users', () => {
      expect(service.isStudent(studentUser)).toBe(true);
      expect(service.isStudent(teacherUser)).toBe(false);
    });
  });

  describe('validateSchoolManagerAccess', () => {
    it('should not validate non-school-manager users', () => {
      const adminUser: UserContext = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
      
      expect(() => service.validateSchoolManagerAccess(adminUser, 'school-1')).not.toThrow();
    });

    it('should throw exception when school manager has no assigned school', () => {
      const schoolManagerUser: UserContext = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
      };

      expect(() => service.validateSchoolManagerAccess(schoolManagerUser, 'school-1'))
        .toThrow(RbacExceptions.schoolManagerNoSchool());
    });

    it('should throw exception when school manager accesses different school', () => {
      const schoolManagerUser: UserContext = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };

      expect(() => service.validateSchoolManagerAccess(schoolManagerUser, 'school-2'))
        .toThrow(RbacExceptions.schoolAccessDenied());
    });

    it('should not throw when school manager accesses their own school', () => {
      const schoolManagerUser: UserContext = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };

      expect(() => service.validateSchoolManagerAccess(schoolManagerUser, 'school-1')).not.toThrow();
    });
  });

  describe('validateSchoolManagerUserCreation', () => {
    it('should not validate non-school-manager users', () => {
      const adminUser: UserContext = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
      
      expect(() => service.validateSchoolManagerUserCreation(adminUser, EUserRole.ADMIN)).not.toThrow();
    });

    it('should throw exception when school manager tries to create admin', () => {
      const schoolManagerUser: UserContext = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };

      expect(() => service.validateSchoolManagerUserCreation(schoolManagerUser, EUserRole.ADMIN))
        .toThrow(RbacExceptions.cannotCreateAdmin());
    });

    it('should throw exception when school manager tries to create another school manager', () => {
      const schoolManagerUser: UserContext = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };

      expect(() => service.validateSchoolManagerUserCreation(schoolManagerUser, EUserRole.SCHOOL_MANAGER))
        .toThrow(RbacExceptions.cannotCreateAdmin());
    });

    it('should not throw when school manager creates teacher or student', () => {
      const schoolManagerUser: UserContext = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };

      expect(() => service.validateSchoolManagerUserCreation(schoolManagerUser, EUserRole.TEACHER)).not.toThrow();
      expect(() => service.validateSchoolManagerUserCreation(schoolManagerUser, EUserRole.STUDENT)).not.toThrow();
    });
  });

  describe('getEffectiveSchoolId', () => {
    it('should return requested school ID for admin', () => {
      const adminUser: UserContext = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
      
      expect(service.getEffectiveSchoolId(adminUser, 'school-1')).toBe('school-1');
      expect(service.getEffectiveSchoolId(adminUser, undefined)).toBe(undefined);
    });

    it('should return assigned school ID for school manager', () => {
      const schoolManagerUser: UserContext = {
        sub: '2',
        email: '<EMAIL>',
        role: EUserRole.SCHOOL_MANAGER,
        schoolId: 'school-1',
      };

      expect(service.getEffectiveSchoolId(schoolManagerUser, 'school-2')).toBe('school-1');
      expect(service.getEffectiveSchoolId(schoolManagerUser, undefined)).toBe('school-1');
    });

    it('should return undefined for other roles', () => {
      const teacherUser: UserContext = { sub: '3', email: '<EMAIL>', role: EUserRole.TEACHER };
      
      expect(service.getEffectiveSchoolId(teacherUser, 'school-1')).toBe(undefined);
    });
  });

  describe('getRoleLevel', () => {
    it('should return correct hierarchy levels', () => {
      expect(service.getRoleLevel(EUserRole.ADMIN)).toBe(4);
      expect(service.getRoleLevel(EUserRole.SCHOOL_MANAGER)).toBe(3);
      expect(service.getRoleLevel(EUserRole.TEACHER)).toBe(2);
      expect(service.getRoleLevel(EUserRole.STUDENT)).toBe(1);
    });
  });

  describe('hasMinimumRole', () => {
    it('should correctly check role hierarchy', () => {
      const adminUser: UserContext = { sub: '1', email: '<EMAIL>', role: EUserRole.ADMIN };
      const teacherUser: UserContext = { sub: '2', email: '<EMAIL>', role: EUserRole.TEACHER };
      const studentUser: UserContext = { sub: '3', email: '<EMAIL>', role: EUserRole.STUDENT };

      expect(service.hasMinimumRole(adminUser, EUserRole.TEACHER)).toBe(true);
      expect(service.hasMinimumRole(teacherUser, EUserRole.TEACHER)).toBe(true);
      expect(service.hasMinimumRole(studentUser, EUserRole.TEACHER)).toBe(false);
    });
  });
});
