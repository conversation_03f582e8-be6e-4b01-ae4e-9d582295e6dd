import { Injectable, Logger } from '@nestjs/common';
import { EUserRole } from '../../user/dto/create-user.dto';
import { RbacExceptions } from '../exceptions/rbac-exceptions';

export interface UserContext {
  sub: string;
  email: string;
  role: EUserRole;
  schoolId?: string;
}

@Injectable()
export class RbacService {
  private readonly logger = new Logger(RbacService.name);

  /**
   * Check if user has any of the specified roles
   */
  hasAnyRole(user: UserContext, roles: EUserRole[]): boolean {
    if (!user || !roles || roles.length === 0) {
      return false;
    }

    // Admin has access to everything
    if (user.role === EUserRole.ADMIN) {
      return true;
    }

    return roles.includes(user.role);
  }

  /**
   * Check if user has a specific role
   */
  hasRole(user: UserContext, role: EUserRole): boolean {
    return this.hasAnyRole(user, [role]);
  }

  /**
   * Check if user is admin
   */
  isAdmin(user: UserContext): boolean {
    return user?.role === EUserRole.ADMIN;
  }

  /**
   * Check if user is school manager
   */
  isSchoolManager(user: UserContext): boolean {
    return user?.role === EUserRole.SCHOOL_MANAGER;
  }

  /**
   * Check if user is teacher
   */
  isTeacher(user: UserContext): boolean {
    return user?.role === EUserRole.TEACHER;
  }

  /**
   * Check if user is student
   */
  isStudent(user: UserContext): boolean {
    return user?.role === EUserRole.STUDENT;
  }

  /**
   * Check if user is an independent teacher
   */
  isIndependentTeacher(user: UserContext): boolean {
    return user?.role === EUserRole.INDEPENDENT_TEACHER;
  }

  /**
   * Validate school manager has access to a specific school
   */
  validateSchoolManagerAccess(user: UserContext, targetSchoolId: string): void {
    if (!this.isSchoolManager(user)) {
      return; // Not a school manager, no validation needed
    }

    if (!user.schoolId) {
      this.logger.warn(`School manager ${user.sub} has no assigned school`);
      throw RbacExceptions.schoolManagerNoSchool();
    }

    if (user.schoolId !== targetSchoolId) {
      this.logger.warn(`School manager ${user.sub} attempted to access school ${targetSchoolId}, but is assigned to ${user.schoolId}`);
      throw RbacExceptions.schoolAccessDenied();
    }
  }

  /**
   * Validate school manager can create user with specified role
   */
  validateSchoolManagerUserCreation(user: UserContext, targetRole: EUserRole): void {
    if (!this.isSchoolManager(user)) {
      return; // Not a school manager, no validation needed
    }

    if (targetRole === EUserRole.ADMIN || targetRole === EUserRole.SCHOOL_MANAGER) {
      this.logger.warn(`School manager ${user.sub} attempted to create user with role ${targetRole}`);
      throw RbacExceptions.cannotCreateAdmin();
    }
  }

  /**
   * Validate school manager can assign user to specified school
   */
  validateSchoolManagerUserAssignment(user: UserContext, targetSchoolId: string): void {
    if (!this.isSchoolManager(user)) {
      return; // Not a school manager, no validation needed
    }

    if (!user.schoolId) {
      this.logger.warn(`School manager ${user.sub} has no assigned school`);
      throw RbacExceptions.schoolManagerNoSchool();
    }

    if (targetSchoolId && targetSchoolId !== user.schoolId) {
      this.logger.warn(`School manager ${user.sub} attempted to assign user to school ${targetSchoolId}, but is assigned to ${user.schoolId}`);
      throw RbacExceptions.cannotTransferUsers();
    }
  }

  /**
   * Get the effective school ID for filtering operations
   * For school managers, returns their assigned school ID
   * For admins, returns the provided school ID (no restriction)
   */
  getEffectiveSchoolId(user: UserContext, requestedSchoolId?: string): string | undefined {
    if (this.isAdmin(user)) {
      return requestedSchoolId; // Admin can access any school
    }

    if (this.isSchoolManager(user)) {
      return user.schoolId; // School manager can only access their school
    }

    return undefined; // Other roles don't have school-based filtering
  }

  /**
   * Check if user can access a specific school
   */
  canAccessSchool(user: UserContext, schoolId: string): boolean {
    if (this.isAdmin(user)) {
      return true; // Admin can access any school
    }

    if (this.isSchoolManager(user) || this.isTeacher(user)) {
      return user.schoolId === schoolId; // Must be assigned to the school
    }

    if (this.isIndependentTeacher(user)) {
      return user.schoolId === schoolId; // Independent teacher can access their own school
    }

    return false; // Students and others cannot access school information
  }

  /**
   * Check if user can manage users in a specific school
   */
  canManageUsersInSchool(user: UserContext, schoolId: string): boolean {
    if (this.isAdmin(user)) {
      return true; // Admin can manage users in any school
    }

    if (this.isSchoolManager(user)) {
      return user.schoolId === schoolId; // Can only manage users in their school
    }

    return false; // Teachers and students cannot manage users
  }

  /**
   * Log access attempt for audit purposes
   */
  logAccessAttempt(user: UserContext, resource: string, action: string, granted: boolean): void {
    const logLevel = granted ? 'debug' : 'warn';
    const status = granted ? 'GRANTED' : 'DENIED';
    
    this.logger[logLevel](`Access ${status}: User ${user.sub} (${user.role}) attempted ${action} on ${resource}`);
  }

  /**
   * Get role hierarchy level (higher number = more privileges)
   */
  getRoleLevel(role: EUserRole): number {
    switch (role) {
      case EUserRole.ADMIN:
        return 4;
      case EUserRole.SCHOOL_MANAGER:
        return 3;
      case EUserRole.TEACHER:
        return 2;
      case EUserRole.INDEPENDENT_TEACHER:
        return 2; // Same level as TEACHER for most purposes
      case EUserRole.STUDENT:
        return 1;
      default:
        return 0;
    }
  }

  /**
   * Check if user role is higher than or equal to required role
   */
  hasMinimumRole(user: UserContext, minimumRole: EUserRole): boolean {
    return this.getRoleLevel(user.role) >= this.getRoleLevel(minimumRole);
  }
}
