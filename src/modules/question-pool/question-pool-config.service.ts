import { Injectable, Inject } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import questionPoolConfig, { QuestionPoolConfigType } from '../../core/configs/question-pool/question-pool.config';
import { WorksheetGenerationOptionsDto, SelectionStrategy, WorksheetGenerationOptions } from '../worksheet/dto/worksheet-generation-options.dto';

/**
 * Service for managing question pool configuration and validation
 * Provides methods to resolve configuration with user overrides and validate strategies
 */
@Injectable()
export class QuestionPoolConfigService {
  constructor(
    @Inject(questionPoolConfig.KEY)
    private readonly config: ConfigType<typeof questionPoolConfig>,
  ) {}

  /**
   * Get the current question pool configuration
   */
  getConfig(): QuestionPoolConfigType {
    return this.config;
  }

  /**
   * Check if question pool is enabled
   */
  isQuestionPoolEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Get the default selection strategy
   */
  getDefaultSelectionStrategy(): SelectionStrategy {
    return this.config.defaultSelectionStrategy as SelectionStrategy;
  }

  /**
   * Get the minimum pool questions threshold
   */
  getMinPoolQuestionsThreshold(): number {
    return this.config.minPoolQuestionsThreshold;
  }

  /**
   * Check if a specific selection strategy is allowed
   */
  isStrategyAllowed(strategy: SelectionStrategy): boolean {
    switch (strategy) {
      case SelectionStrategy.POOL_ONLY:
        return this.config.featureFlags.allowPoolOnlyStrategy;
      case SelectionStrategy.AI_ONLY:
        return this.config.featureFlags.allowAiOnlyStrategy;
      case SelectionStrategy.HYBRID:
        return this.config.featureFlags.allowHybridStrategy;
      case SelectionStrategy.MIXED:
        return this.config.featureFlags.allowMixedStrategy;
      default:
        return false;
    }
  }

  /**
   * Resolve worksheet generation options with configuration defaults and validation
   * @param options User-provided options (optional)
   * @returns Resolved options with configuration applied
   */
  resolveWorksheetGenerationOptions(
    options?: WorksheetGenerationOptionsDto,
  ): WorksheetGenerationOptions {
    const resolved: WorksheetGenerationOptions = {
      ...options,
      poolEnabled: this.isQuestionPoolEnabled(),
    };

    // Determine the strategy to use
    let targetStrategy: SelectionStrategy;

    if (options?.selectionStrategy) {
      // User provided a strategy, validate it
      if (this.isStrategyAllowed(options.selectionStrategy)) {
        targetStrategy = options.selectionStrategy;
        resolved.strategyAllowed = true;
      } else {
        // Strategy not allowed, fall back to default
        targetStrategy = this.getDefaultSelectionStrategy();
        resolved.strategyAllowed = false;
      }
    } else {
      // No user strategy, use default
      targetStrategy = this.getDefaultSelectionStrategy();
      resolved.strategyAllowed = true;
    }

    resolved.resolvedStrategy = targetStrategy;

    // Apply configuration defaults for missing values
    if (resolved.minPoolQuestionsRequired === undefined) {
      resolved.minPoolQuestionsRequired = this.getMinPoolQuestionsThreshold();
    }

    // Handle pool override logic
    if (resolved.useQuestionPoolOverride !== undefined) {
      // User explicitly set override, respect it if pool is enabled
      resolved.poolEnabled = resolved.poolEnabled && resolved.useQuestionPoolOverride;
    }

    return resolved;
  }

  /**
   * Validate if the resolved options are compatible with current configuration
   * @param resolvedOptions Resolved worksheet generation options
   * @returns Validation result with any issues
   */
  validateResolvedOptions(resolvedOptions: WorksheetGenerationOptions): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];

    // Check if pool is required but disabled
    if (
      (resolvedOptions.resolvedStrategy === SelectionStrategy.POOL_ONLY ||
       resolvedOptions.resolvedStrategy === SelectionStrategy.HYBRID ||
       resolvedOptions.resolvedStrategy === SelectionStrategy.MIXED) &&
      !resolvedOptions.poolEnabled
    ) {
      issues.push('Question pool is required for the selected strategy but is disabled');
    }

    // Check if strategy is allowed
    if (!resolvedOptions.strategyAllowed) {
      issues.push(`Selection strategy '${resolvedOptions.selectionStrategy}' is not allowed by feature flags`);
    }

    // Check minimum threshold
    if (
      resolvedOptions.minPoolQuestionsRequired !== undefined &&
      resolvedOptions.minPoolQuestionsRequired < 0
    ) {
      issues.push('Minimum pool questions required cannot be negative');
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  }

  /**
   * Get all allowed strategies based on current feature flags
   */
  getAllowedStrategies(): SelectionStrategy[] {
    const allowed: SelectionStrategy[] = [];

    if (this.config.featureFlags.allowPoolOnlyStrategy) {
      allowed.push(SelectionStrategy.POOL_ONLY);
    }
    if (this.config.featureFlags.allowAiOnlyStrategy) {
      allowed.push(SelectionStrategy.AI_ONLY);
    }
    if (this.config.featureFlags.allowHybridStrategy) {
      allowed.push(SelectionStrategy.HYBRID);
    }
    if (this.config.featureFlags.allowMixedStrategy) {
      allowed.push(SelectionStrategy.MIXED);
    }

    return allowed;
  }
}
