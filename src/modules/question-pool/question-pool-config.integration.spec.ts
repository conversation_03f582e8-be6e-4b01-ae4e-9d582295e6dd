import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QuestionPoolConfigService } from './question-pool-config.service';
import questionPoolConfig from '../../core/configs/question-pool/question-pool.config';
import { SelectionStrategy } from '../worksheet/dto/worksheet-generation-options.dto';

/**
 * Integration tests demonstrating how the configuration service
 * would be used by consuming services like QuestionPoolService or WorksheetGenerateConsumer
 */
describe('QuestionPoolConfig Integration', () => {
  let configService: ConfigService;
  let questionPoolConfigService: QuestionPoolConfigService;

  beforeEach(async () => {
    // Clear environment variables
    delete process.env.QUESTION_POOL_ENABLED;
    delete process.env.DEFAULT_SELECTION_STRATEGY;
    delete process.env.MIN_POOL_QUESTIONS_THRESHOLD;
    delete process.env.ALLOW_POOL_ONLY_STRATEGY;
    delete process.env.ALLOW_AI_ONLY_STRATEGY;
    delete process.env.ALLOW_HYBRID_STRATEGY;
    delete process.env.ALLOW_MIXED_STRATEGY;
  });

  describe('Configuration Injection and Usage', () => {
    beforeEach(async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      configService = module.get<ConfigService>(ConfigService);
      questionPoolConfigService = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);
    });

    it('should inject configuration correctly into service', () => {
      expect(questionPoolConfigService).toBeDefined();
      expect(questionPoolConfigService.isQuestionPoolEnabled()).toBe(true);
      expect(questionPoolConfigService.getDefaultSelectionStrategy()).toBe(SelectionStrategy.HYBRID);
    });

    it('should access QUESTION_POOL_ENABLED correctly', () => {
      const enabled = questionPoolConfigService.isQuestionPoolEnabled();
      expect(enabled).toBe(true);
    });

    it('should use DEFAULT_SELECTION_STRATEGY when no user strategy provided', () => {
      const resolved = questionPoolConfigService.resolveWorksheetGenerationOptions();
      expect(resolved.resolvedStrategy).toBe(SelectionStrategy.HYBRID);
    });

    it('should prioritize user strategy when provided and allowed', () => {
      const resolved = questionPoolConfigService.resolveWorksheetGenerationOptions({
        selectionStrategy: SelectionStrategy.POOL_ONLY,
      });
      expect(resolved.resolvedStrategy).toBe(SelectionStrategy.POOL_ONLY);
      expect(resolved.strategyAllowed).toBe(true);
    });
  });

  describe('Environment Variable Override Scenarios', () => {
    it('should handle disabled question pool', async () => {
      process.env.QUESTION_POOL_ENABLED = 'false';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      const service = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);

      expect(service.isQuestionPoolEnabled()).toBe(false);

      const resolved = service.resolveWorksheetGenerationOptions({
        selectionStrategy: SelectionStrategy.POOL_ONLY,
      });

      const validation = service.validateResolvedOptions(resolved);
      expect(validation.isValid).toBe(false);
      expect(validation.issues).toContain(
        'Question pool is required for the selected strategy but is disabled'
      );
    });

    it('should handle custom default strategy', async () => {
      process.env.DEFAULT_SELECTION_STRATEGY = 'ai-only';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      const service = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);

      expect(service.getDefaultSelectionStrategy()).toBe(SelectionStrategy.AI_ONLY);

      const resolved = service.resolveWorksheetGenerationOptions();
      expect(resolved.resolvedStrategy).toBe(SelectionStrategy.AI_ONLY);
    });

    it('should handle custom minimum threshold', async () => {
      process.env.MIN_POOL_QUESTIONS_THRESHOLD = '25';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      const service = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);

      expect(service.getMinPoolQuestionsThreshold()).toBe(25);

      const resolved = service.resolveWorksheetGenerationOptions();
      expect(resolved.minPoolQuestionsRequired).toBe(25);
    });
  });

  describe('Feature Flag Behavior', () => {
    it('should handle strategy not allowed by feature flags', async () => {
      process.env.ALLOW_POOL_ONLY_STRATEGY = 'false';
      process.env.DEFAULT_SELECTION_STRATEGY = 'hybrid';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      const service = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);

      expect(service.isStrategyAllowed(SelectionStrategy.POOL_ONLY)).toBe(false);

      // When user requests a disallowed strategy, should fall back to default
      const resolved = service.resolveWorksheetGenerationOptions({
        selectionStrategy: SelectionStrategy.POOL_ONLY,
      });

      expect(resolved.resolvedStrategy).toBe(SelectionStrategy.HYBRID);
      expect(resolved.strategyAllowed).toBe(false);

      const validation = service.validateResolvedOptions(resolved);
      expect(validation.isValid).toBe(false);
      expect(validation.issues).toContain(
        "Selection strategy 'pool-only' is not allowed by feature flags"
      );
    });

    it('should provide correct allowed strategies list', async () => {
      process.env.ALLOW_POOL_ONLY_STRATEGY = 'false';
      process.env.ALLOW_MIXED_STRATEGY = 'false';

      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      const service = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);

      const allowed = service.getAllowedStrategies();
      expect(allowed).toHaveLength(2);
      expect(allowed).toContain(SelectionStrategy.AI_ONLY);
      expect(allowed).toContain(SelectionStrategy.HYBRID);
      expect(allowed).not.toContain(SelectionStrategy.POOL_ONLY);
      expect(allowed).not.toContain(SelectionStrategy.MIXED);
    });
  });

  describe('Real-world Usage Scenarios', () => {
    beforeEach(async () => {
      const module: TestingModule = await Test.createTestingModule({
        imports: [
          ConfigModule.forRoot({
            load: [questionPoolConfig],
            isGlobal: true,
          }),
        ],
        providers: [QuestionPoolConfigService],
      }).compile();

      questionPoolConfigService = module.get<QuestionPoolConfigService>(QuestionPoolConfigService);
    });

    it('should simulate WorksheetGenerateConsumer usage', () => {
      // Simulate how WorksheetGenerateConsumer would use the config service
      const userOptions = {
        selectionStrategy: SelectionStrategy.HYBRID as SelectionStrategy,
        minPoolQuestionsRequired: 15,
      };

      const resolved = questionPoolConfigService.resolveWorksheetGenerationOptions(userOptions);
      const validation = questionPoolConfigService.validateResolvedOptions(resolved);

      expect(validation.isValid).toBe(true);
      expect(resolved.resolvedStrategy).toBe(SelectionStrategy.HYBRID);
      expect(resolved.poolEnabled).toBe(true);
      expect(resolved.minPoolQuestionsRequired).toBe(15);
    });

    it('should simulate QuestionPoolService usage', () => {
      // Simulate how QuestionPoolService would check if it should be used
      const shouldUsePool = questionPoolConfigService.isQuestionPoolEnabled();
      const allowedStrategies = questionPoolConfigService.getAllowedStrategies();
      const minThreshold = questionPoolConfigService.getMinPoolQuestionsThreshold();

      expect(shouldUsePool).toBe(true);
      expect(allowedStrategies).toContain(SelectionStrategy.POOL_ONLY);
      expect(minThreshold).toBe(10);
    });
  });
});
