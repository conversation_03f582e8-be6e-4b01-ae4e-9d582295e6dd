import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuestionPoolService } from './question-pool.service';
import { QuestionPoolConfigService } from './question-pool-config.service';
import { QuestionPoolCacheService } from './services/question-pool-cache.service';
import { QuestionPoolMetricsService } from './services/question-pool-metrics.service';
import { QuestionPoolMetricsController } from './question-pool-metrics.controller';
import { QuestionPoolController } from './question-pool.controller';
import { UserQuestionHistoryService } from './services/user-question-history.service';
import { QuestionPool, QuestionPoolSchema } from '../mongodb/schemas/question-pool.schema';
import { QuestionGeneratorCronService } from './question-generator-cron.service';
import { PromptModule } from '../prompt/prompt.module';
import { BuildPromptModule } from '../build-prompt/build-prompt.module';
import { ValidationModule } from '../validation/validation.module';
import { WorksheetModule } from '../worksheet/worksheet.module';
import { MonitoringModule } from '../monitoring/monitoring.module';
import { AuthModule } from '../auth/auth.module';
import { UserModule } from '../user/user.module';
import { OptionType } from '../options/entities/option-type.entity';
import { OptionValue } from '../options/entities/option-value.entity';
import { Worksheet } from '../worksheet/entities/worksheet.entity';
import { WorksheetQuestionDocument, WorksheetQuestionDocumentSchema } from '../mongodb/schemas/worksheet-question-document.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: QuestionPool.name, schema: QuestionPoolSchema },
    ]),
    TypeOrmModule.forFeature([
      OptionType,
      OptionValue,
      Worksheet,
    ]),
    MongooseModule.forFeature([
      { name: WorksheetQuestionDocument.name, schema: WorksheetQuestionDocumentSchema },
    ]),
    forwardRef(() => PromptModule),
    forwardRef(() => BuildPromptModule),
    forwardRef(() => WorksheetModule),
    forwardRef(() => MonitoringModule),
    forwardRef(() => AuthModule), // Import AuthModule for JwtService and guards
    forwardRef(() => UserModule), // Import UserModule for User entity access
    ValidationModule,
  ],
  controllers: [QuestionPoolMetricsController, QuestionPoolController],
  providers: [
    QuestionPoolService,
    QuestionPoolConfigService,
    QuestionPoolCacheService,
    QuestionPoolMetricsService,
    QuestionGeneratorCronService,
    UserQuestionHistoryService,
  ],
  exports: [
    QuestionPoolService,
    QuestionPoolConfigService,
    QuestionPoolCacheService,
    QuestionPoolMetricsService,
    UserQuestionHistoryService,
  ],
})
export class QuestionPoolModule {}
