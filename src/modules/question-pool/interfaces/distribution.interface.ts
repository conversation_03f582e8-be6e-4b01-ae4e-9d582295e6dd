/**
 * Interfaces for question pool distribution and balancing configuration
 */

/**
 * Difficulty level distribution configuration
 */
export interface DifficultyDistribution {
  Easy: number;    // Percentage (0-1) for Easy questions
  Medium: number;  // Percentage (0-1) for Medium questions
  Advanced: number; // Percentage (0-1) for Advanced questions
}

/**
 * Question type balancing configuration
 */
export interface QuestionTypeBalancing {
  enabled: boolean;
  preferDiversity: boolean; // If true, try to balance question types
  targetDistribution?: Record<string, number>; // Optional specific percentages per type
}

/**
 * Diversity algorithm configuration
 */
export interface DiversityConfig {
  enabled: boolean;
  recencyPenaltyWeight: number; // Weight for penalizing recently selected questions (0-1)
  frequencyPenaltyWeight: number; // Weight for penalizing frequently selected questions (0-1)
  recencyThresholdHours: number; // Hours within which questions are considered "recent"
}

/**
 * Quality validation configuration
 */
export interface QualityValidationConfig {
  enabled: boolean;
  failureHandlingStrategy: 'discard' | 'replace' | 'log_and_proceed';
  maxReplacementAttempts: number;
  minValidationSuccessRate: number; // Minimum percentage of questions that must pass validation
}

/**
 * Fallback strategy configuration
 */
export interface FallbackConfig {
  allowBestEffort: boolean; // Allow returning fewer questions if exact criteria can't be met
  relaxDistributionOnShortfall: boolean; // Relax distribution rules if not enough questions
  logFallbackReasons: boolean; // Log when fallback strategies are triggered
}

/**
 * Complete distribution configuration for question selection
 */
export interface QuestionDistributionConfig {
  difficultyDistribution: DifficultyDistribution;
  questionTypeBalancing: QuestionTypeBalancing;
  diversity: DiversityConfig;
  qualityValidation: QualityValidationConfig;
  fallback: FallbackConfig;
}

/**
 * Parameters for question selection with distribution rules
 */
export interface QuestionSelectionParams {
  // Basic filters
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  type?: string | string[];
  status?: string;
  grade?: string;
  language?: string;
  
  // Distribution configuration
  count: number;
  distributionConfig?: Partial<QuestionDistributionConfig>;
  
  // Override flags
  skipDistribution?: boolean; // Skip distribution enforcement for this request
  skipDiversity?: boolean; // Skip diversity algorithms for this request
  skipValidation?: boolean; // Skip quality validation for this request
}

/**
 * Result of question selection with metadata
 */
export interface QuestionSelectionResult {
  questions: any[]; // Array of selected questions
  metadata: {
    totalRequested: number;
    totalReturned: number;
    distributionAchieved: Record<string, number>; // Actual distribution percentages
    fallbacksTriggered: string[]; // List of fallback strategies that were used
    validationStats: {
      totalValidated: number;
      passed: number;
      failed: number;
      successRate: number;
    };
    selectionTime: number; // Time taken for selection in milliseconds
    // Optional cache-related metadata
    cachedAt?: Date; // When the result was cached
    cacheKey?: string; // Cache key used for this result
  };
}

/**
 * Weighted question item for selection algorithms
 */
export interface WeightedQuestion {
  question: any;
  weight: number; // Selection weight (higher = more likely to be selected)
  reasons: string[]; // Reasons for the assigned weight
}
