import { Injectable, Logger } from '@nestjs/common';
import { collectDefaultMetrics, Histogram, Counter, Gauge, Registry } from 'prom-client';

@Injectable()
export class QuestionPoolMetricsService {
  private readonly logger = new Logger(QuestionPoolMetricsService.name);
  private readonly registry: Registry;

  // Histogram for tracking query execution times
  private readonly queryDurationHistogram: Histogram<string>;

  // Histogram for tracking database query times
  private readonly dbQueryDurationHistogram: Histogram<string>;

  // Counter for cache hits and misses
  private readonly cacheHitCounter: Counter<string>;
  private readonly cacheMissCounter: Counter<string>;

  // Counter for total queries
  private readonly totalQueriesCounter: Counter<string>;

  // Counter for errors
  private readonly errorCounter: Counter<string>;

  // Gauge for questions requested vs returned
  private readonly questionsRequestedGauge: Gauge<string>;
  private readonly questionsReturnedGauge: Gauge<string>;

  // Gauge for active database connections
  private readonly dbConnectionsGauge: Gauge<string>;

  constructor() {
    // Create a new registry for this service to avoid conflicts in testing
    this.registry = new Registry();

    // Collect default Node.js metrics only in production
    if (process.env.NODE_ENV !== 'test') {
      collectDefaultMetrics({ register: this.registry });
    }

    // Initialize custom metrics
    this.queryDurationHistogram = new Histogram({
      name: 'question_pool_query_duration_seconds',
      help: 'Duration of question pool queries in seconds',
      labelNames: ['method', 'cache_status', 'has_distribution', 'has_diversity'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
      registers: [this.registry],
    });

    this.dbQueryDurationHistogram = new Histogram({
      name: 'question_pool_db_query_duration_seconds',
      help: 'Duration of database queries for question pool in seconds',
      labelNames: ['query_type', 'collection'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
      registers: [this.registry],
    });

    this.cacheHitCounter = new Counter({
      name: 'question_pool_cache_hits_total',
      help: 'Total number of cache hits for question pool queries',
      labelNames: ['cache_type'],
      registers: [this.registry],
    });

    this.cacheMissCounter = new Counter({
      name: 'question_pool_cache_misses_total',
      help: 'Total number of cache misses for question pool queries',
      labelNames: ['cache_type'],
      registers: [this.registry],
    });

    this.totalQueriesCounter = new Counter({
      name: 'question_pool_queries_total',
      help: 'Total number of question pool queries',
      labelNames: ['method', 'status'],
      registers: [this.registry],
    });

    this.errorCounter = new Counter({
      name: 'question_pool_errors_total',
      help: 'Total number of errors in question pool operations',
      labelNames: ['operation', 'error_type'],
      registers: [this.registry],
    });

    this.questionsRequestedGauge = new Gauge({
      name: 'question_pool_questions_requested',
      help: 'Number of questions requested in the last query',
      registers: [this.registry],
    });

    this.questionsReturnedGauge = new Gauge({
      name: 'question_pool_questions_returned',
      help: 'Number of questions returned in the last query',
      registers: [this.registry],
    });

    this.dbConnectionsGauge = new Gauge({
      name: 'question_pool_db_connections_active',
      help: 'Number of active database connections',
      registers: [this.registry],
    });

    this.logger.log('Question Pool Metrics Service initialized');
  }

  /**
   * Records the duration of a question pool query
   */
  recordQueryDuration(
    duration: number,
    method: string,
    cacheStatus: 'hit' | 'miss' | 'disabled',
    hasDistribution: boolean = false,
    hasDiversity: boolean = false
  ): void {
    this.queryDurationHistogram
      .labels(method, cacheStatus, hasDistribution.toString(), hasDiversity.toString())
      .observe(duration);
  }

  /**
   * Records the duration of a database query
   */
  recordDbQueryDuration(duration: number, queryType: string, collection: string = 'questions'): void {
    this.dbQueryDurationHistogram
      .labels(queryType, collection)
      .observe(duration);
  }

  /**
   * Records a cache hit
   */
  recordCacheHit(cacheType: string = 'question_pool'): void {
    this.cacheHitCounter.labels(cacheType).inc();
  }

  /**
   * Records a cache miss
   */
  recordCacheMiss(cacheType: string = 'question_pool'): void {
    this.cacheMissCounter.labels(cacheType).inc();
  }

  /**
   * Records a query execution
   */
  recordQuery(method: string, status: 'success' | 'error' | 'partial'): void {
    this.totalQueriesCounter.labels(method, status).inc();
  }

  /**
   * Records an error
   */
  recordError(operation: string, errorType: string): void {
    this.errorCounter.labels(operation, errorType).inc();
  }

  /**
   * Updates the questions requested/returned gauges
   */
  updateQuestionCounts(requested: number, returned: number): void {
    this.questionsRequestedGauge.set(requested);
    this.questionsReturnedGauge.set(returned);
  }

  /**
   * Updates the active database connections gauge
   */
  updateDbConnections(activeConnections: number): void {
    this.dbConnectionsGauge.set(activeConnections);
  }

  /**
   * Gets the Prometheus registry for metrics endpoint
   */
  getRegistry() {
    return this.registry;
  }

  /**
   * Gets current metrics as a string (for Prometheus scraping)
   */
  async getMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  /**
   * Calculates cache hit rate
   */
  async getCacheHitRate(): Promise<number> {
    try {
      const metrics = await this.registry.getMetricsAsJSON();

      const cacheHits = metrics.find(m => m.name === 'question_pool_cache_hits_total');
      const cacheMisses = metrics.find(m => m.name === 'question_pool_cache_misses_total');

      if (!cacheHits || !cacheMisses) {
        return 0;
      }

      const totalHits = cacheHits.values.reduce((sum, v) => sum + (v.value || 0), 0);
      const totalMisses = cacheMisses.values.reduce((sum, v) => sum + (v.value || 0), 0);
      const total = totalHits + totalMisses;

      return total > 0 ? totalHits / total : 0;
    } catch (error) {
      this.logger.error(`Error calculating cache hit rate: ${error.message}`);
      return 0;
    }
  }

  /**
   * Gets performance summary for monitoring dashboard
   */
  async getPerformanceSummary(): Promise<{
    totalQueries: number;
    cacheHitRate: number;
    averageQueryTime: number;
    errorRate: number;
  }> {
    try {
      const metrics = await this.registry.getMetricsAsJSON();
      
      const totalQueriesMetric = metrics.find(m => m.name === 'question_pool_queries_total');
      const queryDurationMetric = metrics.find(m => m.name === 'question_pool_query_duration_seconds');
      const errorsMetric = metrics.find(m => m.name === 'question_pool_errors_total');
      
      const totalQueries = totalQueriesMetric?.values.reduce((sum, v) => sum + (v.value || 0), 0) || 0;
      const totalErrors = errorsMetric?.values.reduce((sum, v) => sum + (v.value || 0), 0) || 0;
      
      // For now, return 0 for average query time
      // In a production system, you would parse the histogram metrics properly
      const averageQueryTime = 0;
      
      const cacheHitRate = await this.getCacheHitRate();
      const errorRate = totalQueries > 0 ? totalErrors / totalQueries : 0;
      
      return {
        totalQueries,
        cacheHitRate,
        averageQueryTime,
        errorRate,
      };
    } catch (error) {
      this.logger.error(`Error getting performance summary: ${error.message}`);
      return {
        totalQueries: 0,
        cacheHitRate: 0,
        averageQueryTime: 0,
        errorRate: 0,
      };
    }
  }

  /**
   * Resets all metrics (useful for testing)
   */
  resetMetrics(): void {
    this.registry.clear();
    this.logger.warn('All metrics have been reset');
  }
}
