import { Body, Controller, HttpCode, Post, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { PromptService } from './services/prompt.service';
import { GenPromptDto } from './dto/genPrompt.dto';
import { Public } from '../auth/decorators/public.decorator';

@ApiTags('Prompt')
@Controller('prompt')
@Public()
export class PromptController {
  constructor(private readonly promptService: PromptService) {}

  @Post('exercise')
  @ApiOperation({ summary: 'Generate exercise based on provided content and requirements' })
  @ApiBody({ type: GenPromptDto })
  @ApiResponse({
    status: 201,
    description: 'Exercise generated successfully',
    schema: {
      type: 'object',
      properties: {
        result: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              question: { type: 'string' },
              options: { type: 'array', items: { type: 'string' } },
              answer: { type: 'string' },
              explanation: { type: 'string' },
              image: { type: 'string' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async genExercise(@Body() body: GenPromptDto) {
    const result = await this.promptService.generatePrompt(body);
    return result;
  }
}
