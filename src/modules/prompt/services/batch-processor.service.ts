import { Injectable, Logger, forwardRef, Inject, BadRequestException } from '@nestjs/common';
import { UserRequest } from '../dto/genPrompt.dto';
import { ExerciseQuestionItem, ExerciseResult } from '../interfaces/exercise-result.interface';
import { BuildPromptService } from '../../build-prompt/build-prompt.service';
import { QuestionGeneratorService } from './question-generator.service';
import { QuestionPoolService } from '../../question-pool/question-pool.service';

/**
 * Service for processing batches of questions
 */
@Injectable()
export class BatchProcessorService {
  private readonly logger = new Logger(BatchProcessorService.name);
  private readonly MAX_QUESTIONS_PER_BATCH = 5;

  constructor(
    @Inject(forwardRef(() => BuildPromptService))
    private buildPromptService: BuildPromptService,
    @Inject(forwardRef(() => QuestionGeneratorService))
    private questionGeneratorService: QuestionGeneratorService,
    @Inject(forwardRef(() => QuestionPoolService))
    private questionPoolService: QuestionPoolService
  ) {}

  /**
   * Processes a request for generating questions, potentially in batches
   * @param contextContent The curriculum context content
   * @param userRequest The user's request parameters
   * @param options Optional processing options
   * @param progressCallback
   * @returns The generated exercise result
   */
  async processBatches(
    contextContent: string,
    userRequest: UserRequest,
    options: { reusePartialResponses?: boolean, narrativeStructure?: string } = { reusePartialResponses: true },
    progressCallback?: (currentCount: number, batchResult: ExerciseResult) => Promise<void>
  ): Promise<ExerciseResult> {
    // Validate custom question count
    if (userRequest.isCustomQuestionCount) {
      if (!Number.isInteger(userRequest.totalQuestions) || userRequest.totalQuestions <= 0) {
        throw new BadRequestException('Custom question count must be a positive integer.');
      }
      if (userRequest.totalQuestions > 100) { // Max limit for custom questions
        throw new BadRequestException('Custom question count cannot exceed 100.');
      }
    }

    const metadataString = this.formatMetadata(userRequest);

    // If the total questions is within the limit, generate in one go
    if (userRequest.totalQuestions <= this.MAX_QUESTIONS_PER_BATCH) {
      const systemPrompt = this.buildPromptService.buildSystemPrompt(
        contextContent,
        metadataString,
        userRequest,
      );
      const userPrompt = this.buildUserPromptWithNarrativeStructure(userRequest, metadataString, options);
      const result = await this.questionGeneratorService.generateQuestions(systemPrompt, userPrompt, userRequest, true);

      // Call the progress callback if provided
      if (progressCallback) {
         progressCallback(result.result.length, result);
      }

      // Save questions to the pool
      try {
        // Add subject information to each question
        const questionsWithSubject = result.result.map(question => ({
          ...question,
          subject: userRequest.topic,
          parentSubject: userRequest.parentSubject,
          childSubject: userRequest.subject
        }));

        // Save to question pool
        await this.questionPoolService.addQuestions(
          questionsWithSubject,
          userRequest.selectedOptions
        );
        this.logger.log(`Saved ${questionsWithSubject.length} questions to the pool`);
      } catch (error) {
        this.logger.error(`Error saving questions to pool: ${error.message}`, error.stack);
        // Continue even if saving to pool fails
      }

      return result;
    }

    // Otherwise, split into batches
    this.logger.log(`Splitting ${userRequest.totalQuestions} questions into batches of ${this.MAX_QUESTIONS_PER_BATCH}`);

    // Calculate the number of batches needed
    let numBatches = Math.ceil(userRequest.totalQuestions / this.MAX_QUESTIONS_PER_BATCH);
    const results: ExerciseQuestionItem[] = [];
    let questionsGenerated = 0;

    // Generate each batch
    for (let i = 0; i < numBatches; i++) {
      // Calculate questions for this batch
      const questionsForBatch = Math.min(
        this.MAX_QUESTIONS_PER_BATCH,
        userRequest.totalQuestions - questionsGenerated
      );

      // Skip this batch if no questions are needed (should not happen, but just in case)
      if (questionsForBatch <= 0) {
        this.logger.warn(`Skipping batch ${i+1} as no more questions are needed. Already generated ${questionsGenerated}/${userRequest.totalQuestions}`);
        continue;
      }

      // Create a modified request for this batch
      const batchRequest = {
        ...userRequest,
        totalQuestions: questionsForBatch
      };

      // Generate batch-specific prompts
      const batchMetadataString = this.formatMetadata(batchRequest);
      const systemPrompt = this.buildPromptService.buildSystemPrompt(
        contextContent,
        batchMetadataString,
        batchRequest,
      );
      const userPrompt = this.buildUserPromptWithNarrativeStructure(batchRequest, batchMetadataString, options);

      try {
        this.logger.log(`Generating batch ${i+1}/${numBatches} with ${questionsForBatch} questions. Progress: ${questionsGenerated}/${userRequest.totalQuestions}`);
        // Log schoolId for debugging
        this.logger.log(`BatchProcessorService: Passing schoolId ${batchRequest.schoolId || 'NOT PROVIDED'} to QuestionGeneratorService for batch ${i+1}`);

        // Generate this batch
        // Only the first batch should skip cache
        const isFirstBatch = i === 0;
        const batchResult = await this.questionGeneratorService.generateQuestions(systemPrompt, userPrompt, batchRequest, isFirstBatch);

        // Add the results to our collection
        if (batchResult && Array.isArray(batchResult.result)) {
          // Log the actual result for debugging
          this.logger.log(`Batch ${i+1} result received with ${batchResult.result.length} questions`);

          results.push(...batchResult.result);
          questionsGenerated += batchResult.result.length;
          this.logger.log(`Batch ${i+1} generated ${batchResult.result.length} questions. Total so far: ${questionsGenerated}/${userRequest.totalQuestions}`);

          // Call the progress callback if provided
          if (progressCallback) {
            const partialResult: ExerciseResult = { result: [...batchResult.result] };
            progressCallback(questionsGenerated, partialResult);
          }

          // Save batch questions to the pool
          try {
            // Add subject information to each question
            const questionsWithSubject = batchResult.result.map(question => ({
              ...question,
              subject: userRequest.topic,
              parentSubject: userRequest.parentSubject,
              childSubject: userRequest.subject
            }));

            // Save to question pool
            await this.questionPoolService.addQuestions(
              questionsWithSubject,
              userRequest.selectedOptions
            );
            this.logger.log(`Saved ${questionsWithSubject.length} questions from batch ${i+1} to the pool`);
          } catch (error) {
            this.logger.error(`Error saving batch ${i+1} questions to pool: ${error.message}`, error.stack);
            // Continue even if saving to pool fails
          }

          // Check if we need to generate more questions
          if (questionsGenerated < userRequest.totalQuestions && i === numBatches - 1) {
            // We've completed all batches but still don't have enough questions
            const remainingQuestions = userRequest.totalQuestions - questionsGenerated;
            this.logger.log(`Still need ${remainingQuestions} more questions. Adding an extra batch.`);

            // Increment the number of batches
            numBatches++;
          }
        } else {
          this.logger.warn(`Batch ${i+1} returned invalid result format. Expected object with result array. Got: ${JSON.stringify(batchResult).substring(0, 200)}...`);
        }
      } catch (error) {
        this.logger.error(`Error generating batch ${i+1}: ${error.message}`, error.stack);
        // Continue with other batches even if one fails
      }
    }

    // Keep trying until we have exactly the requested number of questions
    let maxRetries = 3; // Limit the number of retries to avoid infinite loops
    let retryCount = 0;

    while (results.length < userRequest.totalQuestions && retryCount < maxRetries) {
      this.logger.warn(`Only generated ${results.length} questions out of the requested ${userRequest.totalQuestions}. Retry ${retryCount + 1}/${maxRetries} to generate the remaining questions.`);

      // Calculate how many more questions we need
      const remainingQuestions = userRequest.totalQuestions - results.length;

      // Create a new request for the remaining questions
      // Add a slight variation to the request to avoid getting the same results
      const remainingRequest = {
        ...userRequest,
        totalQuestions: remainingQuestions,
        // Add a retry indicator to help the AI generate different questions
        difficulty: userRequest.difficulty + (retryCount > 0 ? ` (retry ${retryCount})` : '')
      };

      // Generate the remaining questions in a single batch
      const remainingMetadataString = this.formatMetadata(remainingRequest);
      const systemPrompt = this.buildPromptService.buildSystemPrompt(
        contextContent,
        remainingMetadataString,
        remainingRequest,
      );
      const userPrompt = this.buildUserPromptWithNarrativeStructure(remainingRequest, remainingMetadataString, options);

      try {
        this.logger.log(`Generating remaining ${remainingQuestions} questions in batch retry ${retryCount + 1}`);
        // Use cache for retry batches
        const remainingResult = await this.questionGeneratorService.generateQuestions(systemPrompt, userPrompt, remainingRequest, false);

        if (remainingResult && Array.isArray(remainingResult.result) && remainingResult.result.length > 0) {
          this.logger.log(`Retry batch ${retryCount + 1} generated ${remainingResult.result.length} additional questions`);
          results.push(...remainingResult.result);
          questionsGenerated += remainingResult.result.length;

          // Call the progress callback if provided
          if (progressCallback) {
            const partialResult: ExerciseResult = { result: [...remainingResult.result] };
            progressCallback(questionsGenerated, partialResult);
          }
        } else {
          this.logger.warn(`Retry batch ${retryCount + 1} failed to generate any questions`);
        }
      } catch (error) {
        this.logger.error(`Error generating retry batch ${retryCount + 1}: ${error.message}`, error.stack);
      }

      retryCount++;
    }

    // If we still don't have enough questions after all retries, duplicate existing questions to meet the requirement
    if (results.length < userRequest.totalQuestions) {
      this.logger.warn(`After ${maxRetries} retries, still only have ${results.length} questions. Duplicating existing questions to meet the requirement of ${userRequest.totalQuestions}.`);

      // Duplicate existing questions until we have enough
      const originalResults = [...results];
      while (results.length < userRequest.totalQuestions) {
        // Get a random question from the original set
        const randomIndex = Math.floor(Math.random() * originalResults.length);
        const questionToDuplicate = originalResults[randomIndex];

        // Create a slightly modified copy
        const modifiedQuestion = {
          ...questionToDuplicate,
          // Add a note that this is a duplicate (in a way that won't be visible to users)
          content: questionToDuplicate.content + ' ' // Add a space to make it slightly different
        };

        results.push(modifiedQuestion);
      }

      // Update the progress with the duplicated questions
      questionsGenerated = results.length;
      if (progressCallback) {
        const partialResult: ExerciseResult = {
          result: results.slice(originalResults.length) // Just the newly added questions
        };
         progressCallback(questionsGenerated, partialResult);
      }
    }

    // Ensure we have exactly the requested number of questions
    if (results.length > userRequest.totalQuestions) {
      this.logger.warn(`Generated ${results.length} questions, which is more than the requested ${userRequest.totalQuestions}. Trimming excess questions.`);
      // Trim the results to the exact number requested
      const trimmedResults = results.slice(0, userRequest.totalQuestions);
      // Clear the original array and add back only the trimmed items
      results.length = 0;
      results.push(...trimmedResults);

      // Update the questions generated count to match the actual number
      questionsGenerated = results.length;
      this.logger.log(`After trimming, final question count: ${questionsGenerated}/${userRequest.totalQuestions}`);
    }

    this.logger.log(`Final result: ${results.length} questions out of requested ${userRequest.totalQuestions}`);

    // Return the combined results as an ExerciseResult
    return {
      result: results
    } as ExerciseResult;
  }

  /**
   * Processes a request for generating questions in parallel
   * @param contextContent The curriculum context content
   * @param userRequest The user's request parameters
   * @param parallelBatches Number of parallel batches to run
   * @param progressCallback
   * @returns The generated exercise result
   */
  async processBatchesInParallel(
    contextContent: string,
    userRequest: UserRequest,
    parallelBatches: number = 2,
    progressCallback?: (currentCount: number, batchResult: ExerciseResult) => Promise<void>,
    options?: { narrativeStructure?: string }
  ): Promise<ExerciseResult> {
    // Validate custom question count
    if (userRequest.isCustomQuestionCount) {
      if (!Number.isInteger(userRequest.totalQuestions) || userRequest.totalQuestions <= 0) {
        throw new BadRequestException('Custom question count must be a positive integer.');
      }
      if (userRequest.totalQuestions > 100) { // Max limit for custom questions
        throw new BadRequestException('Custom question count cannot exceed 100.');
      }
    }

    this.logger.log(`Starting parallel question generation with ${parallelBatches} processes for ${userRequest.totalQuestions} questions`);

    // Calculate questions per process
    const questionsPerProcess = Math.ceil(userRequest.totalQuestions / parallelBatches);

    // Create multiple batch processes
    const batchPromises: Promise<ExerciseResult>[] = [];
    for (let i = 0; i < parallelBatches; i++) {
      const batchRequest = {
        ...userRequest,
        totalQuestions: Math.min(
          questionsPerProcess,
          userRequest.totalQuestions - (i * questionsPerProcess)
        )
      };

      // Skip if no questions needed for this batch
      if (batchRequest.totalQuestions <= 0) continue;

      this.logger.log(`Creating parallel batch ${i+1} with ${batchRequest.totalQuestions} questions`);
      // Log schoolId for debugging in parallel processing
      this.logger.log(`BatchProcessorService: Parallel batch ${i+1} using schoolId ${batchRequest.schoolId || 'NOT PROVIDED'}`);

      // Add to promises
      batchPromises.push(this.processBatches(contextContent, batchRequest, { reusePartialResponses: true }, progressCallback));
    }

    // Wait for all batches to complete
    const batchResults = await Promise.all(batchPromises);

    // Combine results
    const combinedResults: ExerciseQuestionItem[] = [];
    let totalProcessed = 0;

    for (const result of batchResults) {
      if (result && Array.isArray(result.result)) {
        this.logger.log(`Adding ${result.result.length} questions from parallel batch`);
        combinedResults.push(...result.result);
        totalProcessed += result.result.length;

        // Call progress callback with combined results so far
        if (progressCallback) {
          const partialResult: ExerciseResult = {
            result: result.result // Just send this batch's results
          };
          progressCallback(totalProcessed, partialResult);
        }
      }
    }

    // Keep trying until we have exactly the requested number of questions
    let maxRetries = 3; // Limit the number of retries to avoid infinite loops
    let retryCount = 0;

    while (combinedResults.length < userRequest.totalQuestions && retryCount < maxRetries) {
      this.logger.warn(`Parallel processing generated only ${combinedResults.length} questions out of requested ${userRequest.totalQuestions}. Retry ${retryCount + 1}/${maxRetries} to generate the remaining questions.`);

      // Calculate how many more questions we need
      const remainingQuestions = userRequest.totalQuestions - combinedResults.length;

      // Create a new request for the remaining questions with slight variation
      const remainingRequest = {
        ...userRequest,
        totalQuestions: remainingQuestions,
        // Add a retry indicator to help the AI generate different questions
        difficulty: userRequest.difficulty + (retryCount > 0 ? ` (retry ${retryCount})` : '')
      };

      // Generate the remaining questions in a single batch
      const remainingMetadataString = this.formatMetadata(remainingRequest);
      const systemPrompt = this.buildPromptService.buildSystemPrompt(
        contextContent,
        remainingMetadataString,
        remainingRequest,
      );
      const userPrompt = this.buildUserPromptWithNarrativeStructure(remainingRequest, remainingMetadataString, options);

      try {
        this.logger.log(`Generating remaining ${remainingQuestions} questions in parallel retry ${retryCount + 1}`);
        // Use cache for retry batches
        const remainingResult = await this.questionGeneratorService.generateQuestions(systemPrompt, userPrompt, remainingRequest, false);

        if (remainingResult && Array.isArray(remainingResult.result) && remainingResult.result.length > 0) {
          this.logger.log(`Parallel retry ${retryCount + 1} generated ${remainingResult.result.length} additional questions`);
          combinedResults.push(...remainingResult.result);
          totalProcessed += remainingResult.result.length;

          // Call progress callback with the remaining results
          if (progressCallback) {
            const partialResult: ExerciseResult = {
              result: remainingResult.result
            };
             progressCallback(totalProcessed, partialResult);
          }
        } else {
          this.logger.warn(`Parallel retry ${retryCount + 1} failed to generate any questions`);
        }
      } catch (error) {
        this.logger.error(`Error generating parallel retry ${retryCount + 1}: ${error.message}`, error.stack);
      }

      retryCount++;
    }

    // If we still don't have enough questions after all retries, duplicate existing questions to meet the requirement
    if (combinedResults.length < userRequest.totalQuestions) {
      this.logger.warn(`After ${maxRetries} parallel retries, still only have ${combinedResults.length} questions. Duplicating existing questions to meet the requirement of ${userRequest.totalQuestions}.`);

      // Duplicate existing questions until we have enough
      const originalResults = [...combinedResults];
      while (combinedResults.length < userRequest.totalQuestions) {
        // Get a random question from the original set
        const randomIndex = Math.floor(Math.random() * originalResults.length);
        const questionToDuplicate = originalResults[randomIndex];

        // Create a slightly modified copy
        const modifiedQuestion = {
          ...questionToDuplicate,
          // Add a note that this is a duplicate (in a way that won't be visible to users)
          content: questionToDuplicate.content + ' ' // Add a space to make it slightly different
        };

        combinedResults.push(modifiedQuestion);
        totalProcessed++;
      }

      // Update the progress with the duplicated questions
      if (progressCallback) {
        const partialResult: ExerciseResult = {
          result: combinedResults.slice(originalResults.length) // Just the newly added questions
        };
        progressCallback(totalProcessed, partialResult);
      }
    }

    // Trim to requested number if we got more
    if (combinedResults.length > userRequest.totalQuestions) {
      this.logger.warn(`Parallel processing generated ${combinedResults.length} questions, which is more than the requested ${userRequest.totalQuestions}. Trimming excess questions.`);
      // Trim the results to the exact number requested
      const trimmedResults = combinedResults.slice(0, userRequest.totalQuestions);
      // Clear the original array and add back only the trimmed items
      combinedResults.length = 0;
      combinedResults.push(...trimmedResults);

      // Update the total processed count to match the actual number
      totalProcessed = combinedResults.length;
      this.logger.log(`After trimming parallel results, final question count: ${totalProcessed}/${userRequest.totalQuestions}`);

      // Notify about the trimmed questions via callback
      if (progressCallback) {
        progressCallback(totalProcessed, { result: [] });
      }
    }

    this.logger.log(`Parallel processing complete. Generated ${combinedResults.length} questions out of requested ${userRequest.totalQuestions}`);

    return {
      result: combinedResults
    } as ExerciseResult;
  }

  /**
   * Wrapper for buildUserPrompt that passes the narrative structure
   * @param userRequest The user's request parameters
   * @param metadataString Formatted metadata about the exercise requirements
   * @param options Optional processing options
   * @returns The user prompt
   */
  private buildUserPromptWithNarrativeStructure(
    userRequest: UserRequest, 
    metadataString: string, 
    options?: { narrativeStructure?: string }
  ): string {
    return this.buildPromptService.buildUserPrompt(
      userRequest, 
      metadataString, 
      options?.narrativeStructure
    );
  }

  private formatMetadata(userRequest: UserRequest): string {
    let metadata = `
            Total Number of Questions: ${userRequest.totalQuestions}
            Grade Level: ${userRequest.grade}
            Topic: ${userRequest.topic}
            Difficulty: ${userRequest.difficulty}
            Exercise Type: ${userRequest.exerciseType.join(' and ')}
        `;

    // Add subject data if available
    if (userRequest.subjectData && userRequest.subjectData.length > 0) {
      metadata += '\nSubject Data:';
      for (const subject of userRequest.subjectData) {
        metadata += `\n  - ${subject.label}:`;
        for (const item of subject.items) {
          metadata += `\n    * ${item}`;
        }
      }
    }

    // Add exercise type distribution if available
    if (userRequest.exerciseTypeDistribution) {
      metadata += '\nExercise Type Distribution:';
      for (const [type, count] of Object.entries(userRequest.exerciseTypeDistribution)) {
        metadata += `\n  - ${type}: ${count} questions`;
      }
    }

    return metadata.trim();
  }
}
