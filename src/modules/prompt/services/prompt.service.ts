import { Injectable, Logger } from '@nestjs/common';
import { GenPromptDto } from '../dto/genPrompt.dto';
import { ExerciseResult, ExerciseQuestionItem } from '../interfaces/exercise-result.interface';
import { BatchProcessorService } from './batch-processor.service';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetPromptResult } from '../../mongodb/schemas/worksheet-prompt-result.schema';
import { SocketGateway } from '../../socket/socket.gateway';
import { NarrativeStructureService } from '../../school/services/narrative-structure.service';

/**
 * Service for generating exercise prompts
 */
@Injectable()
export class PromptService {
  private readonly logger = new Logger(PromptService.name);

  constructor(
    private batchProcessorService: BatchProcessorService,
    private configService: ConfigService,
    @InjectModel(WorksheetPromptResult.name)
    private worksheetPromptResultModel: Model<WorksheetPromptResult>,
    private socketGateway: SocketGateway,
    private narrativeStructureService: NarrativeStructureService
  ) {}

  /**
   * Generate exercise prompts, potentially in batches for larger question counts
   * @param relevantContent Relevant curriculum content
   * @param userRequest User's request parameters
   * @param worksheetId
   * @returns Generated exercise content
   */
  async generatePrompt({
    relevantContent,
    userRequest,
    worksheetId,
  }: GenPromptDto): Promise<ExerciseResult> {
    try {
      // Format the context content
      const contextContent = this.formatContextContent(relevantContent);

      // Fetch narrative structure if schoolId is provided
      let narrativeStructure: string | undefined;
      if (userRequest.schoolId) {
        try {
          const narrativeStructureEntity = await this.narrativeStructureService.findBySchoolId(userRequest.schoolId);
          if (narrativeStructureEntity) {
            narrativeStructure = narrativeStructureEntity.content;
            this.logger.log(`Found narrative structure for school ${userRequest.schoolId}`);
          } else {
            this.logger.log(`No narrative structure found for school ${userRequest.schoolId}`);
          }
        } catch (error) {
          this.logger.warn(`Error fetching narrative structure for school ${userRequest.schoolId}: ${error.message}`);
          // Continue without narrative structure
        }
      }

      // Check if parallel processing is enabled
      const useParallelProcessing = this.configService.get<string>('USE_PARALLEL_PROCESSING') === 'true';
      const parallelBatches = parseInt(this.configService.get<string>('PARALLEL_BATCHES') || '2', 10);
      const parallelThreshold = parseInt(this.configService.get<string>('PARALLEL_THRESHOLD') || '2', 10);

      // Create a callback function to update progress if worksheetId is provided
      const progressCallback = worksheetId ?
        async (currentCount: number, batchResult: ExerciseResult) => {
          try {
            // Update the MongoDB record with the current progress
            const promptResult = await this.worksheetPromptResultModel.findOne({ worksheetId });

            if (promptResult) {
              // Update the current count and merge the results
              const updatedResult = {
                result: [...(promptResult.promptResult?.result || []), ...batchResult.result]
              };

              this.logger.log(`Updating MongoDB for worksheet ${worksheetId}: Setting currentQuestionCount to ${currentCount}`);

              // Update the MongoDB record with explicit currentQuestionCount
              const updateResult = await this.worksheetPromptResultModel.findOneAndUpdate(
                { worksheetId },
                {
                  promptResult: updatedResult,
                  currentQuestionCount: currentCount
                },
                { new: true } // Return the updated document
              );

              if (updateResult) {
                this.logger.log(`MongoDB update successful. New currentQuestionCount: ${updateResult.currentQuestionCount}`);
              } else {
                this.logger.warn(`MongoDB update did not return updated document. Current count should be: ${currentCount}`);
              }

              // Emit the progress update via socket
              this.socketGateway.emitWorksheetProgress(
                worksheetId,
                currentCount,
                promptResult.totalQuestionCount,
                batchResult // Send the latest batch of questions
              );

              this.logger.log(`Updated progress for worksheet ${worksheetId}: ${currentCount}/${promptResult.totalQuestionCount}`);
            }
          } catch (error) {
            this.logger.error(`Error updating progress: ${error.message}`, error.stack);
          }
        } : undefined;

      let result: ExerciseResult;

      if (useParallelProcessing && userRequest.totalQuestions > parallelThreshold) {
        this.logger.log(`Using parallel processing with ${parallelBatches} batches for ${userRequest.totalQuestions} questions`);
        result = await this.batchProcessorService.processBatchesInParallel(
          contextContent,
          userRequest,
          parallelBatches,
          progressCallback,
          { narrativeStructure }
        );
      } else {
        // Use regular batch processing
        this.logger.log(`Using regular batch processing for ${userRequest.totalQuestions} questions`);
        result = await this.batchProcessorService.processBatches(
          contextContent,
          userRequest,
          { reusePartialResponses: true, narrativeStructure },
          progressCallback
        );
      }

      // Final update if worksheetId is provided
      if (worksheetId) {
        try {
          const promptResult = await this.worksheetPromptResultModel.findOne({ worksheetId });
          if (promptResult) {
            // Verify we have exactly the requested number of questions
            const actualQuestionCount = result.result.length;
            const expectedCount = promptResult.totalQuestionCount;

            if (actualQuestionCount !== expectedCount) {
              this.logger.warn(`Final result contains ${actualQuestionCount} questions, but expected exactly ${expectedCount}. Adjusting to match the requirement.`);

              // Ensure we have exactly the right number of questions
              if (actualQuestionCount < expectedCount) {
                // If we have too few, duplicate some existing questions
                const originalResults = [...result.result];
                while (result.result.length < expectedCount) {
                  // Get a random question from the original set
                  const randomIndex = Math.floor(Math.random() * originalResults.length);
                  const questionToDuplicate = originalResults[randomIndex];

                  // Create a slightly modified copy
                  const modifiedQuestion = {
                    ...questionToDuplicate,
                    // Add a note that this is a duplicate (in a way that won't be visible to users)
                    content: questionToDuplicate.content + ' ' // Add a space to make it slightly different
                  };

                  result.result.push(modifiedQuestion);
                }
                this.logger.log(`Added ${expectedCount - actualQuestionCount} duplicate questions to meet the requirement`);
              } else {
                // If we have too many, trim the excess
                result.result = result.result.slice(0, expectedCount);
                this.logger.log(`Trimmed ${actualQuestionCount - expectedCount} excess questions to meet the requirement`);
              }
            }

            // Now we should have exactly the right number of questions
            // Ensure we have exactly the expected count before saving
            if (result.result.length !== expectedCount) {
              this.logger.error(`Question count mismatch after adjustment: ${result.result.length} vs expected ${expectedCount}. Forcing correction.`);
              if (result.result.length > expectedCount) {
                result.result = result.result.slice(0, expectedCount);
              } else {
                // This should not happen, but just in case
                while (result.result.length < expectedCount) {
                  const randomIndex = Math.floor(Math.random() * result.result.length);
                  const questionToDuplicate = result.result[randomIndex];
                  result.result.push({
                    ...questionToDuplicate,
                    content: questionToDuplicate.content + ' '
                  });
                }
              }
            }

            // Verify one more time
            this.logger.log(`Final verification: ${result.result.length}/${expectedCount} questions`);

            // Save the final result
            await this.worksheetPromptResultModel.findOneAndUpdate(
              { worksheetId },
              {
                promptResult: result,
                currentQuestionCount: expectedCount // Always use the expected count
              }
            );

            // Final progress update with the expected count
            this.socketGateway.emitWorksheetProgress(
              worksheetId,
              expectedCount,
              expectedCount,
              undefined // No new questions to send
            );

            this.logger.log(`Final update for worksheet ${worksheetId}: ${expectedCount}/${expectedCount} questions generated (100% complete)`);
          }
        } catch (error) {
          this.logger.error(`Error in final progress update: ${error.message}`, error.stack);
        }
      }

      return result;
    } catch (error) {
      this.logger.error(`Error in generatePrompt: ${error.message}`, error.stack);

      // Even in error case, try to return the requested number of questions
      if (worksheetId) {
        try {
          const promptResult = await this.worksheetPromptResultModel.findOne({ worksheetId });
          if (promptResult) {
            const expectedCount = promptResult.totalQuestionCount;

            // Create placeholder questions to meet the requirement
            const placeholderQuestions: ExerciseQuestionItem[] = [];
            for (let i = 0; i < expectedCount; i++) {
              placeholderQuestions.push({
                type: 'Multiple Choice',
                content: `Question ${i+1}: An error occurred while generating this question. Please try again.`,
                options: ['Option A', 'Option B', 'Option C', 'Option D'],
                answer: ['Option A'],
                explain: 'This is a placeholder question due to an error in generation.',
                image: ''
              });
            }

            const errorResult = {
              result: placeholderQuestions,
              error: error.message
            } as ExerciseResult;

            // Update MongoDB with the placeholder questions
            await this.worksheetPromptResultModel.findOneAndUpdate(
              { worksheetId },
              {
                promptResult: errorResult,
                currentQuestionCount: expectedCount
              }
            );

            // Final progress update
            this.socketGateway.emitWorksheetProgress(
              worksheetId,
              expectedCount,
              expectedCount,
              undefined
            );

            this.logger.log(`Error case: Created ${expectedCount} placeholder questions for worksheet ${worksheetId}`);

            return errorResult;
          }
        } catch (innerError) {
          this.logger.error(`Error creating placeholder questions: ${innerError.message}`, innerError.stack);
        }
      }

      // If all else fails, return an empty result
      return {
        result: [],
        error: error.message
      } as ExerciseResult;
    }
  }

  /**
   * Formats the context content for the prompt
   * @param relevantContent The relevant content to format
   * @returns The formatted context content
   */
  private formatContextContent(relevantContent: string[]): string {
    return relevantContent.filter(Boolean).join('\n\n');
  }
}
