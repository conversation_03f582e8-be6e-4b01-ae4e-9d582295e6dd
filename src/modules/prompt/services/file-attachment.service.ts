import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { FilesService } from '../../files/files.service';
import { FileMetadata } from '../../files/interfaces/file.interface';
import { UserRequest } from '../dto/genPrompt.dto';

/**
 * Service for handling file attachments
 */
@Injectable()
export class FileAttachmentService {
  private readonly logger = new Logger(FileAttachmentService.name);

  constructor(
    @Inject(forwardRef(() => FilesService))
    private filesService: FilesService
  ) {}

  /**
   * Gets a relevant file for attachment based on userRequest's grade and topic
   * @param userRequest The user request containing grade and topic
   * @returns The file metadata if found, null otherwise
   */
  async getRelevantFileForAttachment(userRequest: UserRequest): Promise<FileMetadata | null> {
    try {
      this.logger.log(`Attempting to find relevant file for grade: ${userRequest.grade}, topic: ${userRequest.topic}`);

      // Query files with tags matching grade and topic
      const queryResult = await this.filesService.getFiles({
        tags: [userRequest.grade, userRequest.topic],
        limit: 100, // Get a reasonable number of files to choose from
      });

      // If no files found, return null
      if (queryResult.total === 0 || !queryResult.files.length) {
        this.logger.log(`No files found for grade ${userRequest.grade} and topic ${userRequest.topic}`);
        return null;
      }

      // Randomly select one file from the results
      const randomIndex = Math.floor(Math.random() * queryResult.files.length);
      const selectedFile = queryResult.files[randomIndex];

      this.logger.log(`Selected file for attachment: ${selectedFile.filename}`);
      return selectedFile;
    } catch (error) {
      this.logger.error(`Error getting relevant file for attachment: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Gets a file buffer
   * @param fileId The ID of the file
   * @returns The file buffer
   */
  async getFileBuffer(fileId: string): Promise<Buffer> {
    return await this.filesService.getFileBuffer(fileId);
  }

  /**
   * Encodes a PDF file to base64 (deprecated - use getFileBuffer instead)
   * @param fileId The ID of the file to encode
   * @returns The base64-encoded PDF
   * @deprecated Use getFileBuffer instead
   */
  async encodePDFToBase64(fileId: string): Promise<string> {
    this.logger.warn('encodePDFToBase64 is deprecated. Use getFileBuffer instead.');
    const fileBuffer = await this.getFileBuffer(fileId);
    return fileBuffer.toString('base64');
  }
}
