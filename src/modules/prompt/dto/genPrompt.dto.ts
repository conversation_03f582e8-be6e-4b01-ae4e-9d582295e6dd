import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Subject data structure for the new payload format
 */
export class SubjectDataItem {
  @ApiProperty({
    description: 'Label of the subject',
    example: 'Speed',
  })
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Items within the subject',
    example: ['Speed Conversion', 'Time, Distance and Speed Relationship'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  items: string[];
}

/**
 * UserRequest DTO for generating educational content
 *
 * IMPORTANT: Subject Hierarchy Structure
 * The naming of the subject hierarchy fields is counterintuitive but maintained for backward compatibility:
 *
 * 1. topic: Main subject area (e.g., "Mathematics", "Science", "English")
 * 2. parentSubject: Chapter or unit (e.g., "Fractions", "Algebra", "Grammar")
 * 3. subject: Specific lesson content (e.g., "Dividing a whole number by a proper fraction")
 *
 * When using these fields, remember their actual hierarchical relationship:
 * Main Subject Area (topic) > Chapter/Unit (parentSubject) > Specific Lesson (subject)
 */
export class UserRequest {
  @ApiProperty({
    description: 'Grade level of the student',
    example: 'Primary 3',
  })
  @IsString()
  grade: string;

  @ApiProperty({
    description: 'School ID for school-specific examination formats',
    example: 'school-123',
    required: false,
  })
  @IsString()
  @IsOptional()
  schoolId?: string;

  @ApiProperty({
    description: 'Main subject area (e.g., Mathematics, Science, English)',
    example: 'Mathematics',
    required: true,
  })
  @IsString()
  topic: string;

  @ApiProperty({
    description: 'Specific lesson content or sub-topic (e.g., "Dividing a whole number by a proper fraction")',
    example: 'Dividing a whole number by a proper fraction',
    required: false,
  })
  @IsString()
  @IsOptional()
  subject?: string;

  @ApiProperty({
    description: 'Chapter or unit (e.g., "Fractions", "Algebra")',
    example: 'Fractions',
    required: false,
  })
  @IsString()
  @IsOptional()
  parentSubject?: string;

  @ApiProperty({
    description: 'Structured subject data with labels and items',
    type: [SubjectDataItem],
    required: false,
  })
  @IsArray()
  @IsOptional()
  subjectData?: SubjectDataItem[];

  @ApiProperty({
    description: 'Difficulty level of the exercise',
    example: 'Medium',
    enum: ['Easy', 'Medium', 'Hard'],
  })
  @IsString()
  difficulty: string;

  @ApiProperty({
    description: 'Total number of questions in the exercise',
    example: 10,
    minimum: 1,
  })
  @IsNumber()
  totalQuestions: number;

  @ApiProperty({
    description: 'Indicates if a custom question count was used',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isCustomQuestionCount?: boolean;

  @ApiProperty({
    description: 'Types of exercises to generate',
    example: ['Multiple Choice', 'Open-ended'],
    isArray: true,
  })
  @IsArray()
  @IsString({ each: true })
  exerciseType: string[];

  @ApiProperty({
    description: 'Distribution of questions by exercise type',
    example: { 'Multiple Choice': 5, 'Open-ended': 3 },
    required: false,
  })
  @IsObject()
  @IsOptional()
  exerciseTypeDistribution?: Record<string, number>;

  @ApiProperty({
    description: 'Whether to include images in the questions',
    example: false,
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  includeImages: boolean = false;

  @ApiProperty({
    description: 'Selected options from the worksheet',
    required: false,
  })
  @IsObject()
  @IsOptional()
  selectedOptions?: Record<string, any>;

  @ApiProperty({
    description: 'ID of the worksheet, if applicable',
    example: 'worksheet-xyz-123',
    required: false,
  })
  @IsString()
  @IsOptional()
  worksheetId?: string;
}

export class GenPromptDto {
  @ApiProperty({
    description: 'Relevant content to use for generating the exercise',
    example: ['Content about fractions', 'Content about decimals'],
    isArray: true,
  })
  @IsArray()
  @IsString({ each: true })
  relevantContent: string[];

  @ApiProperty({
    description: 'User request details for the exercise',
    type: UserRequest,
  })
  @IsObject()
  userRequest: UserRequest;

  @ApiProperty({
    description: 'ID of the worksheet for tracking progress',
    required: false,
  })
  @IsString()
  @IsOptional()
  worksheetId?: string;
}
