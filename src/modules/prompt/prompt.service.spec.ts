import { Test, TestingModule } from '@nestjs/testing';
import { PromptService } from './services/prompt.service';
import { BatchProcessorService } from './services/batch-processor.service';
import { ConfigService } from '@nestjs/config';

describe('PromptService', () => {
  let service: PromptService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PromptService,
        {
          provide: BatchProcessorService,
          useValue: {
            processBatches: jest.fn().mockResolvedValue({ result: [] }),
            processBatchesInParallel: jest.fn().mockResolvedValue({ result: [] }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key) => {
              if (key === 'USE_PARALLEL_PROCESSING') return 'false';
              if (key === 'PARALLEL_BATCHES') return '2';
              return null;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<PromptService>(PromptService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
