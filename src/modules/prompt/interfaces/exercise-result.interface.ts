/**
 * Interface for a single exercise question item
 */
export interface ExerciseQuestionItem {
  type: string;
  content: string;
  options?: string[];
  answer?: string[];
  imagePrompt?: string;
  explain?: string;
  image?: string;
  subject?: string; // Parent subject category the question belongs to
  parentSubject?: string; // Subject type parent (e.g., Algebra, Geometry)
  childSubject?: string; // Subject type child (e.g., Linear Equations, Triangles)
  [key: string]: any; // Allow for additional properties
}

/**
 * Interface for the complete exercise result
 */
export interface ExerciseResult {
  result: ExerciseQuestionItem[];
}
