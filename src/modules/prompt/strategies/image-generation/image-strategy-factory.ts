import { Injectable } from '@nestjs/common';
import { ImageGenerationStrategy } from './image-generation-strategy.interface';
import { NoImageStrategy } from './no-image-strategy';
import { DetailedImageStrategy } from './detailed-image-strategy';

/**
 * Factory for creating image generation strategies
 */
@Injectable()
export class ImageStrategyFactory {
  constructor(
    private noImageStrategy: NoImageStrategy,
    private detailedImageStrategy: DetailedImageStrategy
  ) {}

  /**
   * Creates an appropriate image generation strategy based on the image prompt
   * @param imagePrompt The image prompt from the question
   * @param questionContent The content of the question
   * @param topic Optional topic for determining if an image is necessary
   * @returns The appropriate image generation strategy
   */
  createStrategy(imagePrompt: string, questionContent: string, topic?: string): ImageGenerationStrategy {
    // First check if no image is required
    if (!this.noImageStrategy.shouldGenerateImage(imagePrompt, questionContent, topic)) {
      return this.noImageStrategy;
    }

    // Otherwise use the detailed image strategy
    return this.detailedImageStrategy;
  }
}
