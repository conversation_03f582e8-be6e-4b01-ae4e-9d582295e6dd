import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { ImageGenerationStrategy } from './image-generation-strategy.interface';
import { GenImageService } from '../../../gen-image/gen-image.service';
import { BuildPromptService } from '../../../build-prompt/build-prompt.service';

/**
 * Strategy for generating detailed images based on question content
 */
@Injectable()
export class DetailedImageStrategy implements ImageGenerationStrategy {
  private readonly logger = new Logger(DetailedImageStrategy.name);

  constructor(
    @Inject(forwardRef(() => GenImageService))
    private genImageService: GenImageService,
    @Inject(forwardRef(() => BuildPromptService))
    private buildPromptService: BuildPromptService
  ) {}

  /**
   * Determines if an image should be generated based on the prompt and topic
   * @param imagePrompt The image prompt from the question
   * @param questionContent The content of the question
   * @param topic Optional topic for determining if an image is necessary
   * @returns True if an image should be generated, false otherwise
   */
  shouldGenerateImage(imagePrompt: string, questionContent: string, topic?: string): boolean {
    // Check if the image prompt explicitly says no image is required
    if (imagePrompt && (
      imagePrompt.toLowerCase().includes('no image') ||
      imagePrompt.toLowerCase().includes('no diagram') ||
      imagePrompt.toLowerCase().includes('no illustration') ||
      imagePrompt.toLowerCase().includes('not required')
    )) {
      this.logger.log(`Skipping image generation as prompt indicates no image is needed: "${imagePrompt}"`);
      return false;
    }

    // Check if the image prompt is detailed enough (contains measurements and is not too short)
    const hasDetailedPrompt = imagePrompt.length > 20 &&
      (imagePrompt.includes('m') || imagePrompt.includes('cm') ||
       imagePrompt.includes('km') || imagePrompt.includes('mm') ||
       imagePrompt.includes('square') || imagePrompt.includes('angle') ||
       imagePrompt.includes('degree') || imagePrompt.includes('triangle') ||
       imagePrompt.includes('circle') || imagePrompt.includes('rectangle') ||
       imagePrompt.includes('graph') || imagePrompt.includes('chart'));

    if (!hasDetailedPrompt && imagePrompt) {
      this.logger.warn(`Using question content instead of image prompt because the prompt lacks detail: "${imagePrompt}"`);
    }

    // Determine if an SVG is necessary based on the topic
    if (topic) {
      const normalizedTopic = topic.toLowerCase();

      // Topics that typically benefit from visual representations
      const visualTopics = [
        'mathematics', 'math', 'geometry', 'algebra', 'calculus', 'trigonometry',
        'science', 'physics', 'chemistry', 'biology', 'astronomy',
        'geography', 'maps', 'social studies',
        'art', 'visual arts', 'design',
        'engineering', 'architecture',
        'statistics', 'data'
      ];

      // Check if the topic is in the list of visual topics
      const needsVisual = visualTopics.some(visualTopic => 
        normalizedTopic.includes(visualTopic)
      );

      if (!needsVisual) {
        // Check if the content contains elements that would benefit from visualization
        const contentNeedsVisual = 
          questionContent.toLowerCase().includes('diagram') ||
          questionContent.toLowerCase().includes('graph') ||
          questionContent.toLowerCase().includes('chart') ||
          questionContent.toLowerCase().includes('figure') ||
          questionContent.toLowerCase().includes('plot') ||
          questionContent.toLowerCase().includes('map') ||
          questionContent.toLowerCase().includes('draw') ||
          questionContent.toLowerCase().includes('illustrate') ||
          questionContent.toLowerCase().includes('visual');

        if (!contentNeedsVisual) {
          this.logger.log(`Skipping image generation for topic "${topic}" as it doesn't typically require visualization`);
          return false;
        }
      }
    }

    // Check if the content actually needs visualization
    const hasVisualElements = 
      imagePrompt.includes('m') || imagePrompt.includes('cm') ||
      imagePrompt.includes('km') || imagePrompt.includes('mm') ||
      imagePrompt.includes('square') || imagePrompt.includes('angle') ||
      imagePrompt.includes('degree') || imagePrompt.includes('triangle') ||
      imagePrompt.includes('circle') || imagePrompt.includes('rectangle') ||
      imagePrompt.includes('graph') || imagePrompt.includes('chart') ||
      imagePrompt.includes('diagram') || imagePrompt.includes('figure') ||
      imagePrompt.includes('map') || imagePrompt.includes('draw') ||
      imagePrompt.includes('illustrate') || imagePrompt.includes('visual');

    if (!hasVisualElements) {
      this.logger.log(`Skipping image generation as the content doesn't contain visual elements`);
      return false;
    }

    return true;
  }

  /**
   * Generates an image based on the provided content
   * @param imageContent The content to use for image generation
   * @param topic Optional topic for specialized image generation
   * @returns The generated image as a string (SVG)
   */
  async generateImage(imageContent: string, topic?: string): Promise<string> {
    if (!imageContent || imageContent.trim() === '') {
      return '';
    }

    // Clean up the image content to remove any "no overlap" instructions that might be in the prompt
    // as these will be handled by our SVG generation system
    const cleanedContent = imageContent
      .replace(/no overlapping/gi, '')
      .replace(/no overlap/gi, '')
      .replace(/labels should not overlap/gi, '')
      .replace(/ensure nothing is cut off/gi, '')
      .trim();

    this.logger.log(`Generating SVG for topic: ${topic || 'General'}, content length: ${cleanedContent.length} chars`);

    // Get the system prompt
    const systemPrompt = this.buildPromptService.enhanceSvgPrompt(
      cleanedContent,
      topic
    );

    // Enhance user prompt with content-specific instructions
    let enhancedUserPrompt = `Create an SVG drawing that illustrates the following concept: ${cleanedContent}. Respond with only the SVG code. Ensure NO overlapping elements and that NOTHING is cut off.`;

    // Add specific instructions for questions involving vehicles, speed, time, or distance
    const hasVehicleJourney =
      (cleanedContent.toLowerCase().includes('car') ||
       cleanedContent.toLowerCase().includes('train') ||
       cleanedContent.toLowerCase().includes('bus') ||
       cleanedContent.toLowerCase().includes('vehicle')) &&
      (cleanedContent.toLowerCase().includes('km/h') ||
       cleanedContent.toLowerCase().includes('speed') ||
       cleanedContent.toLowerCase().includes('hour') ||
       cleanedContent.toLowerCase().includes('distance'));

    if (hasVehicleJourney) {
      enhancedUserPrompt += ` This involves a vehicle journey, so include a clear visual of the vehicle, any speed indicators, time duration, and distance markers mentioned in the question.`;
    }

    const svgResult = await this.genImageService.generateImage(enhancedUserPrompt, systemPrompt);

    // Check if we got a valid SVG result
    if (!svgResult || !svgResult.includes('<svg')) {
      this.logger.warn('Failed to generate valid SVG, returning empty string');
      return '';
    }

    this.logger.log(`Successfully generated SVG with length: ${svgResult.length} chars`);
    return svgResult;
  }
}
