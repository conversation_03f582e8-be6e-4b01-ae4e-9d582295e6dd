import { Injectable, Logger } from '@nestjs/common';
import { ImageGenerationStrategy } from './image-generation-strategy.interface';

/**
 * Strategy for handling cases where no image is required
 */
@Injectable()
export class NoImageStrategy implements ImageGenerationStrategy {
  private readonly logger = new Logger(NoImageStrategy.name);
  private readonly noImagePhrases = [
    'no image required',
    'no image needed',
    'no image necessary',
    'no diagram required',
    'no diagram needed'
  ];

  /**
   * Determines if an image should be generated based on the prompt
   * @param imagePrompt The image prompt from the question
   * @param questionContent The content of the question (not used in this strategy)
   * @param topic Optional topic for determining if an image is necessary (not used in this strategy)
   * @returns False if the prompt indicates no image is needed, true otherwise
   */
  shouldGenerateImage(imagePrompt: string, questionContent?: string, topic?: string): boolean {
    const isNoImageRequired = this.noImagePhrases.some(phrase =>
      imagePrompt.toLowerCase().includes(phrase)
    );

    if (isNoImageRequired) {
      this.logger.log(`Skipping image generation as prompt indicates no image is required: "${imagePrompt}"`);
      return false;
    }

    return true;
  }

  /**
   * Returns an empty string as no image is to be generated
   * @param imageContent The content to use for image generation (ignored)
   * @param topic Optional topic for specialized image generation (ignored)
   * @param questionType Optional question type (ignored)
   * @returns Empty string
   */
  async generateImage(imageContent?: string, topic?: string, questionType?: string): Promise<string> {
    return '';
  }
}
