/**
 * Interface for image generation strategies
 */
export interface ImageGenerationStrategy {
  /**
   * Determines if an image should be generated based on the prompt and topic
   * @param imagePrompt The image prompt from the question
   * @param questionContent The content of the question
   * @param topic Optional topic for determining if an image is necessary
   * @returns True if an image should be generated, false otherwise
   */
  shouldGenerateImage(imagePrompt: string, questionContent: string, topic?: string): boolean;

  /**
   * Generates an image based on the provided content
   * @param imageContent The content to use for image generation
   * @param topic Optional topic for specialized image generation
   * @returns The generated image as a string (SVG)
   */
  generateImage(imageContent: string, topic?: string): Promise<string>;
}
