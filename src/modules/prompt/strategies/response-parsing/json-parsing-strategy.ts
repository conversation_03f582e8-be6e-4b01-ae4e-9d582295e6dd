import { Injectable, Logger } from '@nestjs/common';
import { ResponseParsingStrategy } from './response-parsing-strategy.interface';

/**
 * Strategy for parsing JSON responses
 */
@Injectable()
export class JsonParsingStrategy implements ResponseParsingStrategy {
  private readonly logger = new Logger(JsonParsingStrategy.name);

  /**
   * Parses the response content as JSON
   * @param content The response content to parse
   * @returns The parsed JSON object
   * @throws Error if the content cannot be parsed as JSON
   */
  parseResponse(content: string): any {
    try {
      // First try to parse the JSON directly
      return JSON.parse(content);
    } catch (error) {
      this.logger.warn(`JSON parsing error: ${error.message}. Attempting to extract valid JSON...`);

      try {
        // Look for JSON-like structure with regex
        const jsonMatch = content.match(/\{\s*"result"\s*:\s*\[.*\]\s*\}/s);
        if (jsonMatch) {
          // Try to parse the extracted JSON
          const data = JSON.parse(jsonMatch[0]);
          this.logger.log('Successfully extracted and parsed JSON from content');
          return data;
        } else {
          // If no complete JSON structure found, try to extract individual items
          const partialData = this.extractValidItemsFromPartialResponse(content);
          if (partialData.result.length > 0) {
            this.logger.log(`Successfully extracted ${partialData.result.length} valid items from partial response`);
            return partialData;
          }

          // If no valid items found, throw error
          throw new Error('Could not find valid JSON structure in response');
        }
      } catch (extractError) {
        // Try one more time to extract individual items
        try {
          const partialData = this.extractValidItemsFromPartialResponse(content);
          if (partialData.result.length > 0) {
            this.logger.log(`Successfully extracted ${partialData.result.length} valid items from partial response after extraction error`);
            return partialData;
          }
        } catch (partialError) {
          // Ignore this error and throw the original one
        }

        // If all attempts fail, throw a detailed error
        this.logger.error(`Failed to parse or extract JSON: ${extractError.message}`);
        throw new Error(`Invalid JSON response: ${error.message}. Content preview: ${content.substring(0, 100)}...`);
      }
    }
  }

  /**
   * Extracts valid items from a partial response
   * @param content The response content to extract items from
   * @returns An object with a result array containing the valid items
   */
  private extractValidItemsFromPartialResponse(content: string): { result: any[] } {
    const result: any[] = [];

    // Try to extract individual JSON objects that might be valid question items
    // Look for objects with at least content property
    const itemRegex = /\{[^\{\}]*"content"[^\{\}]*\}/g;
    const itemMatches = content.match(itemRegex);

    if (itemMatches) {
      for (const itemMatch of itemMatches) {
        try {
          const item = JSON.parse(itemMatch);
          if (item && typeof item === 'object' && 'content' in item && typeof item.content === 'string') {
            // Ensure required properties are present
            if (!('type' in item)) {
              item.type = 'unknown';
            }
            if ('options' in item && !Array.isArray(item.options)) {
              item.options = [];
            } else if (!('options' in item)) {
              item.options = [];
            }
            if ('answer' in item && !Array.isArray(item.answer)) {
              item.answer = [];
            } else if (!('answer' in item)) {
              item.answer = [];
            }

            result.push(item);
          }
        } catch (e) {
          // Skip invalid items
          this.logger.debug(`Skipping invalid item: ${e.message}`);
        }
      }
    }

    // Try another approach - look for arrays of objects
    if (result.length === 0) {
      const arrayRegex = /\[\s*\{[^\[\]]*\}\s*(,\s*\{[^\[\]]*\}\s*)*\]/g;
      const arrayMatches = content.match(arrayRegex);

      if (arrayMatches) {
        for (const arrayMatch of arrayMatches) {
          try {
            const items = JSON.parse(arrayMatch);
            if (Array.isArray(items)) {
              for (const item of items) {
                if (item && typeof item === 'object' && 'content' in item && typeof item.content === 'string') {
                  // Ensure required properties are present
                  if (!('type' in item)) {
                    item.type = 'unknown';
                  }
                  if ('options' in item && !Array.isArray(item.options)) {
                    item.options = [];
                  } else if (!('options' in item)) {
                    item.options = [];
                  }
                  if ('answer' in item && !Array.isArray(item.answer)) {
                    item.answer = [];
                  } else if (!('answer' in item)) {
                    item.answer = [];
                  }

                  result.push(item);
                }
              }
            }
          } catch (e) {
            // Skip invalid arrays
            this.logger.debug(`Skipping invalid array: ${e.message}`);
          }
        }
      }
    }

    return { result };
  }
}
