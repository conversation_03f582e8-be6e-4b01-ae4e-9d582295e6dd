import { Injectable, Logger } from '@nestjs/common';
import { ExerciseQuestionItem } from '../../interfaces/exercise-result.interface';

/**
 * Validator for AI response data
 */
@Injectable()
export class ResponseValidator {
  private readonly logger = new Logger(ResponseValidator.name);

  /**
   * Validates the structure of the parsed response data
   * @param data The data to validate
   * @returns The validated data
   * @throws Error if the data is invalid
   */
  validateResponse(data: any): any {
    // Validate the data structure
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid response format: expected an object');
    }

    if (!('result' in data) || !Array.isArray(data.result)) {
      throw new Error('Invalid response format: missing or invalid result array');
    }

    // Ensure each item in the result array has the required properties
    for (let i = 0; i < data.result.length; i++) {
      const item = data.result[i];
      if (!item || typeof item !== 'object') {
        throw new Error(`Invalid item at index ${i}: expected an object`);
      }

      // Check for required properties
      if (!('content' in item) || typeof item.content !== 'string') {
        this.logger.warn(`Item at index ${i} has missing or invalid content property`);
      }

      // Ensure options is an array if present
      if ('options' in item && !Array.isArray(item.options)) {
        this.logger.warn(`Item at index ${i} has invalid options property: expected an array`);
        // Convert to empty array to prevent errors
        item.options = [];
      }

      // Ensure answer is an array if present
      if ('answer' in item && !Array.isArray(item.answer)) {
        this.logger.warn(`Item at index ${i} has invalid answer property: expected an array`);
        // Convert to empty array to prevent errors
        item.answer = [];
      }
    }

    return data;
  }

  /**
   * Filters out invalid items from the result array
   * @param items The array of items to filter
   * @returns The filtered array of valid items
   */
  filterValidItems(items: any[]): ExerciseQuestionItem[] {
    return items.filter((item): item is ExerciseQuestionItem => {
      if (!item || typeof item !== 'object') {
        this.logger.warn(`Skipping invalid item in result array: ${JSON.stringify(item)}`);
        return false;
      }
      return true;
    });
  }
}
