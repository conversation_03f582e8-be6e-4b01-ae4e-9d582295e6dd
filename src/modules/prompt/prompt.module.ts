import { Module, forwardRef } from '@nestjs/common';
import { PromptController } from './prompt.controller';
import { ConfigModule } from '@nestjs/config';
import { AiModule } from '../ai/ai.module';
import { GenImageModule } from '../gen-image/gen-image.module';
import { BuildPromptModule } from '../build-prompt/build-prompt.module';
import { FilesModule } from '../files/files.module';
import { DocumentsModule } from '../documents/documents.module';
import { MongodbModule } from '../mongodb/mongodb.module';
import { SocketModule } from '../socket/socket.module';
import { QuestionPoolModule } from '../question-pool/question-pool.module';
import { SchoolModule } from '../school/school.module';

// Services
import { PromptService } from './services/prompt.service';
import { BatchProcessorService } from './services/batch-processor.service';
import { QuestionGeneratorService } from './services/question-generator.service';
import { FileAttachmentService } from './services/file-attachment.service';

// Strategies
import { JsonParsingStrategy } from './strategies/response-parsing';
import { ResponseValidator } from './strategies/response-parsing';
import { NoImageStrategy } from './strategies/image-generation';
import { DetailedImageStrategy } from './strategies/image-generation';
import { ImageStrategyFactory } from './strategies/image-generation';

@Module({
  imports: [
    ConfigModule,
    forwardRef(() => AiModule),
    forwardRef(() => GenImageModule),
    forwardRef(() => BuildPromptModule),
    forwardRef(() => FilesModule),
    forwardRef(() => DocumentsModule),
    forwardRef(() => QuestionPoolModule),
    MongodbModule,
    SocketModule,
    SchoolModule,
  ],
  providers: [
    // Main services
    PromptService,
    BatchProcessorService,
    QuestionGeneratorService,
    FileAttachmentService,

    // Response parsing strategies
    JsonParsingStrategy,
    ResponseValidator,

    // Image generation strategies
    NoImageStrategy,
    DetailedImageStrategy,
    ImageStrategyFactory,
  ],
  exports: [PromptService, QuestionGeneratorService, BatchProcessorService],
  controllers: [PromptController],
})
export class PromptModule {}
