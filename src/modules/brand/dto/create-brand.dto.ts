import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class CreateBrandDto {
  @ApiPropertyOptional({
    description: 'The official logo or symbol representing the school',
    example: 'https://example.com/logo.png',
  })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiPropertyOptional({
    description: 'Brand color scheme or primary colors',
    example: '#FF5733',
  })
  @IsOptional()
  @IsString()
  color?: string;

  @ApiPropertyOptional({
    description: 'Additional brand imagery or visual assets',
    example: 'https://example.com/image.png',
  })
  @IsOptional()
  @IsString()
  image?: string;
}