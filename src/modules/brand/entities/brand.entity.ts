import { Entity, Column } from 'typeorm';
import BaseEntity from 'src/core/entities/base-entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('brands')
export class Brand extends BaseEntity {
  @ApiProperty({
    description: 'The official logo or symbol representing the school',
    example: 'https://example.com/logo.png',
  })
  @Column({ nullable: true })
  logo: string;

  @ApiProperty({
    description: 'Brand color scheme or primary colors',
    example: '#FF5733',
  })
  @Column({ nullable: true })
  color: string;

  @ApiProperty({
    description: 'Additional brand imagery or visual assets',
    example: 'https://example.com/image.png',
  })
  @Column({ nullable: true })
  image: string;
}