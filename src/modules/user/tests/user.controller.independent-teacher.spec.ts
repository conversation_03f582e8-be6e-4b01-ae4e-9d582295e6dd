import { ForbiddenException } from '@nestjs/common';
import { UserController } from '../user.controller';
import { UserService } from '../user.service';
import { RbacService } from '../../auth/services/rbac.service';
import { EUserRole, CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { FilterUserDto } from '../dto/filter-user.dto';

describe('UserController - INDEPENDENT_TEACHER Restrictions', () => {
  let controller: UserController;
  let userService: jest.Mocked<UserService>;
  let rbacService: jest.Mocked<RbacService>;

  const mockIndependentTeacher = {
    sub: 'independent-teacher-id',
    email: '<EMAIL>',
    role: EUserRole.INDEPENDENT_TEACHER,
    schoolId: 'independent-school-id',
  };

  const mockRequest = {
    user: mockIndependentTeacher,
  };

  beforeEach(() => {
    userService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
    } as any;

    rbacService = {
      isSchoolManager: jest.fn(),
      isIndependentTeacher: jest.fn(),
      validateSchoolManagerUserCreation: jest.fn(),
      validateSchoolManagerUserAssignment: jest.fn(),
      getEffectiveSchoolId: jest.fn(),
    } as any;

    // Create controller instance directly without NestJS DI
    controller = new UserController(userService, rbacService);
  });

  describe('create', () => {
    it('should prevent INDEPENDENT_TEACHER from creating user accounts', () => {
      const createDto: CreateUserDto = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.TEACHER,
        schoolId: 'some-school-id',
      };

      expect(() =>
        controller.create(createDto, mockRequest as any)
      ).toThrow(ForbiddenException);

      expect(userService.create).not.toHaveBeenCalled();
    });

    it('should throw ForbiddenException with appropriate message', () => {
      const createDto: CreateUserDto = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.TEACHER,
        schoolId: 'some-school-id',
      };

      expect(() =>
        controller.create(createDto, mockRequest as any)
      ).toThrow('Independent teachers are not allowed to create user accounts.');
    });
  });

  describe('findAll', () => {
    it('should prevent INDEPENDENT_TEACHER from accessing user lists', async () => {
      const filterDto: FilterUserDto = {};

      await expect(
        controller.findAll(filterDto, mockRequest as any)
      ).rejects.toThrow(ForbiddenException);

      expect(userService.findAll).not.toHaveBeenCalled();
    });

    it('should throw ForbiddenException with appropriate message', async () => {
      const filterDto: FilterUserDto = {};

      await expect(
        controller.findAll(filterDto, mockRequest as any)
      ).rejects.toThrow('Independent teachers are not allowed to access user lists.');
    });
  });

  describe('findOne', () => {
    it('should prevent INDEPENDENT_TEACHER from accessing other user details', async () => {
      await expect(
        controller.findOne('some-user-id', mockRequest as any)
      ).rejects.toThrow(ForbiddenException);

      expect(userService.findById).not.toHaveBeenCalled();
    });

    it('should throw ForbiddenException with appropriate message', async () => {
      await expect(
        controller.findOne('some-user-id', mockRequest as any)
      ).rejects.toThrow('Independent teachers are not allowed to access other user details.');
    });
  });

  describe('update', () => {
    it('should prevent INDEPENDENT_TEACHER from updating other user accounts', async () => {
      const updateDto: UpdateUserDto = {
        name: 'Updated Name',
      };

      await expect(
        controller.update('some-user-id', updateDto, mockRequest as any)
      ).rejects.toThrow(ForbiddenException);

      expect(userService.update).not.toHaveBeenCalled();
    });

    it('should throw ForbiddenException with appropriate message', async () => {
      const updateDto: UpdateUserDto = {
        name: 'Updated Name',
      };

      await expect(
        controller.update('some-user-id', updateDto, mockRequest as any)
      ).rejects.toThrow('Independent teachers are not allowed to update other user accounts.');
    });
  });

  describe('me', () => {
    it('should allow INDEPENDENT_TEACHER to access their own profile', async () => {
      const mockProfile = {
        id: mockIndependentTeacher.sub,
        name: 'Independent Teacher',
        email: mockIndependentTeacher.email,
        role: mockIndependentTeacher.role,
        schoolId: mockIndependentTeacher.schoolId,
      };

      userService.findById.mockResolvedValue(mockProfile as any);

      const result = await controller.me(mockIndependentTeacher as any);

      expect(userService.findById).toHaveBeenCalledWith(mockIndependentTeacher.sub);
      expect(result).toEqual(mockProfile);
    });
  });

  describe('Role-based access control verification', () => {
    it('should verify that INDEPENDENT_TEACHER role is properly checked', async () => {
      const createDto: CreateUserDto = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.TEACHER,
        schoolId: 'some-school-id',
      };

      // Test with different role to ensure the check is specific to INDEPENDENT_TEACHER
      const mockAdminRequest = {
        user: { ...mockIndependentTeacher, role: EUserRole.ADMIN },
      };

      rbacService.isSchoolManager.mockReturnValue(false);
      userService.create.mockResolvedValue({ id: 'new-user-id' } as any);

      // Admin should be able to create users (no exception thrown)
      expect(() =>
        controller.create(createDto, mockAdminRequest as any)
      ).not.toThrow();

      expect(userService.create).toHaveBeenCalledWith(createDto);
    });
  });
});
