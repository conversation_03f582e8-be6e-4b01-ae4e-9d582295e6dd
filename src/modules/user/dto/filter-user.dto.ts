import { IsOptional, IsUUID, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { EUserRole } from './create-user.dto';

export class FilterUserDto {
  @ApiPropertyOptional({
    description: 'Filter users by school ID (UUID format)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  schoolId?: string;

  @ApiPropertyOptional({
    description: 'Filter users by role',
    example: EUserRole.TEACHER,
    enum: EUserRole,
    enumName: 'EUserRole',
  })
  @IsOptional()
  @IsEnum(EUserRole)
  role?: EUserRole;
}