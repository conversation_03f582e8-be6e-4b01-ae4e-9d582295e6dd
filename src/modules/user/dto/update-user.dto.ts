import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsUUID, IsDate } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { EUserRole } from './create-user.dto';

export class UpdateUserDto {
  @ApiPropertyOptional({
    description: 'User full name',
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: 'User password (minimum 6 characters)',
    example: 'password123',
    minLength: 6,
  })
  @IsOptional()
  @MinLength(6)
  password?: string;

  @ApiPropertyOptional({
    description: 'User role',
    example: EUserRole.TEACHER,
    enum: EUserRole,
    enumName: 'EUserRole',
  })
  @IsOptional()
  @IsString()
  role?: EUserRole;

  @ApiPropertyOptional({
    description: 'School ID (required for TEACHER, STUDENT, and SCHOOL_MANAGER roles)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  schoolId?: string;

  @ApiPropertyOptional({
    description: 'Password reset token',
    example: 'a1b2c3d4...',
  })
  @IsOptional()
  @IsString()
  passwordResetToken?: string | null;

  @ApiPropertyOptional({
    description: 'Password reset token expiry date',
  })
  @IsOptional()
  @IsDate()
  passwordResetExpires?: Date | null;
}