import { Document } from 'llamaindex';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GEMINI_EMBEDDING_MODEL, GeminiEmbedding } from '@llamaindex/google';
import { EmbeddingCacheService } from '../mongodb/services/embedding-cache.service';

@Injectable()
export class EmbeddingService {
  private embedClient: GeminiEmbedding;
  private readonly logger = new Logger(EmbeddingService.name);

  constructor(
    private configService: ConfigService,
    private embeddingCacheService: EmbeddingCacheService
  ) {
    // Initialize the Gemini client with API key from config
    const apiKey = this.configService.get<string>('GOOGLE_API_KEY');

    // The correct way to initialize GeminiEmbedding
    this.embedClient = new GeminiEmbedding({
      model: GEMINI_EMBEDDING_MODEL.EMBEDDING_001,
    });
  }

  async embed(chunks: string, chunkID: string): Promise<Document> {
    return new Document({
      text: chunks,
      id_: chunkID,
    });
  }

  /**
   * Gets an embedding for a query with MongoDB caching
   * @param text The text to get embeddings for
   * @param skipCache Whether to skip the cache
   * @returns The embedding vector
   */
  async getQueryEmbedding(text: string, skipCache: boolean = false): Promise<number[]> {
    try {
      // Check if we should use cache
      if (!skipCache) {
        // Try to get from cache
        const cachedEmbedding = await this.embeddingCacheService.getEmbedding(text);
        if (cachedEmbedding) {
          this.logger.debug(`Using cached embedding for text: "${text.substring(0, 30)}..."`);
          return cachedEmbedding;
        }
      }

      this.logger.debug(`Generating new embedding for text: "${text.substring(0, 30)}..."`);

      // Generate new embedding with retry logic for rate limits
      const embedding = await this.retryWithBackoff(async () => {
        // Call the Gemini API to get embeddings
        // getTextEmbedding returns number[] directly, not an object with embedding property
        return await this.embedClient.getTextEmbedding(text);
      });

      // Save to cache if not skipping cache
      if (!skipCache) {
        await this.embeddingCacheService.saveEmbedding(text, embedding);
      }

      return embedding;
    } catch (error) {
      this.logger.error(`Error generating embedding: ${error.message}`);
      throw error;
    }
  }

  /**
   * Clears expired embedding cache entries
   * @returns Number of expired entries removed
   */
  async clearExpiredEmbeddingCache(): Promise<number> {
    return await this.embeddingCacheService.clearExpiredEntries();
  }

  /**
   * Clears the embedding memory cache
   */
  clearEmbeddingMemoryCache(): void {
    this.embeddingCacheService.clearMemoryCache();
  }

  /**
   * Retry a function with exponential backoff
   * @param fn The function to retry
   * @param maxRetries Maximum number of retries
   * @param initialDelay Initial delay in milliseconds
   * @returns The result of the function
   * @private
   */
  private async retryWithBackoff<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    initialDelay: number = 1000
  ): Promise<T> {
    let retries = 0;
    let delay = initialDelay;

    while (true) {
      try {
        return await fn();
      } catch (error) {
        retries++;
        if (retries > maxRetries) {
          this.logger.error(`Max retries (${maxRetries}) exceeded:`, error);
          throw error;
        }

        // Check if error is related to rate limiting
        const isRateLimitError =
          error.message?.includes('rate limit') ||
          error.message?.includes('quota') ||
          error.message?.includes('too many requests');

        if (isRateLimitError) {
          this.logger.warn(`Rate limit error detected, retrying after delay (attempt ${retries}/${maxRetries})`);
        } else {
          this.logger.warn(`Error occurred, retrying after delay (attempt ${retries}/${maxRetries}): ${error.message}`);
        }

        // Exponential backoff with jitter
        delay = delay * 2 + Math.random() * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
}
