import {
  <PERSON>,
  <PERSON>,
  Headers,
  Req,
  Res,
  BadRequestException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { Request, Response } from 'express';
import Stripe from 'stripe';
import { Public } from '../auth/decorators/public.decorator';
import { PriceService } from '../price/price.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { PackagesService } from '../packages/packages.service';
import { StripeService } from './stripe.service';

@Controller('stripe')
export class StripeController {
  constructor(
    private readonly stripeService: StripeService,
    private readonly priceService: PriceService,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => PackagesService))
    private readonly packageService: PackagesService,
  ) {}

  @Public()
  @Post('webhook')
  async handleStripeWebhook(
    @Req() req: Request,
    @Res() res: Response,
    @Headers('stripe-signature') sig: string,
  ) {
    if (!sig) {
      throw new BadRequestException('Missing stripe-signature header');
    }

    const event = this.stripeService.constructWebhookEvent(req.body, sig);

    switch (event.type) {
      case 'price.created':
      case 'price.updated':
        await this.priceService.saveOrUpdateStripePrice(
          event.data.object as Stripe.Price,
        );
        break;

      case 'product.created':
      case 'product.updated':
      case 'product.deleted':
        await this.packageService.handleStripeProduct(
          event.data.object as Stripe.Product,
          event.type,
        );
        break;

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await this.subscriptionService.createOrUpdateFromStripe(
          event.data.object as Stripe.Subscription,
        );
        break;
    }
    return res.json({ received: true });
  }
}
