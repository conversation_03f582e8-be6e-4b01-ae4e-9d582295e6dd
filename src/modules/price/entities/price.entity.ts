// price.entity.ts
import { ApiProperty } from '@nestjs/swagger';
import {Package} from 'src/modules/packages/entities/package.entity';
import {
    Entity,
    Column,
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    ManyToOne,
  } from 'typeorm';

  @Entity('prices')
  export class Price {
    @ApiProperty({
      description: 'Unique identifier for the price',
      example: '123e4567-e89b-12d3-a456-************',
    })
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @ApiProperty({
      description: 'Stripe Price ID',
      example: 'price_1NzV5gFkXjTxxxxxx',
    })
    @Column({ unique: true })
    stripePriceId: string; // e.g. "price_1NzV5gFkXjTxxxxxx"

    @ApiProperty({
      description: 'Currency code',
      example: 'usd',
    })
    @Column()
    currency: string; // e.g. "usd"

    @ApiProperty({
      description: 'Price amount in smallest currency unit (e.g. cents)',
      example: 2000,
    })
    @Column('integer')
    unitAmount: number; // in the smallest currency unit (e.g. cents)

    @ApiProperty({
      description: 'Nickname for the price',
      example: 'Monthly Premium',
      required: false,
    })
    @Column({ nullable: true })
    nickname: string;

    @ApiProperty({
      description: 'Whether the price is active',
      example: true,
    })
    @Column({ default: false })
    active: boolean;

    @ApiProperty({
      description: 'Type of pricing',
      enum: ['one_time', 'recurring'],
      example: 'recurring',
      required: false,
    })
    @Column({ nullable: true })
    type: 'one_time' | 'recurring';

    @ApiProperty({
      description: 'Billing interval for recurring prices',
      enum: ['day', 'week', 'month', 'year'],
      example: 'month',
      required: false,
    })
    @Column({ nullable: true })
    interval: 'day' | 'week' | 'month' | 'year'; // only for recurring

    @ApiProperty({
      description: 'Number of intervals between billings',
      example: 1,
      required: false,
    })
    @Column({ nullable: true, type: 'int' })
    intervalCount: number; // only for recurring

    @ApiProperty({
      description: 'Trial period in days',
      example: 7,
      required: false,
    })
    @Column({ nullable: true, type: 'int' })
    trialPeriodDays: number;

    @ApiProperty({
      description: 'Usage type for recurring prices',
      enum: ['licensed', 'metered'],
      example: 'licensed',
      required: false,
    })
    @Column({ nullable: true })
    usageType: 'licensed' | 'metered'; // for recurring

    @ApiProperty({
      description: 'Associated package',
      type: () => Package,
    })
    @ManyToOne(() => Package, (pkg) => pkg.prices, { onDelete: 'CASCADE' })
    package: Package;

    @ApiProperty({
      description: 'Creation timestamp',
    })
    @CreateDateColumn()
    createdAt: Date;

    @ApiProperty({
      description: 'Last update timestamp',
    })
    @UpdateDateColumn()
    updatedAt: Date;
  }
  