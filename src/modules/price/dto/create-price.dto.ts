import { <PERSON>String, IsNotEmpty, IsOptional, IsNumber, IsBoolean, IsEnum, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePriceDto {
  @ApiProperty({
    description: 'Currency code (ISO 4217)',
    example: 'usd',
  })
  @IsString()
  @IsNotEmpty()
  currency: string;

  @ApiProperty({
    description: 'Price amount in smallest currency unit (e.g. cents for USD)',
    example: 2000,
  })
  @IsNumber()
  unitAmount: number;

  @ApiProperty({
    description: 'Nickname for the price',
    example: 'Monthly Premium',
    required: false,
  })
  @IsOptional()
  @IsString()
  nickname?: string;

  @ApiProperty({
    description: 'Whether the price is active',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @ApiProperty({
    description: 'Type of pricing',
    enum: ['one_time', 'recurring'],
    example: 'recurring',
  })
  @IsEnum(['one_time', 'recurring'])
  type: 'one_time' | 'recurring';

  @ApiProperty({
    description: 'Billing interval for recurring prices (required if type is recurring)',
    enum: ['day', 'week', 'month', 'year'],
    example: 'month',
    required: false,
  })
  @IsOptional()
  @IsEnum(['day', 'week', 'month', 'year'])
  interval?: 'day' | 'week' | 'month' | 'year';

  @ApiProperty({
    description: 'Number of intervals between billings (required if type is recurring)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  intervalCount?: number;

  @ApiProperty({
    description: 'Trial period in days',
    example: 7,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  trialPeriodDays?: number;

  @ApiProperty({
    description: 'Usage type for recurring prices',
    enum: ['licensed', 'metered'],
    example: 'licensed',
    required: false,
  })
  @IsOptional()
  @IsEnum(['licensed', 'metered'])
  usageType?: 'licensed' | 'metered';

  @ApiProperty({
    description: 'Package ID to associate this price with',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  packageId: string;

  @ApiProperty({
    description: 'Stripe Price ID (optional, will be created if not provided)',
    example: 'price_1NzV5gFkXjTxxxxxx',
    required: false,
  })
  @IsOptional()
  @IsString()
  stripePriceId?: string;
}
