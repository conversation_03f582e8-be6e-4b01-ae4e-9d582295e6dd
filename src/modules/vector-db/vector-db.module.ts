// In vector-db.module.ts
import { DynamicModule, Module, Provider } from '@nestjs/common';
import { QdrantVectorDBService } from './implementations/qdrant.service';
import { EmbeddingModule } from '../embedding/embedding.module';
import { PineconeService } from './implementations/pinecone.service';

@Module({})
export class VectorDBModule {
  static register(options: { type: string }): DynamicModule {
    let provider: Provider;

    switch (options.type) {
      case 'qdrant':
        provider = {
          provide: 'VECTOR_DB',
          useClass: QdrantVectorDBService,
        };
        break;
      case 'pinecone':
        provider = {
          provide: 'VECTOR_DB',
          useClass: PineconeService, // Replace with actual Pinecone service
        };
        break;
      default:
        provider = {
          provide: 'VECTOR_DB',
          useClass: QdrantVectorDBService,
        };
    }

    return {
      module: VectorDBModule,
      imports: [EmbeddingModule],
      providers: [provider],
      exports: ['VECTOR_DB'],
    };
  }
}
