import { Index, Pinecone, PineconeRecord, RecordMetadata, RecordMetadataValue } from '@pinecone-database/pinecone';
import { Injectable, OnModuleInit } from '@nestjs/common';
import {
  VectorDBDocument,
  VectorDBService,
} from '../interfaces/vector-db.interface';
import { PineconeVectorStore } from '@llamaindex/pinecone';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PineconeService implements VectorDBService, OnModuleInit {
  private client: Pinecone;
  private index: Index;
  private collectionName: string;

  constructor(private configService: ConfigService) {
    this.collectionName = this.configService.get<string>(
      'PINECONE_NAME_SPACE',
      'edusg',
    );
    const pineconeApiKey = this.configService.get<string>(
      'PINECONE_API_KEY',
    ) as string;
    this.client = new Pinecone({
      apiKey: pineconeApiKey,
    });
  }

  async onModuleInit(): Promise<void> {
    await this.initialize();
  }

  /**
   * Initialize the Pinecone service
   * @throws Error if configuration is not set
   */
  async initialize(): Promise<void> {
    if (!this.client) {
      throw new Error(
        'Pinecone configuration is not set. Call setConfig() first.',
      );
    }

    try {
      // Get the list of indexes
      const indexes = await this.client.listIndexes();
      console.log('Available Pinecone indexes:', indexes);

      // Check if our index exists
      const indexName = this.configService.get<string>(
        'PINECONE_NAME_SPACE',
        'edusg',
      );
      const indexExists =
        indexes.indexes &&
        indexes.indexes.some((index) => index.name === indexName);

      if (!indexExists) {
        console.log(
          `Index ${indexName} does not exist. Please create it in the Pinecone console.`,
        );
      } else {
        console.log(`Using existing Pinecone index: ${indexName}`);
        this.index = this.client.index(indexName);
      }
    } catch (error) {
      console.error('Error initializing Pinecone:', error);
      // Don't throw here to allow the application to start even if Pinecone is not available
      console.log(
        'Continuing without Pinecone initialization. Some features may not work properly.',
      );
    }
  }

  getVectorCollection(): PineconeVectorStore {
    const apiKey = this.configService.get<string>('PINECONE_API_KEY');
    const indexName = this.configService.get<string>(
      'PINECONE_NAME_SPACE',
      'edusg',
    );

    if (!apiKey) {
      console.error('PINECONE_API_KEY is not defined in environment variables');
    }

    console.log(`Creating PineconeVectorStore with index: ${indexName}`);

    return new PineconeVectorStore({
      apiKey: apiKey as string,
      indexName: indexName,
    });
  }

  getVectorStore(): Pinecone {
    return this.client;
  }

  async upsertDocuments(documents: VectorDBDocument[]): Promise<void> {
    const vectors: PineconeRecord<RecordMetadata>[] = documents.map((doc) => {
      const pineconeMetadata: RecordMetadata = {};
      if (doc.metadata) {
        for (const key in doc.metadata) {
          if (Object.prototype.hasOwnProperty.call(doc.metadata, key)) {
            const value = doc.metadata[key];
            if (
              typeof value === 'string' ||
              typeof value === 'number' ||
              typeof value === 'boolean' ||
              (Array.isArray(value) && value.every(item => typeof item === 'string')) ||
              value === null
            ) {
              pineconeMetadata[key] = value as RecordMetadataValue;
            } else {
              // Optionally log or handle incompatible metadata types
              console.warn(
                `PineconeService: Metadata field '${key}' has an incompatible type for Pinecone and will be omitted. Value: ${JSON.stringify(value)}, Type: ${typeof value}`
              );
              // Alternative: try to convert, e.g., pineconeMetadata[key] = String(value);
              // However, omitting is safer to prevent unexpected data in Pinecone.
            }
          }
        }
      }
      return {
        id: doc.id,
        values: doc.embedding,
        metadata: pineconeMetadata,
      };
    });

    try {
      // Ensure this.index is initialized before attempting to use it.
      // The initialize method already has logging for this, but an explicit check here
      // before a critical operation might be beneficial if errors persist.
      if (!this.index) {
        throw new Error('Pinecone index is not initialized. Cannot upsert documents.');
      }
      await this.index.namespace(this.collectionName).upsert(vectors);
    } catch (error) {
      console.error('Error upserting documents to Pinecone:', error);
      throw error;
    }
  }

  /**
   * Query documents by metadata
   * @param metadata The metadata to filter by
   * @param limit Maximum number of documents to return (default: 10)
   * @returns Array of matching documents
   */
  async queryDocumentsByMetadata(
    metadata: Record<string, unknown>,
    limit: number = 10
  ): Promise<VectorDBDocument[]> {
    try {
      if (!this.index) {
        throw new Error('Pinecone index is not initialized');
      }

      // Build the filter object from the metadata
      const filter = {
        $and: Object.entries(metadata).map(([key, value]) => ({
          [key]: { $eq: value },
        })),
      };

      // Create a dummy vector of the right dimension for the query
      // This is needed because Pinecone requires a vector for querying
      // But we're only interested in metadata filtering
      const dummyVector = new Array(768).fill(0); // Corrected dimension to match index

      // Query Pinecone with the metadata filter
      const queryResponse = await this.index.namespace(this.collectionName).query({
        vector: dummyVector,
        filter: filter,
        topK: limit,
        includeMetadata: true,
        includeValues: true,
      });

      // Map the response to VectorDBDocument objects
      if (queryResponse.matches && queryResponse.matches.length > 0) {
        return queryResponse.matches.map((match) => ({
          id: match.id,
          embedding: match.values as number[],
          metadata: match.metadata as Record<string, unknown>,
          score: match.score,
        }));
      }

      return [];
    } catch (error) {
      console.error('Error querying documents by metadata from Pinecone:', error);
      throw error;
    }
  }

  async deleteDocuments(documentIds: string[]): Promise<void> {
    try {
      if (!this.index) {
        throw new Error('Pinecone index is not initialized');
      }
      if (documentIds.length === 0) {
        console.log('No document IDs provided for deletion.');
        return;
      }
      await this.index.namespace(this.collectionName).deleteMany(documentIds);
      console.log(`Successfully deleted ${documentIds.length} documents from Pinecone namespace: ${this.collectionName}`);
    } catch (error) {
      console.error('Error deleting documents from Pinecone:', error);
      throw error;
    }
  }
}
