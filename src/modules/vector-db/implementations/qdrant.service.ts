import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  VectorDBDocument,
  VectorDBService,
} from '../interfaces/vector-db.interface';
import { EmbeddingService } from '../../embedding/embedding.service';
import { QdrantClient } from '@qdrant/js-client-rest';
import { QdrantVectorStore } from '@llamaindex/qdrant';

@Injectable()
export class QdrantVectorDBService implements VectorDBService, OnModuleInit {
  private client: QdrantClient;
  private collectionName: string;
  private vectorSize: number;

  constructor(
    private configService: ConfigService,
    private embeddingService: EmbeddingService,
  ) {
    this.collectionName = this.configService.get<string>(
      'QDRANT_COLLECTION',
      'documents',
    );
    this.vectorSize = this.configService.get<number>('VECTOR_SIZE', 1536);

    const qdrantUrl = this.configService.get<string>(
      'QDRANT_URL',
      'http://localhost',
    );
    const qdrantPort = this.configService.get<number>('QDRANT_PORT', 6333);
    console.log(`Connecting to Qdrant at ${qdrantUrl}...`);
    if (!qdrantUrl) {
      throw new Error('QDRANT_URL is not defined in the environment variables');
    }
    this.client = new QdrantClient({ url: `${qdrantUrl}:${qdrantPort}` });
  }

  async onModuleInit(): Promise<void> {
    await this.initialize();
  }

  async initialize(): Promise<void> {
    const collections = await this.client.getCollections();

    try {
      const collectionExists = collections.collections.some(
        (collection) => collection.name === this.collectionName,
      );

      if (!collectionExists) {
        console.log(`Creating vector collection: ${this.collectionName}`);
        await this.client.createCollection(this.collectionName, {
          optimizers_config: {
            default_segment_number: 2,
          },
          on_disk_payload: true,
        });

        console.log(`Created vector collection: ${this.collectionName}`);
      }
    } catch (error) {
      console.error('Failed to initialize Qdrant:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      throw new Error(`Qdrant initialization failed: ${error.message}`);
    }
  }

  getVectorStore(): QdrantClient {
    return this.client;
  }

  getVectorCollection() {
    // Get your QdrantClient instance
    const qdrantClient = this.getVectorStore();

    // Create the vector store wrapper
    // Use it with VectorStoreIndex
    return new QdrantVectorStore({
      client: qdrantClient,
      collectionName: this.collectionName,
    });
  }

  getCollectionName() {
    return this.collectionName;
  }

  async upsertDocuments(documents: VectorDBDocument[]): Promise<void> {
    try {
      const points = documents.map((doc) => ({
        id: doc.id,
        vector: doc.embedding,
        payload: doc.metadata,
      }));

      await this.client.upsert(this.collectionName, {
        points,
      });
    } catch (error) {
      console.error('Failed to upsert documents to Qdrant:', error);
      throw new Error(`Qdrant upsert failed: ${error.message}`);
    }
  }

  async findSimilar(
    embedding: number[],
    limit = 10,
  ): Promise<VectorDBDocument[]> {
    try {
      const response = await this.client.search(this.collectionName, {
        vector: embedding,
        limit,
        with_payload: true,
      });

      return response.map((hit) => ({
        id: String(hit.id),
        embedding: [] as number[], // Explicitly type as number[]
        metadata: hit.payload as Record<string, any>,
        score: hit.score,
      }));
    } catch (error) {
      console.error('Failed to find similar documents in Qdrant:', error);
      throw new Error(`Qdrant search failed: ${error.message}`);
    }
  }

  async findSimilarByText(
    text: string,
    limit = 10,
  ): Promise<VectorDBDocument[]> {
    try {
      const tempId = `query-${Date.now()}`;
      const embedding = await this.embeddingService.embed(text, tempId);

      if (!Array.isArray(embedding)) {
        throw new Error(
          'EmbeddingService did not return a valid embedding array',
        );
      }

      return this.findSimilar(embedding, limit);
    } catch (error) {
      console.error(
        'Failed to find similar documents by text in Qdrant:',
        error,
      );
      throw new Error(`Qdrant text search failed: ${error.message}`);
    }
  }

  async deleteDocuments(documentIds: string[]): Promise<void> {
    try {
      await this.client.delete(this.collectionName, { 
        points: documentIds 
      });
      console.log(`Successfully deleted ${documentIds.length} documents from Qdrant collection: ${this.collectionName}`);
    } catch (error) {
      console.error('Failed to delete documents from Qdrant:', error);
      throw new Error(`Qdrant delete failed: ${error.message}`);
    }
  }

  async queryDocumentsByMetadata(
    metadataFilter: Record<string, unknown>,
    limit = 10,
  ): Promise<VectorDBDocument[]> {
    try {
      const filterConditions = Object.entries(metadataFilter).map(([key, value]) => ({
        key: key, // Assuming metadata keys are top-level in the payload
        match: {
          value: value,
        },
      }));

      const filter = {
        must: filterConditions,
      };

      const response = await this.client.scroll(this.collectionName, {
        filter,
        limit,
        with_payload: true,
        with_vector: false, // No need for vectors if just filtering by metadata
      });

      return response.points.map((point) => ({
        id: String(point.id),
        embedding: [], // Not fetching embedding in this query
        metadata: point.payload as Record<string, any>,
        // score is not typically returned by scroll, unless specifically requested/calculated
      }));
    } catch (error) {
      console.error('Failed to query documents by metadata in Qdrant:', error);
      throw new Error(`Qdrant metadata query failed: ${error.message}`);
    }
  }
}
