import { QdrantClient } from '@qdrant/js-client-rest';
import { QdrantVectorStore } from '@llamaindex/qdrant';
import { Pinecone } from '@pinecone-database/pinecone';
import { PineconeVectorStore } from '@llamaindex/pinecone';

export interface VectorDBDocument {
  id: string;
  embedding: number[];
  metadata: Record<string, unknown>;
  score?: number; // Optional, for search results
}

export interface VectorDBService {
  initialize(): Promise<void>;
  upsertDocuments(documents: VectorDBDocument[]): Promise<void>;
  getVectorStore(): QdrantClient | Pinecone; // Replace with actual type of your vector store`
  deleteDocuments(documentIds: string[]): Promise<void>;
  getVectorCollection(): QdrantVectorStore | PineconeVectorStore;
  queryDocumentsByMetadata(metadata: Record<string, unknown>, limit?: number): Promise<VectorDBDocument[]>;
}
