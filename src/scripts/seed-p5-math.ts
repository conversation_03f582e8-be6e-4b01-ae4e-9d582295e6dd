import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { OptionsService } from '../modules/options/options.service';

async function bootstrap() {
  try {
    // Create a NestJS application context
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the OptionsService
    const optionsService = app.get(OptionsService);
    
    console.log('Starting to seed P5 Math subjects...');
    
    // Seed the initial data first (if needed)
    await optionsService.seedInitialData();
    
    // Seed the P5 Math subjects
    const result = await optionsService.seedP5MathSubjectsDetailed();
    
    console.log('Seeding completed:', result);
    
    // Close the application context
    await app.close();
    
    process.exit(0);
  } catch (error) {
    console.error('Error seeding P5 Math subjects:', error);
    process.exit(1);
  }
}

bootstrap();
