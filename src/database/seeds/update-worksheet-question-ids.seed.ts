import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorksheetPromptResult } from '../../modules/mongodb/schemas/worksheet-prompt-result.schema';
import { Worksheet } from '../../modules/worksheet/entities/worksheet.entity';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class UpdateWorksheetQuestionIdsSeed {
  private readonly logger = new Logger(UpdateWorksheetQuestionIdsSeed.name);

  constructor(
    @InjectModel(WorksheetPromptResult.name)
    private worksheetPromptResultModel: Model<WorksheetPromptResult>,
    @InjectRepository(Worksheet)
    private worksheetRepository: Repository<Worksheet>,
  ) {}

  async run(): Promise<void> {
    this.logger.log('Starting worksheet question IDs migration...');

    try {
      // Validate database connections
      await this.validateConnections();

      // Get all worksheet prompt results that have questions
      const promptResults = await this.worksheetPromptResultModel.find({
        'promptResult.result': { $exists: true, $ne: [] }
      }).exec();

      this.logger.log(`Found ${promptResults.length} worksheet prompt results to process`);

      if (promptResults.length === 0) {
        this.logger.log('No worksheets found that need processing. Migration completed.');
        return;
      }

      let processedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;
      const errorDetails: string[] = [];

      for (const promptResult of promptResults) {
        try {
          const result = await this.processWorksheetPromptResult(promptResult);
          if (result.skipped) {
            skippedCount++;
          } else {
            processedCount++;
          }

          if ((processedCount + skippedCount) % 10 === 0) {
            this.logger.log(`Progress: ${processedCount + skippedCount}/${promptResults.length} worksheets (${processedCount} processed, ${skippedCount} skipped)...`);
          }
        } catch (error) {
          errorCount++;
          const errorMsg = `Worksheet ${promptResult.worksheetId}: ${error.message}`;
          errorDetails.push(errorMsg);
          this.logger.error(errorMsg, error.stack);
        }
      }

      this.logger.log(
        `Migration completed! Processed: ${processedCount}, Skipped: ${skippedCount}, Errors: ${errorCount}, Total: ${promptResults.length}`
      );

      if (errorCount > 0) {
        this.logger.warn('Errors encountered during migration:');
        errorDetails.forEach(error => this.logger.warn(`- ${error}`));
      }

    } catch (error) {
      this.logger.error(`Migration failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async processWorksheetPromptResult(promptResult: any): Promise<{ skipped: boolean }> {
    const worksheetId = promptResult.worksheetId;
    const questions = promptResult.promptResult?.result || [];

    if (!Array.isArray(questions) || questions.length === 0) {
      this.logger.warn(`No questions found for worksheet ${worksheetId}`);
      return { skipped: true };
    }

    // Check if questions already have IDs
    const hasIds = questions.every(q => q && q.id);
    if (hasIds) {
      this.logger.debug(`Worksheet ${worksheetId} already has question IDs, skipping...`);
      return { skipped: true };
    }

    this.logger.log(`Processing worksheet ${worksheetId} with ${questions.length} questions`);

    // Generate IDs for questions that don't have them
    const updatedQuestions = questions.map((question, index) => {
      if (!question) {
        this.logger.warn(`Null question found at index ${index} for worksheet ${worksheetId}`);
        return question;
      }

      // If question already has an ID, keep it
      if (question.id) {
        return question;
      }

      // Generate new UUID for the question
      const questionId = uuidv4();
      this.logger.debug(`Generated ID ${questionId} for question ${index + 1} in worksheet ${worksheetId}`);

      return {
        ...question,
        id: questionId
      };
    });

    // Extract question IDs for PostgreSQL
    const questionIds = updatedQuestions
      .filter(q => q && q.id)
      .map(q => q.id);

    // Update MongoDB with questions that have IDs
    await this.worksheetPromptResultModel.findOneAndUpdate(
      { worksheetId },
      {
        'promptResult.result': updatedQuestions
      }
    ).exec();

    this.logger.debug(`Updated MongoDB for worksheet ${worksheetId} with ${questionIds.length} question IDs`);

    // Update PostgreSQL worksheet with question IDs
    await this.updatePostgreSQLWorksheet(worksheetId, questionIds);

    this.logger.log(`Successfully processed worksheet ${worksheetId}`);
    return { skipped: false };
  }

  private async updatePostgreSQLWorksheet(worksheetId: string, questionIds: string[]): Promise<void> {
    try {
      // Find the worksheet in PostgreSQL
      const worksheet = await this.worksheetRepository.findOne({
        where: { id: worksheetId }
      });

      if (!worksheet) {
        this.logger.warn(`Worksheet ${worksheetId} not found in PostgreSQL, skipping...`);
        return;
      }

      // Create question metadata
      const questionMetadata = {
        lastQuestionUpdate: new Date(),
        questionVersion: 1,
        hasUnsavedChanges: false,
        collaborators: [],
        lockStatus: {
          isLocked: false
        }
      };

      // Update the worksheet with question IDs and metadata
      await this.worksheetRepository.update(
        { id: worksheetId },
        {
          questionIds,
          questionMetadata,
          maxQuestions: Math.max(worksheet.maxQuestions || 100, questionIds.length)
        }
      );

      this.logger.debug(`Updated PostgreSQL worksheet ${worksheetId} with ${questionIds.length} question IDs`);

    } catch (error) {
      this.logger.error(`Error updating PostgreSQL for worksheet ${worksheetId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate database connections before running migration
   */
  private async validateConnections(): Promise<void> {
    try {
      // Test MongoDB connection
      await this.worksheetPromptResultModel.findOne().limit(1).exec();
      this.logger.log('✅ MongoDB connection validated');

      // Test PostgreSQL connection
      await this.worksheetRepository.findOne({ where: {} });
      this.logger.log('✅ PostgreSQL connection validated');

    } catch (error) {
      this.logger.error('❌ Database connection validation failed');
      throw new Error(`Database connection failed: ${error.message}`);
    }
  }

  /**
   * Dry run to check what would be updated without making changes
   */
  async dryRun(): Promise<void> {
    this.logger.log('Starting dry run for worksheet question IDs migration...');

    try {
      const promptResults = await this.worksheetPromptResultModel.find({
        'promptResult.result': { $exists: true, $ne: [] }
      }).exec();

      this.logger.log(`Found ${promptResults.length} worksheet prompt results`);

      let needsUpdateCount = 0;
      let alreadyHasIdsCount = 0;
      let noQuestionsCount = 0;

      for (const promptResult of promptResults) {
        const worksheetId = promptResult.worksheetId;
        const questions = promptResult.promptResult?.result || [];

        if (!Array.isArray(questions) || questions.length === 0) {
          noQuestionsCount++;
          continue;
        }

        const hasIds = questions.every(q => q && q.id);
        if (hasIds) {
          alreadyHasIdsCount++;
        } else {
          needsUpdateCount++;
          this.logger.log(`Worksheet ${worksheetId}: ${questions.length} questions need IDs`);
        }
      }

      this.logger.log(`Dry run results:`);
      this.logger.log(`- Total worksheets: ${promptResults.length}`);
      this.logger.log(`- Need updates: ${needsUpdateCount}`);
      this.logger.log(`- Already have IDs: ${alreadyHasIdsCount}`);
      this.logger.log(`- No questions: ${noQuestionsCount}`);

    } catch (error) {
      this.logger.error(`Dry run failed: ${error.message}`, error.stack);
      throw error;
    }
  }
}
