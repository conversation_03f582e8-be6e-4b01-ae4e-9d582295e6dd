/**
 * Shared interfaces and types for Exercise Questions
 * Used across the application for worksheet question management
 */

/**
 * Enum for question types
 */
export enum EQuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  TRUE_FALSE = 'true_false',
  SHORT_ANSWER = 'short_answer',
  LONG_ANSWER = 'long_answer',
  FILL_IN_THE_BLANK = 'fill_in_the_blank',
  MATCHING = 'matching',
  ORDERING = 'ordering',
  CALCULATION = 'calculation',
  DIAGRAM = 'diagram',
  ESSAY = 'essay',
}

/**
 * Enum for question difficulty levels
 */
export enum EQuestionDifficulty {
  VERY_EASY = 'very_easy',
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  VERY_HARD = 'very_hard',
}

/**
 * Enum for question status
 */
export enum EQuestionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived',
  UNDER_REVIEW = 'under_review',
}

/**
 * Interface for question media references
 */
export interface IQuestionMedia {
  imageUrl?: string;
  imagePrompt?: string;
  audioUrl?: string;
  videoUrl?: string;
  attachmentUrls?: string[];
}

/**
 * Interface for question metadata
 */
export interface IQuestionMetadata {
  tags?: string[];
  keywords?: string[];
  estimatedTimeMinutes?: number;
  cognitiveLevel?: string; // Bloom's taxonomy level
  learningObjectives?: string[];
  prerequisites?: string[];
  hints?: string[];
  references?: string[];
  authorNotes?: string;
  reviewNotes?: string;
  lastReviewDate?: Date;
  nextReviewDate?: Date;
}

/**
 * Interface for question audit information
 */
export interface IQuestionAudit {
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // User ID
  updatedBy?: string; // User ID
  version: number;
  changeLog?: Array<{
    timestamp: Date;
    userId: string;
    action: string;
    changes: Record<string, any>;
    reason?: string;
  }>;
}

/**
 * Core Exercise Question interface
 * This is the main interface used throughout the application
 */
export interface IExerciseQuestion {
  // Core identification
  id?: string;
  
  // Question content
  type: EQuestionType;
  content: string;
  options: string[];
  answer: string[];
  explain: string;
  
  // Subject and topic information
  subject?: string;
  parentSubject?: string;
  childSubject?: string;
  topic?: string;
  subtopic?: string;
  
  // Academic information
  grade?: string;
  difficulty?: EQuestionDifficulty;
  
  // Media references
  media?: IQuestionMedia;
  
  // Legacy fields for backward compatibility
  imagePrompt?: string;
  imageUrl?: string;
  image?: string;
  
  // Status and visibility
  status?: EQuestionStatus;
  isPublic?: boolean;
  
  // School association for data isolation
  schoolId?: string;
  
  // Metadata
  metadata?: IQuestionMetadata;
  
  // Audit information
  audit?: IQuestionAudit;
  
  // Additional flexible fields
  [key: string]: any;
}

/**
 * Interface for question validation rules
 */
export interface IQuestionValidationRules {
  minOptions?: number;
  maxOptions?: number;
  minAnswers?: number;
  maxAnswers?: number;
  contentMaxLength?: number;
  explainMaxLength?: number;
  requiredFields?: string[];
}

/**
 * Interface for question search criteria
 */
export interface IQuestionSearchCriteria {
  type?: EQuestionType | EQuestionType[];
  subject?: string | string[];
  topic?: string | string[];
  grade?: string | string[];
  difficulty?: EQuestionDifficulty | EQuestionDifficulty[];
  status?: EQuestionStatus | EQuestionStatus[];
  schoolId?: string;
  tags?: string[];
  keywords?: string[];
  createdBy?: string;
  createdAfter?: Date;
  createdBefore?: Date;
  hasMedia?: boolean;
}

/**
 * Interface for question statistics
 */
export interface IQuestionStatistics {
  totalQuestions: number;
  questionsByType: Record<EQuestionType, number>;
  questionsByDifficulty: Record<EQuestionDifficulty, number>;
  questionsByGrade: Record<string, number>;
  questionsBySubject: Record<string, number>;
  averageDifficulty: number;
  questionsWithMedia: number;
  questionsWithoutMedia: number;
}

/**
 * Type alias for backward compatibility with existing code
 */
export type ExerciseQuestion = IExerciseQuestion;

/**
 * Type for question creation (without audit fields)
 */
export type CreateExerciseQuestion = Omit<IExerciseQuestion, 'id' | 'audit'>;

/**
 * Type for question updates (partial with required id)
 */
export type UpdateExerciseQuestion = Partial<IExerciseQuestion> & { id: string };

/**
 * Utility type for question with required fields
 */
export type RequiredExerciseQuestion = Required<Pick<IExerciseQuestion, 'type' | 'content' | 'options' | 'answer' | 'explain'>> & 
  Partial<Omit<IExerciseQuestion, 'type' | 'content' | 'options' | 'answer' | 'explain'>>;
