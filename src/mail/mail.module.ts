import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { Global, Module } from '@nestjs/common';
import { MailService } from './mail.service';
import { join } from 'path';
import { ConfigService } from '@nestjs/config';

@Global()
@Module({
  imports: [
    MailerModule.forRootAsync({
      useFactory: async (config: ConfigService) => {
        // Validate required mail configuration
        const mailHost = config.get('MAIL_HOST');
        const mailPort = config.get('MAIL_PORT');
        const mailFrom = config.get('MAIL_FROM');

        if (!mailHost || !mailPort || !mailFrom) {
          throw new Error('Mail configuration is incomplete. Please check MAIL_HOST, MAIL_PORT, and MAIL_FROM environment variables.');
        }

        return {
          transport: {
            host: mailHost,
            port: +mailPort,
            secure: mailPort === '465',
            auth: {
              user: config.get('MAIL_USER'),
              pass: config.get('MAIL_PASSWORD'),
            },
          },
          defaults: {
            from: mailFrom,
          },
          template: {
            dir: join(process.cwd(), 'src', 'mail', 'templates'),
            adapter: new HandlebarsAdapter(),
            options: {
              strict: true,
            },
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [MailService],
  exports: [MailService],
})
export class MailModule {}
