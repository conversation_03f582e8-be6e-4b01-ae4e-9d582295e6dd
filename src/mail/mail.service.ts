import { MailerService } from '@nestjs-modules/mailer';
import { Injectable, Logger, InternalServerErrorException } from '@nestjs/common';
import { User } from 'src/modules/user/entities/user.entity';

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);

  constructor(private mailerService: MailerService) {}

  async sendUserPasswordReset(user: User, resetUrl: string): Promise<void> {
    try {
      this.logger.log(`Sending password reset email to ${user.email}`);

      await this.mailerService.sendMail({
        to: user.email,
        subject: 'Reset your password',
        template: 'reset-password', // `.hbs` extension is appended automatically
        context: {
          name: user.name,
          url: resetUrl,
        },
      });

      this.logger.log(`Password reset email sent successfully to ${user.email}`);
    } catch (error) {
      this.logger.error(`Failed to send password reset email to ${user.email}`, error.stack);
      throw new InternalServerErrorException('Failed to send password reset email. Please try again later.');
    }
  }
}
