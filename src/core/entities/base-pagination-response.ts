import { ApiProperty } from '@nestjs/swagger';

/**
 * Base class for pagination responses
 * Provides standard pagination response structure
 * Used for returning paginated data from API endpoints
 */
export class BasePaginationResponse<T> {
  @ApiProperty({
    description: 'Array of items for the current page',
    isArray: true,
  })
  items: T[];

  @ApiProperty({
    description: 'Current page number',
    example: 1,
    type: Number,
  })
  currentPage: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    type: Number,
  })
  pageSize: number;

  @ApiProperty({
    description: 'Total number of items across all pages',
    example: 100,
    type: Number,
  })
  totalItems: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10,
    type: Number,
  })
  totalPages: number;

  constructor(
    items: T[],
    currentPage: number,
    pageSize: number,
    totalItems: number,
    totalPages: number,
  ) {
    this.items = items;
    this.currentPage = currentPage;
    this.pageSize = pageSize;
    this.totalItems = totalItems;
    this.totalPages = totalPages;
  }
}
