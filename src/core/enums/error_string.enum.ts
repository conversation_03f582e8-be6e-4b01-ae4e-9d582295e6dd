export enum EErrorString {
  WRONG_CREDENTIAL = 'Wrong credentials.',
  NOT_EXIST = 'The requested resource does not exist.',
  EXISTED = 'The requested resource was existed.',
  USER_NOT_EXIST = 'User is not exist',
  EMAIL_ALREADY_EXIST = 'Email already existed',
  PASSWORD_NOT_MATCH = 'Password does not match',
  BANK_NOT_EXIST = 'Bank is not exist',
  PAYMENT_ACCOUNT_NOT_EXIST = 'Payment account is not exist',
  PAYMENT_ACCOUNT_ALREADY_EXIST = 'Payment account already existed',
  PROJECT_ID_REQUIRED = 'ProjectId is required',
}
