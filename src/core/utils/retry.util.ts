import { Logger } from '@nestjs/common';

export interface RetryOptions {
  maxRetries?: number;
  initialDelay?: number;
  maxDelay?: number;
  backoffMultiplier?: number;
  jitter?: boolean;
  retryCondition?: (error: any) => boolean;
  onRetry?: (error: any, attempt: number, delay: number) => void;
}

export interface RetryResult<T> {
  result?: T;
  success: boolean;
  attempts: number;
  totalTime: number;
  lastError?: any;
}

/**
 * Utility function to retry operations with exponential backoff
 * Based on existing patterns in embedding and documents services
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = {},
  logger?: Logger
): Promise<RetryResult<T>> {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    maxDelay = 10000,
    backoffMultiplier = 2,
    jitter = true,
    retryCondition = () => true,
    onRetry,
  } = options;

  const startTime = Date.now();
  let attempts = 0;
  let delay = initialDelay;
  let lastError: any;

  while (attempts <= maxRetries) {
    try {
      const result = await operation();
      const totalTime = Date.now() - startTime;
      
      if (logger && attempts > 0) {
        logger.log(`Operation succeeded after ${attempts} retries in ${totalTime}ms`);
      }
      
      return {
        result,
        success: true,
        attempts: attempts + 1,
        totalTime,
      };
    } catch (error) {
      lastError = error;
      attempts++;

      // Check if we should retry this error
      if (!retryCondition(error)) {
        if (logger) {
          logger.error(`Operation failed with non-retryable error: ${error.message}`);
        }
        break;
      }

      // Check if we've exhausted retries
      if (attempts > maxRetries) {
        if (logger) {
          logger.error(`Operation failed after ${maxRetries} retries: ${error.message}`);
        }
        break;
      }

      // Calculate delay with jitter
      let currentDelay = Math.min(delay, maxDelay);
      if (jitter) {
        currentDelay = currentDelay + Math.random() * 1000;
      }

      if (logger) {
        logger.warn(
          `Operation failed (attempt ${attempts}/${maxRetries + 1}), retrying in ${Math.round(currentDelay)}ms: ${error.message}`
        );
      }

      // Call onRetry callback if provided
      if (onRetry) {
        onRetry(error, attempts, currentDelay);
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, currentDelay));

      // Increase delay for next attempt
      delay = Math.min(delay * backoffMultiplier, maxDelay);
    }
  }

  const totalTime = Date.now() - startTime;
  return {
    success: false,
    attempts,
    totalTime,
    lastError,
  };
}

/**
 * Decorator for adding retry logic to class methods
 */
export function Retry(options: RetryOptions = {}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const logger = this.logger || new Logger(target.constructor.name);
      
      const result = await withRetry(
        () => method.apply(this, args),
        options,
        logger
      );

      if (!result.success) {
        throw result.lastError;
      }

      return result.result;
    };
  };
}

/**
 * Common retry conditions for different types of errors
 */
export const RetryConditions = {
  /**
   * Retry on database connection errors and timeouts
   */
  database: (error: any): boolean => {
    const message = error.message?.toLowerCase() || '';
    return (
      message.includes('connection') ||
      message.includes('timeout') ||
      message.includes('network') ||
      message.includes('econnreset') ||
      message.includes('enotfound') ||
      message.includes('econnrefused') ||
      error.code === 'ETIMEDOUT' ||
      error.code === 'ECONNRESET' ||
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED'
    );
  },

  /**
   * Retry on AI service rate limiting and temporary errors
   */
  aiService: (error: any): boolean => {
    const message = error.message?.toLowerCase() || '';
    const status = error.status || error.response?.status;
    
    return (
      status === 429 || // Rate limit
      status === 502 || // Bad gateway
      status === 503 || // Service unavailable
      status === 504 || // Gateway timeout
      message.includes('rate limit') ||
      message.includes('quota') ||
      message.includes('too many requests') ||
      message.includes('timeout') ||
      message.includes('network')
    );
  },

  /**
   * Retry on network-related errors
   */
  network: (error: any): boolean => {
    const message = error.message?.toLowerCase() || '';
    return (
      message.includes('network') ||
      message.includes('timeout') ||
      message.includes('connection') ||
      error.code === 'ETIMEDOUT' ||
      error.code === 'ECONNRESET' ||
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED'
    );
  },

  /**
   * Never retry (for testing or specific use cases)
   */
  never: (): boolean => false,

  /**
   * Always retry (default behavior)
   */
  always: (): boolean => true,
};
