import { registerAs } from '@nestjs/config';
import { configService } from '../env/env.config';

export default registerAs('jwt', () => {
  return {
    secret: process.env.JWT_SECRET, // Ensure this reads the secret from .env
    audience: configService.get('JWT_TOKEN_AUDIENCE'),
    issuer: configService.get('JWT_TOKEN_ISSUER'),
    accessTokenTtl: parseInt(
      configService.get('JWT_ACCESS_TOKEN_TTL') ?? '31557600000',
      10,
    ),
    refreshTokenTtl: parseInt(
      configService.get('JWT_REFRESH_TOKEN_TTL') ?? '31557600000',
      10,
    ),
    monoSession: configService.get('MONO_SESSION') === 'true',
    signOptions: { expiresIn: process.env.JWT_EXPIRATION || '30d' },
  };
});
