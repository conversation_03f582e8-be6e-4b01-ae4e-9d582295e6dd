import { registerAs } from '@nestjs/config';
import { DataSource, DataSourceOptions } from 'typeorm';
import { configService } from '../env/env.config';

const config: DataSourceOptions = {
  type: 'postgres',
  host: `${configService.get('DB_HOST')}`,
  port: +configService.get('DB_PORT'),
  username: `${configService.get('DB_USER')}`,
  password: `${configService.get('DB_PASSWORD')}`,
  database: `${configService.get('DB_NAME')}`,
  entities: ['dist/**/*.entity{.ts,.js}'],
  migrations: ['dist/migrations/*{.ts,.js}'],
  synchronize: false,
};

export default registerAs('typeorm', () => config);
export const connectionSource = new DataSource(config as DataSourceOptions);
