import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();

    const logDetails = {
      method: request.method,
      endpoint: request.url,
      query: JSON.stringify(request.query),
      body: JSON.stringify(request.body),
      ip: request.ip,
      statusCode: HttpStatus.OK,
      message: undefined,
    };

    const logMessage = `Method: ${logDetails.method} | Endpoint: ${
      logDetails.endpoint
    } | Query: ${logDetails.query} | Body: ${logDetails.body} | IP: ${
      logDetails.ip
    }`;

    this.logger.log(`Request Info: ${logMessage}`);
  }
}
