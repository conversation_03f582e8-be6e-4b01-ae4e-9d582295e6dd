import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseResponse } from '../entities/base-response';

@Injectable()
export class ResponseFormatInterceptor<T>
  implements NestInterceptor<T, BaseResponse<T>>
{
  constructor(private reflector: Reflector) {}

  private getSuccessState(statusCode: number): boolean {
    return statusCode >= 200 && statusCode < 300;
  }

  private getMessage(context: ExecutionContext, success: boolean): string {
    const customMessage = this.reflector.get<string>(
      'response_message',
      context.getHandler(),
    );
    return customMessage || (success ? 'Success' : 'Error');
  }

  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<BaseResponse<T>> {
    return next.handle().pipe(
      map((data) => {
        // Check if the response is already a BaseResponse object
        if (data instanceof BaseResponse) {
          return data;
        }

        const statusCode = context.switchToHttp().getResponse().statusCode;
        const success = this.getSuccessState(statusCode);
        const message = this.getMessage(context, success);

        return new BaseResponse(statusCode, message, data ?? null);
      }),
    );
  }
}
