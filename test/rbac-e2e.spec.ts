import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtService } from '@nestjs/jwt';
import { EUserRole } from '../src/modules/user/dto/create-user.dto';

describe('RBAC End-to-End Tests', () => {
  let app: INestApplication;
  let jwtService: JwtService;

  // Mock user tokens for different roles
  let adminToken: string;
  let schoolManagerToken: string;
  let teacherToken: string;
  let studentToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        // Import your AppModule here
        // AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);

    // Create test tokens for different user roles
    adminToken = jwtService.sign({
      sub: 'admin-1',
      email: '<EMAIL>',
      role: EUserRole.ADMIN,
    });

    schoolManagerToken = jwtService.sign({
      sub: 'manager-1',
      email: '<EMAIL>',
      role: EUserRole.SCHOOL_MANAGER,
      schoolId: 'school-1',
    });

    teacherToken = jwtService.sign({
      sub: 'teacher-1',
      email: '<EMAIL>',
      role: EUserRole.TEACHER,
      schoolId: 'school-1',
    });

    studentToken = jwtService.sign({
      sub: 'student-1',
      email: '<EMAIL>',
      role: EUserRole.STUDENT,
      schoolId: 'school-1',
    });

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('User Management Endpoints', () => {
    describe('POST /user', () => {
      const createUserDto = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        role: EUserRole.TEACHER,
        schoolId: 'school-1',
      };

      it('should allow admin to create users', () => {
        return request(app.getHttpServer())
          .post('/user')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(createUserDto)
          .expect(201);
      });

      it('should allow school manager to create users for their school', () => {
        return request(app.getHttpServer())
          .post('/user')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .send(createUserDto)
          .expect(201);
      });

      it('should deny teacher access to create users', () => {
        return request(app.getHttpServer())
          .post('/user')
          .set('Authorization', `Bearer ${teacherToken}`)
          .send(createUserDto)
          .expect(403);
      });

      it('should deny student access to create users', () => {
        return request(app.getHttpServer())
          .post('/user')
          .set('Authorization', `Bearer ${studentToken}`)
          .send(createUserDto)
          .expect(403);
      });

      it('should deny unauthenticated access', () => {
        return request(app.getHttpServer())
          .post('/user')
          .send(createUserDto)
          .expect(401);
      });

      it('should prevent school manager from creating admin users', () => {
        const adminUserDto = { ...createUserDto, role: EUserRole.ADMIN };

        return request(app.getHttpServer())
          .post('/user')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .send(adminUserDto)
          .expect(403)
          .expect((res) => {
            expect(res.body.code).toBe('RBAC_CANNOT_CREATE_ADMIN');
          });
      });

      it('should prevent school manager from creating users for different school', () => {
        const differentSchoolUserDto = { ...createUserDto, schoolId: 'school-2' };

        return request(app.getHttpServer())
          .post('/user')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .send(differentSchoolUserDto)
          .expect(403)
          .expect((res) => {
            expect(res.body.code).toBe('RBAC_CANNOT_TRANSFER_USERS');
          });
      });
    });

    describe('GET /user', () => {
      it('should allow admin to list all users', () => {
        return request(app.getHttpServer())
          .get('/user')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);
      });

      it('should allow school manager to list users from their school', () => {
        return request(app.getHttpServer())
          .get('/user')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .expect(200);
      });

      it('should deny teacher access to list users', () => {
        return request(app.getHttpServer())
          .get('/user')
          .set('Authorization', `Bearer ${teacherToken}`)
          .expect(403);
      });

      it('should deny student access to list users', () => {
        return request(app.getHttpServer())
          .get('/user')
          .set('Authorization', `Bearer ${studentToken}`)
          .expect(403);
      });
    });

    describe('GET /user/me', () => {
      it('should allow all authenticated users to access their own profile', () => {
        const tokens = [adminToken, schoolManagerToken, teacherToken, studentToken];

        return Promise.all(
          tokens.map((token) =>
            request(app.getHttpServer())
              .get('/user/me')
              .set('Authorization', `Bearer ${token}`)
              .expect(200),
          ),
        );
      });

      it('should deny unauthenticated access to profile', () => {
        return request(app.getHttpServer())
          .get('/user/me')
          .expect(401);
      });
    });
  });

  describe('School Management Endpoints', () => {
    describe('DELETE /schools/:id', () => {
      it('should allow admin to delete schools', () => {
        return request(app.getHttpServer())
          .delete('/schools/school-1')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);
      });

      it('should deny school manager access to delete schools', () => {
        return request(app.getHttpServer())
          .delete('/schools/school-1')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .expect(403);
      });

      it('should deny teacher access to delete schools', () => {
        return request(app.getHttpServer())
          .delete('/schools/school-1')
          .set('Authorization', `Bearer ${teacherToken}`)
          .expect(403);
      });
    });

    describe('GET /schools/:id', () => {
      it('should allow admin to access any school', () => {
        return request(app.getHttpServer())
          .get('/schools/school-1')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);
      });

      it('should allow school manager to access their own school', () => {
        return request(app.getHttpServer())
          .get('/schools/school-1')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .expect(200);
      });

      it('should allow teacher to access their own school', () => {
        return request(app.getHttpServer())
          .get('/schools/school-1')
          .set('Authorization', `Bearer ${teacherToken}`)
          .expect(200);
      });

      it('should prevent school manager from accessing different school', () => {
        return request(app.getHttpServer())
          .get('/schools/school-2')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .expect(403)
          .expect((res) => {
            expect(res.body.code).toBe('RBAC_SCHOOL_ACCESS_DENIED');
          });
      });

      it('should deny student access to school information', () => {
        return request(app.getHttpServer())
          .get('/schools/school-1')
          .set('Authorization', `Bearer ${studentToken}`)
          .expect(403);
      });
    });

    describe('GET /schools/:schoolId/examination-format', () => {
      it('should allow admin to access examination formats', () => {
        return request(app.getHttpServer())
          .get('/schools/school-1/examination-format')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);
      });

      it('should deny school manager access to examination formats', () => {
        return request(app.getHttpServer())
          .get('/schools/school-1/examination-format')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .expect(403);
      });

      it('should deny teacher access to examination formats', () => {
        return request(app.getHttpServer())
          .get('/schools/school-1/examination-format')
          .set('Authorization', `Bearer ${teacherToken}`)
          .expect(403);
      });
    });
  });

  describe('Monitoring Endpoints', () => {
    describe('GET /admin/monitoring/dashboard', () => {
      it('should allow admin to access monitoring dashboard', () => {
        return request(app.getHttpServer())
          .get('/admin/monitoring/dashboard')
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);
      });

      it('should deny school manager access to monitoring', () => {
        return request(app.getHttpServer())
          .get('/admin/monitoring/dashboard')
          .set('Authorization', `Bearer ${schoolManagerToken}`)
          .expect(403);
      });

      it('should deny teacher access to monitoring', () => {
        return request(app.getHttpServer())
          .get('/admin/monitoring/dashboard')
          .set('Authorization', `Bearer ${teacherToken}`)
          .expect(403);
      });

      it('should deny student access to monitoring', () => {
        return request(app.getHttpServer())
          .get('/admin/monitoring/dashboard')
          .set('Authorization', `Bearer ${studentToken}`)
          .expect(403);
      });
    });
  });

  describe('Error Response Format', () => {
    it('should return standardized error format for 403 responses', () => {
      return request(app.getHttpServer())
        .post('/user')
        .set('Authorization', `Bearer ${teacherToken}`)
        .send({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'password',
          role: EUserRole.TEACHER,
        })
        .expect(403)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body).toHaveProperty('error', 'Forbidden');
          expect(res.body).toHaveProperty('statusCode', 403);
          expect(res.body).toHaveProperty('code');
          expect(res.body).toHaveProperty('timestamp');
        });
    });

    it('should return standardized error format for 401 responses', () => {
      return request(app.getHttpServer())
        .get('/user')
        .expect(401)
        .expect((res) => {
          expect(res.body).toHaveProperty('message');
          expect(res.body).toHaveProperty('error', 'Unauthorized');
          expect(res.body).toHaveProperty('statusCode', 401);
          expect(res.body).toHaveProperty('code');
          expect(res.body).toHaveProperty('timestamp');
        });
    });
  });
});
