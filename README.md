# EduSG Backend

![NestJS](https://img.shields.io/badge/NestJS-E0234E?style=for-the-badge&logo=nestjs&logoColor=white)
![TypeScript](https://img.shields.io/badge/TypeScript-3178C6?style=for-the-badge&logo=typescript&logoColor=white)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-4169E1?style=for-the-badge&logo=postgresql&logoColor=white)
![Redis](https://img.shields.io/badge/Redis-DC382D?style=for-the-badge&logo=redis&logoColor=white)
![Docker](https://img.shields.io/badge/Docker-2496ED?style=for-the-badge&logo=docker&logoColor=white)

## Overview

EduSG is an AI-powered educational assistance platform designed for students and teachers in Singapore. The system processes educational documents, including examination materials with images, and provides intelligent responses to user queries based on the processed content.

### Key Features

- **Document Processing**: Upload and process educational documents (PDFs)
- **Multimodal Understanding**: Process documents with both text and images using the Qwen 2.5 VL model
- **Intelligent Querying**: Ask questions about uploaded documents and get AI-powered responses
- **Examination Support**: Special handling for examination content with diagrams and visual elements
- **User Management**: Authentication and user role management

## Quick Start

### Prerequisites

- [Docker](https://www.docker.com/get-started) and [Docker Compose](https://docs.docker.com/compose/install/)
- [Node.js](https://nodejs.org/) (v18 or higher)
- [npm](https://www.npmjs.com/) or [Yarn](https://yarnpkg.com/)
- [poppler-utils](https://poppler.freedesktop.org/) for PDF processing

### Installation

1. Clone the repository
   ```bash
   git clone <repository-url>
   cd edusg-be
   ```

2. Install dependencies
   ```bash
   npm install
   # or
   yarn install
   ```

3. Set up environment variables
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. Start required services
   ```bash
   docker-compose up -d
   ```

5. Start the application
   ```bash
   npm run start:dev
   # or
   yarn start:dev
   ```

6. Access the API at http://localhost:3000
   - Interactive API documentation available at http://localhost:3000/api

## Documentation

Comprehensive documentation is available in the [docs](./docs) directory:

- [Documentation Table of Contents](./docs/README.md)
- [System Overview](./docs/system-overview.md)
- [Installation Guide](./docs/installation-guide.md)
- [API Documentation](./docs/api-documentation.md) (also available at http://localhost:3000/api when running)

## Architecture

EduSG Backend is built with:

- **NestJS**: A progressive Node.js framework for building efficient and scalable server-side applications
- **PostgreSQL**: Primary database for user data and application state
- **Redis**: For caching and message queuing
- **Vector Database**: Pinecone/Qdrant for document embeddings and semantic search
- **AI Models**: Integration with Qwen 2.5 VL for multimodal understanding and OpenAI/Gemini for response generation

## Development

### Running Tests

```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:cov
```

### Linting

```bash
npm run lint
```

### Building for Production

```bash
npm run build
npm run start:prod
```

## Troubleshooting

View all service logs:
```bash
docker-compose logs
```

View specific service logs:
```bash
docker-compose logs <service-name>
```

For more troubleshooting information, see the [Installation Guide](./docs/installation-guide.md).
