services:
  # PostgreSQL database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
      POSTGRES_DB: ${DB_NAME:-mydatabase}
    ports:
      - '${DB_PORT:-5432}:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  # Redis for caching/messaging
  redis:
    image: redis:7-alpine
    ports:
      - '${REDIS_PORT:-6379}:6379'
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # Qdrant vector database
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - '${QDRANT_PORT:-6333}:6333' # REST API
      - '${QDRANT_GRPC_PORT:-6334}:6334' # gRPC API
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT_ALLOW_CORS=${QDRANT_ALLOW_CORS:-true}
    restart: unless-stopped

  mongodb:
    image: mongo:latest
    ports:
      - '27017:27017'
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_INITDB_ROOT_USERNAME:-root}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD:-password}

volumes:
  postgres_data:
  redis_data:
  qdrant_data:
  nestjs_node_modules:
  mongodb_data:
