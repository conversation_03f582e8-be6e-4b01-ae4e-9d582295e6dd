import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateExamModule1746517537814 implements MigrationInterface {
    name = 'CreateExamModule1746517537814'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "exam_questions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "examId" character varying NOT NULL, "index" integer NOT NULL, "type" character varying(32) NOT NULL, "content" character varying NOT NULL, "options" jsonb, "answer" jsonb, "userAnswer" jsonb, "isCorrect" boolean, "image" character varying, "explain" character varying, CONSTRAINT "PK_a214d47c7964cb6356f413dc73c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."exams_status_enum" AS ENUM('in_progress', 'completed', 'cancelled')`);
        await queryRunner.query(`CREATE TABLE "exams" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "userId" uuid NOT NULL, "worksheetId" uuid NOT NULL, "title" character varying(255) NOT NULL, "description" character varying, "selectedOptions" jsonb, "status" "public"."exams_status_enum" NOT NULL DEFAULT 'in_progress', "questions" jsonb, "maxAttempts" integer NOT NULL DEFAULT '1', CONSTRAINT "PK_b43159ee3efa440952794b4f53e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TYPE "public"."users_role_enum" RENAME TO "users_role_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."users_role_enum" AS ENUM('teacher', 'admin', 'student')`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "role" TYPE "public"."users_role_enum" USING "role"::"text"::"public"."users_role_enum"`);
        await queryRunner.query(`DROP TYPE "public"."users_role_enum_old"`);
        await queryRunner.query(`ALTER TABLE "exams" ADD CONSTRAINT "FK_5ec7ff70b19e78f3d412addad4f" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "exams" ADD CONSTRAINT "FK_5a2c32e0531feca0e832f4e6a8a" FOREIGN KEY ("worksheetId") REFERENCES "worksheets"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "exams" DROP CONSTRAINT "FK_5a2c32e0531feca0e832f4e6a8a"`);
        await queryRunner.query(`ALTER TABLE "exams" DROP CONSTRAINT "FK_5ec7ff70b19e78f3d412addad4f"`);
        await queryRunner.query(`CREATE TYPE "public"."users_role_enum_old" AS ENUM('teacher', 'admin')`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "role" TYPE "public"."users_role_enum_old" USING "role"::"text"::"public"."users_role_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."users_role_enum_old" RENAME TO "users_role_enum"`);
        await queryRunner.query(`DROP TABLE "exams"`);
        await queryRunner.query(`DROP TYPE "public"."exams_status_enum"`);
        await queryRunner.query(`DROP TABLE "exam_questions"`);
    }

}
