import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSchoolAndBrandEntities1747000000000 implements MigrationInterface {
  name = 'AddSchoolAndBrandEntities1747000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing enum and recreate it with the new values
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "role"`);
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
    await queryRunner.query(
      `CREATE TYPE "public"."users_role_enum" AS ENUM('teacher', 'admin', 'student', 'school_manager')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD "role" "public"."users_role_enum" DEFAULT 'teacher'`,
    );
    await queryRunner.query(
      `UPDATE "users" SET "role" = 'teacher' WHERE "role" IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "role" SET NOT NULL`,
    );

    // Create brands table
    await queryRunner.query(
      `CREATE TABLE "brands" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(), 
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), 
        "deletedAt" TIMESTAMP, 
        "logo" character varying, 
        "color" character varying, 
        "image" character varying, 
        CONSTRAINT "PK_brands" PRIMARY KEY ("id")
      )`,
    );

    // Create schools table
    await queryRunner.query(
      `CREATE TABLE "schools" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(), 
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(), 
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), 
        "deletedAt" TIMESTAMP, 
        "name" character varying NOT NULL, 
        "address" character varying NOT NULL, 
        "phoneNumber" character varying NOT NULL, 
        "registeredNumber" character varying NOT NULL, 
        "email" character varying NOT NULL, 
        "adminId" uuid, 
        "brandId" uuid, 
        CONSTRAINT "PK_schools" PRIMARY KEY ("id")
      )`,
    );

    // Add schoolId column to users table
    await queryRunner.query(
      `ALTER TABLE "users" ADD "schoolId" uuid`,
    );

    // Add foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "schools" ADD CONSTRAINT "FK_schools_admin" FOREIGN KEY ("adminId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "schools" ADD CONSTRAINT "FK_schools_brand" FOREIGN KEY ("brandId") REFERENCES "brands"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_users_school" FOREIGN KEY ("schoolId") REFERENCES "schools"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_users_school"`,
    );
    await queryRunner.query(
      `ALTER TABLE "schools" DROP CONSTRAINT "FK_schools_brand"`,
    );
    await queryRunner.query(
      `ALTER TABLE "schools" DROP CONSTRAINT "FK_schools_admin"`,
    );

    // Remove schoolId column from users table
    await queryRunner.query(
      `ALTER TABLE "users" DROP COLUMN "schoolId"`,
    );

    // Drop schools table
    await queryRunner.query(`DROP TABLE "schools"`);

    // Drop brands table
    await queryRunner.query(`DROP TABLE "brands"`);

    // Revert the enum to its original state
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "role"`);
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
    await queryRunner.query(
      `CREATE TYPE "public"."users_role_enum" AS ENUM('teacher', 'admin')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD "role" "public"."users_role_enum" NOT NULL`,
    );
  }
}
