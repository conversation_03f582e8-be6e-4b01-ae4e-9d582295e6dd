import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitMigration1745392493278 implements MigrationInterface {
  name = 'InitMigration1745392493278';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "option_values" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "label" character varying NOT NULL, "value" character varying NOT NULL, "order" integer NOT NULL DEFAULT '0', "optionTypeId" uuid NOT NULL, CONSTRAINT "PK_832b9dfff8b853260189e4d0645" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "option_types" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "key" character varying NOT NULL, "label" character varying NOT NULL, "description" character varying NOT NULL, CONSTRAINT "PK_c398e2353f3f8d5088698235916" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "worksheet_options" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "text" character varying, "worksheetId" uuid, "optionTypeId" uuid, "optionValueId" uuid, CONSTRAINT "PK_254c3874ffa0cff4fb6edbb7b86" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "worksheets" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "title" character varying, "description" character varying, CONSTRAINT "PK_ec9769b3b2dbad7eb9915e0be3e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."users_role_enum" AS ENUM('teacher', 'admin')`,
    );
    await queryRunner.query(
      `CREATE TABLE "users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "name" character varying(100) NOT NULL, "email" character varying NOT NULL, "role" "public"."users_role_enum" NOT NULL, "password" character varying NOT NULL, CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "option_values" ADD CONSTRAINT "FK_f20deff5afc784b67152712eac7" FOREIGN KEY ("optionTypeId") REFERENCES "option_types"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "worksheet_options" ADD CONSTRAINT "FK_48363520260f7dbd2a8c3884a99" FOREIGN KEY ("worksheetId") REFERENCES "worksheets"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "worksheet_options" ADD CONSTRAINT "FK_d45edc7b6183ddbe91d9f942b45" FOREIGN KEY ("optionTypeId") REFERENCES "option_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "worksheet_options" ADD CONSTRAINT "FK_b465316d0284b9272eb290b8fdb" FOREIGN KEY ("optionValueId") REFERENCES "option_values"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "worksheet_options" DROP CONSTRAINT "FK_b465316d0284b9272eb290b8fdb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "worksheet_options" DROP CONSTRAINT "FK_d45edc7b6183ddbe91d9f942b45"`,
    );
    await queryRunner.query(
      `ALTER TABLE "worksheet_options" DROP CONSTRAINT "FK_48363520260f7dbd2a8c3884a99"`,
    );
    await queryRunner.query(
      `ALTER TABLE "option_values" DROP CONSTRAINT "FK_f20deff5afc784b67152712eac7"`,
    );
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(`DROP TYPE "public"."users_role_enum"`);
    await queryRunner.query(`DROP TABLE "worksheets"`);
    await queryRunner.query(`DROP TABLE "worksheet_options"`);
    await queryRunner.query(`DROP TABLE "option_types"`);
    await queryRunner.query(`DROP TABLE "option_values"`);
  }
}
