import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSubscriptionAndPackageTables1751443829172 implements MigrationInterface {
    name = 'AddSubscriptionAndPackageTables1751443829172'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Drop the existing index that was originally in this migration (if it exists)
        try {
            await queryRunner.query(`DROP INDEX IF EXISTS "public"."IDX_USER_PASSWORD_RESET_TOKEN"`);
        } catch (error) {
            // Index might not exist, continue with migration
            console.log('Index IDX_USER_PASSWORD_RESET_TOKEN does not exist, continuing...');
        }

        // Create package table
        await queryRunner.query(`
            CREATE TABLE "package" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "deletedAt" TIMESTAMP,
                "name" character varying NOT NULL,
                "description" character varying NOT NULL,
                "image" character varying NOT NULL,
                "stripeProductId" character varying NOT NULL,
                CONSTRAINT "PK_package" PRIMARY KEY ("id"),
                CONSTRAINT "UQ_package_stripeProductId" UNIQUE ("stripeProductId")
            )
        `);

        // Create prices table
        await queryRunner.query(`
            CREATE TABLE "prices" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "stripePriceId" character varying NOT NULL,
                "currency" character varying NOT NULL,
                "unitAmount" integer NOT NULL,
                "nickname" character varying,
                "active" boolean NOT NULL DEFAULT false,
                "type" character varying,
                "interval" character varying,
                "intervalCount" integer,
                "trialPeriodDays" integer,
                "usageType" character varying,
                "packageId" uuid,
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_prices" PRIMARY KEY ("id"),
                CONSTRAINT "UQ_prices_stripePriceId" UNIQUE ("stripePriceId")
            )
        `);

        // Create subscriptions table
        await queryRunner.query(`
            CREATE TABLE "subscriptions" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "user_id" character varying NOT NULL,
                "stripe_subscription_id" character varying NOT NULL,
                "stripe_customer_id" character varying NOT NULL,
                "stripe_price_id" character varying NOT NULL,
                "stripe_product_id" character varying NOT NULL,
                "start_date" TIMESTAMP NOT NULL,
                "current_period_start" TIMESTAMP NOT NULL,
                "current_period_end" TIMESTAMP,
                "cancel_at" TIMESTAMP,
                "canceled_at" TIMESTAMP,
                "ended_at" TIMESTAMP,
                "cancel_at_period_end" boolean NOT NULL DEFAULT false,
                "status" character varying NOT NULL,
                "price_unit_amount" integer NOT NULL,
                "currency" character varying NOT NULL,
                "metadata" jsonb,
                "latest_invoice_id" character varying,
                "payment_method" character varying,
                "raw_data" jsonb,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_subscriptions" PRIMARY KEY ("id"),
                CONSTRAINT "UQ_subscriptions_stripe_subscription_id" UNIQUE ("stripe_subscription_id")
            )
        `);

        // Add foreign key constraint for prices -> package
        await queryRunner.query(`
            ALTER TABLE "prices" 
            ADD CONSTRAINT "FK_prices_package" 
            FOREIGN KEY ("packageId") 
            REFERENCES "package"("id") 
            ON DELETE CASCADE
        `);

        // Create indexes
        await queryRunner.query(`CREATE INDEX "IDX_subscriptions_user_id" ON "subscriptions" ("user_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_package_stripeProductId" ON "package" ("stripeProductId")`);
        await queryRunner.query(`CREATE INDEX "IDX_prices_stripePriceId" ON "prices" ("stripePriceId")`);
        await queryRunner.query(`CREATE INDEX "IDX_prices_packageId" ON "prices" ("packageId")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes (if they exist)
        try {
            await queryRunner.query(`DROP INDEX IF EXISTS "IDX_prices_packageId"`);
            await queryRunner.query(`DROP INDEX IF EXISTS "IDX_prices_stripePriceId"`);
            await queryRunner.query(`DROP INDEX IF EXISTS "IDX_package_stripeProductId"`);
            await queryRunner.query(`DROP INDEX IF EXISTS "IDX_subscriptions_user_id"`);
        } catch (error) {
            console.log('Some indexes might not exist, continuing...');
        }

        // Drop foreign key constraint (if it exists)
        try {
            await queryRunner.query(`ALTER TABLE "prices" DROP CONSTRAINT IF EXISTS "FK_prices_package"`);
        } catch (error) {
            console.log('Foreign key constraint might not exist, continuing...');
        }

        // Drop tables (if they exist)
        await queryRunner.query(`DROP TABLE IF EXISTS "subscriptions"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "prices"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "package"`);

        // Recreate the original index (if it doesn't exist)
        try {
            await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_USER_PASSWORD_RESET_TOKEN" ON "users" ("passwordResetToken") WHERE ("passwordResetToken" IS NOT NULL)`);
        } catch (error) {
            console.log('Could not recreate original index, might already exist...');
        }
    }
}
