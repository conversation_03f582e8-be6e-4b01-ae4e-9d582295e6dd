import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddQuestionManagementToWorksheets1750000000000 implements MigrationInterface {
  name = 'AddQuestionManagementToWorksheets1750000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add question management columns to worksheets table
    await queryRunner.addColumns('worksheets', [
      new TableColumn({
        name: 'questions',
        type: 'jsonb',
        isNullable: true,
        comment: 'Array of exercise questions (JSONB format)'
      }),
      new TableColumn({
        name: 'totalQuestions',
        type: 'integer',
        default: 0,
        comment: 'Total number of questions in the worksheet'
      }),
      new TableColumn({
        name: 'lastModifiedBy',
        type: 'varchar',
        isNullable: true,
        comment: 'User ID who last modified questions'
      }),
      new TableColumn({
        name: 'createdBy',
        type: 'varchar',
        isNullable: true,
        comment: 'User ID who created the worksheet'
      }),
      new TableColumn({
        name: 'maxQuestions',
        type: 'integer',
        default: 100,
        comment: 'Maximum number of questions allowed in the worksheet'
      }),
      new TableColumn({
        name: 'questionIds',
        type: 'jsonb',
        isNullable: true,
        comment: 'Array of MongoDB ObjectIds as strings (references to WorksheetQuestionDocument)'
      }),
      new TableColumn({
        name: 'questionMetadata',
        type: 'jsonb',
        isNullable: true,
        comment: 'Question management metadata including version, lock status, collaborators'
      })
    ]);

    // Create indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_WORKSHEET_SCHOOL_UPDATED"
      ON "worksheets" ("schoolId", "updatedAt")
    `);
    await queryRunner.query(`
      CREATE INDEX "IDX_WORKSHEET_SCHOOL_TOTAL_QUESTIONS"
      ON "worksheets" ("schoolId", "totalQuestions")
    `);
    await queryRunner.query(`
      CREATE INDEX "IDX_WORKSHEET_LAST_MODIFIED"
      ON "worksheets" ("lastModifiedBy", "updatedAt")
    `);

    // Add a partial index for worksheets with questions
    await queryRunner.query(`
      CREATE INDEX "IDX_WORKSHEET_WITH_QUESTIONS" 
      ON "worksheets" ("totalQuestions") 
      WHERE "totalQuestions" > 0
    `);

    // Add a GIN index for JSONB question data for better query performance
    await queryRunner.query(`
      CREATE INDEX "IDX_WORKSHEET_QUESTIONS_GIN" 
      ON "worksheets" 
      USING GIN ("questions")
    `);

    // Add a GIN index for JSONB questionIds for better query performance
    await queryRunner.query(`
      CREATE INDEX "IDX_WORKSHEET_QUESTION_IDS_GIN" 
      ON "worksheets" 
      USING GIN ("questionIds")
    `);

    // Add a GIN index for JSONB questionMetadata for better query performance
    await queryRunner.query(`
      CREATE INDEX "IDX_WORKSHEET_QUESTION_METADATA_GIN" 
      ON "worksheets" 
      USING GIN ("questionMetadata")
    `);

    // Update existing worksheets to have default values
    await queryRunner.query(`
      UPDATE "worksheets" 
      SET 
        "totalQuestions" = 0,
        "maxQuestions" = 100,
        "questions" = '[]'::jsonb,
        "questionIds" = '[]'::jsonb,
        "questionMetadata" = '{
          "questionVersion": 1,
          "hasUnsavedChanges": false,
          "collaborators": [],
          "lockStatus": {
            "isLocked": false
          }
        }'::jsonb
      WHERE "totalQuestions" IS NULL
    `);

    // Add check constraints for data integrity
    await queryRunner.query(`
      ALTER TABLE "worksheets" 
      ADD CONSTRAINT "CHK_WORKSHEET_TOTAL_QUESTIONS_NON_NEGATIVE" 
      CHECK ("totalQuestions" >= 0)
    `);

    await queryRunner.query(`
      ALTER TABLE "worksheets" 
      ADD CONSTRAINT "CHK_WORKSHEET_MAX_QUESTIONS_POSITIVE" 
      CHECK ("maxQuestions" > 0)
    `);

    await queryRunner.query(`
      ALTER TABLE "worksheets" 
      ADD CONSTRAINT "CHK_WORKSHEET_TOTAL_LE_MAX_QUESTIONS" 
      CHECK ("totalQuestions" <= "maxQuestions")
    `);

    // Create a function to validate question data structure (optional)
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION validate_worksheet_questions(questions_data jsonb)
      RETURNS boolean AS $$
      BEGIN
        -- Check if questions is an array
        IF jsonb_typeof(questions_data) != 'array' THEN
          RETURN false;
        END IF;
        
        -- Additional validation can be added here
        -- For example, checking required fields in each question
        
        RETURN true;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Add a check constraint using the validation function
    await queryRunner.query(`
      ALTER TABLE "worksheets" 
      ADD CONSTRAINT "CHK_WORKSHEET_QUESTIONS_VALID" 
      CHECK (questions IS NULL OR validate_worksheet_questions(questions))
    `);

    console.log('✅ Added question management columns and indexes to worksheets table');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop check constraints
    await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT IF EXISTS "CHK_WORKSHEET_QUESTIONS_VALID"`);
    await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT IF EXISTS "CHK_WORKSHEET_TOTAL_LE_MAX_QUESTIONS"`);
    await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT IF EXISTS "CHK_WORKSHEET_MAX_QUESTIONS_POSITIVE"`);
    await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT IF EXISTS "CHK_WORKSHEET_TOTAL_QUESTIONS_NON_NEGATIVE"`);

    // Drop the validation function
    await queryRunner.query(`DROP FUNCTION IF EXISTS validate_worksheet_questions(jsonb)`);

    // Drop indexes
    await queryRunner.dropIndex('worksheets', 'IDX_WORKSHEET_QUESTION_METADATA_GIN');
    await queryRunner.dropIndex('worksheets', 'IDX_WORKSHEET_QUESTION_IDS_GIN');
    await queryRunner.dropIndex('worksheets', 'IDX_WORKSHEET_QUESTIONS_GIN');
    await queryRunner.dropIndex('worksheets', 'IDX_WORKSHEET_WITH_QUESTIONS');
    await queryRunner.dropIndex('worksheets', 'IDX_WORKSHEET_LAST_MODIFIED');
    await queryRunner.dropIndex('worksheets', 'IDX_WORKSHEET_SCHOOL_TOTAL_QUESTIONS');
    await queryRunner.dropIndex('worksheets', 'IDX_WORKSHEET_SCHOOL_UPDATED');

    // Drop columns
    await queryRunner.dropColumns('worksheets', [
      'questionMetadata',
      'questionIds',
      'maxQuestions',
      'createdBy',
      'lastModifiedBy',
      'totalQuestions',
      'questions'
    ]);

    console.log('✅ Removed question management columns and indexes from worksheets table');
  }
}
