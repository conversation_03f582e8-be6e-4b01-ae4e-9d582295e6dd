import { MigrationInterface, QueryRunner } from "typeorm";

export class MakeSchoolAddressAndPhoneNumberNullable1749782482763 implements MigrationInterface {
    name = 'MakeSchoolAddressAndPhoneNumberNullable1749782482763'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "registeredNumber" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "address" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "phoneNumber" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "phoneNumber" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "address" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "schools" ALTER COLUMN "registeredNumber" SET NOT NULL`);
    }

}
