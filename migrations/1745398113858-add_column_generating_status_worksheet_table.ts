import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnGeneratingStatusWorksheetTable1745398113858 implements MigrationInterface {
    name = 'AddColumnGeneratingStatusWorksheetTable1745398113858'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."worksheets_generatingstatus_enum" AS ENUM('Pending', 'Generated', 'Error')`);
        await queryRunner.query(`ALTER TABLE "worksheets" ADD "generatingStatus" "public"."worksheets_generatingstatus_enum" NOT NULL DEFAULT 'Pending'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "worksheets" DROP COLUMN "generatingStatus"`);
        await queryRunner.query(`DROP TYPE "public"."worksheets_generatingstatus_enum"`);
    }

}
