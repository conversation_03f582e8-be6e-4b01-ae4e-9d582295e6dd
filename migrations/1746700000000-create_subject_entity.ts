import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateSubjectEntity1746700000000 implements MigrationInterface {
    name = 'CreateSubjectEntity1746700000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create enum type for subject type
        await queryRunner.query(`CREATE TYPE "public"."subjects_type_enum" AS ENUM('parent', 'child')`);
        
        // Create subjects table
        await queryRunner.query(`
            CREATE TABLE "subjects" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
                "deletedAt" TIMESTAMP,
                "name" character varying NOT NULL,
                "description" character varying,
                "type" "public"."subjects_type_enum" NOT NULL DEFAULT 'child',
                "parentId" uuid,
                CONSTRAINT "PK_subjects" PRIMARY KEY ("id")
            )
        `);
        
        // Add foreign key constraint for parent-child relationship
        await queryRunner.query(`
            ALTER TABLE "subjects" 
            ADD CONSTRAINT "FK_subjects_parent" 
            FOREIGN KEY ("parentId") 
            REFERENCES "subjects"("id") 
            ON DELETE SET NULL
        `);
        
        // Create subject_topics join table
        await queryRunner.query(`
            CREATE TABLE "subject_topics" (
                "subject_id" uuid NOT NULL,
                "topic_id" uuid NOT NULL,
                CONSTRAINT "PK_subject_topics" PRIMARY KEY ("subject_id", "topic_id")
            )
        `);
        
        // Add foreign key constraints for subject_topics
        await queryRunner.query(`
            ALTER TABLE "subject_topics" 
            ADD CONSTRAINT "FK_subject_topics_subject" 
            FOREIGN KEY ("subject_id") 
            REFERENCES "subjects"("id") 
            ON DELETE CASCADE
        `);
        
        await queryRunner.query(`
            ALTER TABLE "subject_topics" 
            ADD CONSTRAINT "FK_subject_topics_topic" 
            FOREIGN KEY ("topic_id") 
            REFERENCES "option_values"("id") 
            ON DELETE CASCADE
        `);
        
        // Create subject_grades join table
        await queryRunner.query(`
            CREATE TABLE "subject_grades" (
                "subject_id" uuid NOT NULL,
                "grade_id" uuid NOT NULL,
                CONSTRAINT "PK_subject_grades" PRIMARY KEY ("subject_id", "grade_id")
            )
        `);
        
        // Add foreign key constraints for subject_grades
        await queryRunner.query(`
            ALTER TABLE "subject_grades" 
            ADD CONSTRAINT "FK_subject_grades_subject" 
            FOREIGN KEY ("subject_id") 
            REFERENCES "subjects"("id") 
            ON DELETE CASCADE
        `);
        
        await queryRunner.query(`
            ALTER TABLE "subject_grades" 
            ADD CONSTRAINT "FK_subject_grades_grade" 
            FOREIGN KEY ("grade_id") 
            REFERENCES "option_values"("id") 
            ON DELETE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop subject_grades join table
        await queryRunner.query(`ALTER TABLE "subject_grades" DROP CONSTRAINT "FK_subject_grades_grade"`);
        await queryRunner.query(`ALTER TABLE "subject_grades" DROP CONSTRAINT "FK_subject_grades_subject"`);
        await queryRunner.query(`DROP TABLE "subject_grades"`);
        
        // Drop subject_topics join table
        await queryRunner.query(`ALTER TABLE "subject_topics" DROP CONSTRAINT "FK_subject_topics_topic"`);
        await queryRunner.query(`ALTER TABLE "subject_topics" DROP CONSTRAINT "FK_subject_topics_subject"`);
        await queryRunner.query(`DROP TABLE "subject_topics"`);
        
        // Drop subjects table
        await queryRunner.query(`ALTER TABLE "subjects" DROP CONSTRAINT "FK_subjects_parent"`);
        await queryRunner.query(`DROP TABLE "subjects"`);
        
        // Drop enum type
        await queryRunner.query(`DROP TYPE "public"."subjects_type_enum"`);
    }
}