import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSchoolIdToWorksheetsAndAssignExisting1748413407903 implements MigrationInterface {
    name = 'AddSchoolIdToWorksheetsAndAssignExisting1748413407903'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "worksheets" ADD "schoolId" uuid`);
        await queryRunner.query(`ALTER TABLE "worksheets" ADD CONSTRAINT "FK_3b9f1186dae83a25cce53892e55" FOREIGN KEY ("schoolId") REFERENCES "schools"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Update existing worksheets to assign the specified schoolId
        await queryRunner.query(`UPDATE "worksheets" SET "schoolId" = '0680154d-b878-4933-a76d-6829b6799038'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // No need to revert the data update in the down migration,
        // as dropping the column will remove the data anyway.
        await queryRunner.query(`ALTER TABLE "worksheets" DROP CONSTRAINT "FK_3b9f1186dae83a25cce53892e55"`);
        await queryRunner.query(`ALTER TABLE "worksheets" DROP COLUMN "schoolId"`);
    }

}
