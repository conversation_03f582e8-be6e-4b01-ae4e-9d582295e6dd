import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIsActiveToOptionValue1748230223004 implements MigrationInterface {
    name = 'AddIsActiveToOptionValue1748230223004'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subjects" DROP CONSTRAINT "FK_subjects_parent"`);
        await queryRunner.query(`ALTER TABLE "schools" DROP CONSTRAINT "FK_schools_admin"`);
        await queryRunner.query(`ALTER TABLE "schools" DROP CONSTRAINT "FK_schools_brand"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_users_school"`);
        await queryRunner.query(`ALTER TABLE "subject_topics" DROP CONSTRAINT "FK_subject_topics_subject"`);
        await queryRunner.query(`ALTER TABLE "subject_topics" DROP CONSTRAINT "FK_subject_topics_topic"`);
        await queryRunner.query(`ALTER TABLE "subject_grades" DROP CONSTRAINT "FK_subject_grades_subject"`);
        await queryRunner.query(`ALTER TABLE "subject_grades" DROP CONSTRAINT "FK_subject_grades_grade"`);
        await queryRunner.query(`ALTER TABLE "option_values" ADD "isActive" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "worksheets" DROP COLUMN "subjectData"`);
        await queryRunner.query(`ALTER TABLE "worksheets" ADD "subjectData" json`);
        await queryRunner.query(`ALTER TABLE "schools" ADD CONSTRAINT "UQ_9fabaf7dbecda874878fff6f708" UNIQUE ("brandId")`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "role" DROP DEFAULT`);
        await queryRunner.query(`CREATE INDEX "IDX_4c597c45c6677b1e843829e178" ON "subject_topics" ("subject_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_62d9561f59db449a879bfcd875" ON "subject_topics" ("topic_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_14768ca0ee998a0596d41cd550" ON "subject_grades" ("subject_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_11e256dca417122e263d65da50" ON "subject_grades" ("grade_id") `);
        await queryRunner.query(`ALTER TABLE "subjects" ADD CONSTRAINT "FK_4190e38428c54782b4bbe278a28" FOREIGN KEY ("parentId") REFERENCES "subjects"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schools" ADD CONSTRAINT "FK_99441d7b961e3ad1f2466a95aa6" FOREIGN KEY ("adminId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schools" ADD CONSTRAINT "FK_9fabaf7dbecda874878fff6f708" FOREIGN KEY ("brandId") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_435e192698a6b7d10849295643d" FOREIGN KEY ("schoolId") REFERENCES "schools"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subject_topics" ADD CONSTRAINT "FK_4c597c45c6677b1e843829e1782" FOREIGN KEY ("subject_id") REFERENCES "subjects"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "subject_topics" ADD CONSTRAINT "FK_62d9561f59db449a879bfcd875f" FOREIGN KEY ("topic_id") REFERENCES "option_values"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "subject_grades" ADD CONSTRAINT "FK_14768ca0ee998a0596d41cd5508" FOREIGN KEY ("subject_id") REFERENCES "subjects"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "subject_grades" ADD CONSTRAINT "FK_11e256dca417122e263d65da50e" FOREIGN KEY ("grade_id") REFERENCES "option_values"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "subject_grades" DROP CONSTRAINT "FK_11e256dca417122e263d65da50e"`);
        await queryRunner.query(`ALTER TABLE "subject_grades" DROP CONSTRAINT "FK_14768ca0ee998a0596d41cd5508"`);
        await queryRunner.query(`ALTER TABLE "subject_topics" DROP CONSTRAINT "FK_62d9561f59db449a879bfcd875f"`);
        await queryRunner.query(`ALTER TABLE "subject_topics" DROP CONSTRAINT "FK_4c597c45c6677b1e843829e1782"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_435e192698a6b7d10849295643d"`);
        await queryRunner.query(`ALTER TABLE "schools" DROP CONSTRAINT "FK_9fabaf7dbecda874878fff6f708"`);
        await queryRunner.query(`ALTER TABLE "schools" DROP CONSTRAINT "FK_99441d7b961e3ad1f2466a95aa6"`);
        await queryRunner.query(`ALTER TABLE "subjects" DROP CONSTRAINT "FK_4190e38428c54782b4bbe278a28"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_11e256dca417122e263d65da50"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_14768ca0ee998a0596d41cd550"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_62d9561f59db449a879bfcd875"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_4c597c45c6677b1e843829e178"`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "role" SET DEFAULT 'teacher'`);
        await queryRunner.query(`ALTER TABLE "schools" DROP CONSTRAINT "UQ_9fabaf7dbecda874878fff6f708"`);
        await queryRunner.query(`ALTER TABLE "worksheets" DROP COLUMN "subjectData"`);
        await queryRunner.query(`ALTER TABLE "worksheets" ADD "subjectData" jsonb`);
        await queryRunner.query(`ALTER TABLE "option_values" DROP COLUMN "isActive"`);
        await queryRunner.query(`ALTER TABLE "subject_grades" ADD CONSTRAINT "FK_subject_grades_grade" FOREIGN KEY ("grade_id") REFERENCES "option_values"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subject_grades" ADD CONSTRAINT "FK_subject_grades_subject" FOREIGN KEY ("subject_id") REFERENCES "subjects"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subject_topics" ADD CONSTRAINT "FK_subject_topics_topic" FOREIGN KEY ("topic_id") REFERENCES "option_values"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subject_topics" ADD CONSTRAINT "FK_subject_topics_subject" FOREIGN KEY ("subject_id") REFERENCES "subjects"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_users_school" FOREIGN KEY ("schoolId") REFERENCES "schools"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schools" ADD CONSTRAINT "FK_schools_brand" FOREIGN KEY ("brandId") REFERENCES "brands"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "schools" ADD CONSTRAINT "FK_schools_admin" FOREIGN KEY ("adminId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "subjects" ADD CONSTRAINT "FK_subjects_parent" FOREIGN KEY ("parentId") REFERENCES "subjects"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

}
