import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateExamResultTable1746604769675 implements MigrationInterface {
    name = 'CreateExamResultTable1746604769675'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "exam_results" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "examId" uuid NOT NULL, "studentId" uuid NOT NULL, "answers" jsonb NOT NULL, "detail" jsonb NOT NULL, "score" integer NOT NULL, "total" integer NOT NULL, "submittedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_07d4ea139ed7ca111c75df2de12" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "exam_results" ADD CONSTRAINT "FK_7ef2a5e17cf50726d548405acfe" FOREIGN KEY ("examId") REFERENCES "exams"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "exam_results" ADD CONSTRAINT "FK_033e5c68fb99745a58e82f7abdf" FOREIGN KEY ("studentId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "exam_results" DROP CONSTRAINT "FK_033e5c68fb99745a58e82f7abdf"`);
        await queryRunner.query(`ALTER TABLE "exam_results" DROP CONSTRAINT "FK_7ef2a5e17cf50726d548405acfe"`);
        await queryRunner.query(`DROP TABLE "exam_results"`);
    }

}
