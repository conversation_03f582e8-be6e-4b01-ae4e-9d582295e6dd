# Installation Guide

## Prerequisites

Before installing the EduSG backend system, ensure you have the following:

- Node.js (v16 or later)
- npm (v7 or later)
- MongoDB
- Redis
- PostgreSQL
- poppler-utils (for PDF processing)
- API keys for:
  - OpenRouter (for Qwen 2.5 VL model)
  - Pinecone or Qdrant (vector database)
  - OpenAI or Google (for AI models)

## Installation Steps

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd edusg-be
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Install System Dependencies

For Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install -y poppler-utils
```

For macOS:
```bash
brew install poppler
```

### 4. Configure Environment Variables

Create a `.env` file in the root directory with the following variables:

```
# Server
PORT=3000
NODE_ENV=development

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your_password
DATABASE_NAME=edusg

# MongoDB
MONGODB_URI=mongodb://localhost:27017/edusg

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRATION=1d

# Vector Database
VECTOR_DB_TYPE=pinecone
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX=your_pinecone_index

# AI Models
OPENAI_API_KEY=your_openai_api_key
LLM_MODEL=openai/gpt-4-turbo-preview

# Multimodal Processing
USE_MULTIMODAL_PROCESSING=true
IMAGE_STORAGE_PATH=./uploads/images
OPENROUTER_API_KEY=your_openrouter_api_key

# Parallel Processing
USE_PARALLEL_PROCESSING=true
PARALLEL_BATCHES=2
PARALLEL_THRESHOLD=5
```

### 5. Create Required Directories

```bash
mkdir -p uploads/images
```

### 6. Initialize the Database

```bash
npm run typeorm:migration:run
```

### 7. Start the Server

For development:
```bash
npm run start:dev
```

For production:
```bash
npm run build
npm run start:prod
```

## Verification

To verify that the installation was successful:

1. Access the API documentation at `http://localhost:3000/api`
2. Test the health check endpoint at `http://localhost:3000/`
3. Try uploading a document to `http://localhost:3000/documents/upload`

## Troubleshooting

### PDF Processing Issues

If you encounter issues with PDF processing:

1. Verify that poppler-utils is installed:
   ```bash
   which pdftotext
   which pdftoppm
   ```

2. Check the permissions of the image storage directory:
   ```bash
   ls -la uploads/images
   ```

### Vector Database Issues

If you encounter issues with the vector database:

1. Verify your API keys and environment settings
2. Check that the vector index exists in your Pinecone/Qdrant account
3. Test the connection with a simple query

### API Connection Issues

If you encounter issues connecting to external APIs:

1. Verify your API keys
2. Check for rate limiting or quota issues
3. Test the APIs with a simple curl command

## Next Steps

After installation, you may want to:

1. Upload some initial documents to build your knowledge base
2. Configure the system for your specific educational content
3. Set up user accounts and authentication
4. Connect your frontend application to the API
