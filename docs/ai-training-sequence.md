# AI Training Sequence Diagram

This sequence diagram shows the time-based interactions between different components of the system during document upload and query processing.

```mermaid
sequenceDiagram
    actor Admin
    participant UploadAPI as Document Upload API
    participant DocService as Document Service
    participant MultiModal as Multimodal Service
    participant QwenModel as Qwen 2.5 VL Model
    participant VectorDB as Vector Database
    participant ImgStorage as Image Storage
    actor User
    participant QueryAPI as Query API
    participant LLM as AI Model

    %% Document Upload Flow
    Admin->>UploadAPI: Upload PDF document/examination
    UploadAPI->>DocService: Process document

    alt Standard Document
        DocService->>DocService: Extract text only
    else Examination with Images
        DocService->>MultiModal: Process with multimodal
        MultiModal->>MultiModal: Extract text & images
        MultiModal->>ImgStorage: Store extracted images
        MultiModal->>QwenModel: Analyze images
        QwenModel-->>MultiModal: Return image analysis
        MultiModal-->>DocService: Return enhanced content
    end

    DocService->>DocService: Process & chunk content
    DocService->>VectorDB: Generate & store embeddings
    VectorDB-->>DocService: Confirm storage
    DocService-->>UploadAPI: Document processed
    UploadAPI-->>Admin: Upload successful

    %% Query Flow
    User->>QueryAPI: Submit question
    QueryAPI->>VectorDB: Search for relevant content
    VectorDB-->>QueryAPI: Return relevant chunks

    alt Contains Image References
        QueryAPI->>ImgStorage: Retrieve image references
        ImgStorage-->>QueryAPI: Return image URLs
    end

    QueryAPI->>LLM: Generate response with context
    LLM-->>QueryAPI: Return AI response
    QueryAPI-->>User: Display response with images

    %% Note
    Note over Admin,User: The system doesn't "train" the AI model in the traditional sense.<br/>Instead, it creates a searchable knowledge base that the AI can reference.
```

## Sequence Description

### Document Upload Sequence

The document upload sequence shows the flow of interactions when an administrator uploads a document:

1. **Initiation**: Admin uploads a PDF document or examination through the Upload API
2. **Processing**: The Document Service receives the document for processing
3. **Processing Path Decision**:
   - For standard documents: The Document Service extracts only text
   - For examinations with images: The document is sent to the Multimodal Service
4. **Multimodal Processing** (for documents with images):
   - Text and images are extracted
   - Images are stored in the Image Storage
   - Images are analyzed by the Qwen 2.5 VL Model
   - Enhanced content is returned to the Document Service
5. **Content Processing**: The Document Service processes and chunks the content
6. **Vector Storage**: Embeddings are generated and stored in the Vector Database
7. **Completion**: The Upload API confirms successful processing to the Admin

### Query Sequence

The query sequence shows the flow of interactions when a user submits a question:

1. **Query Submission**: User submits a question through the Query API
2. **Vector Search**: The Query API searches the Vector Database for relevant content
3. **Content Retrieval**: Relevant chunks are returned to the Query API
4. **Image Handling** (if applicable):
   - If the content contains image references, they are retrieved from Image Storage
   - Image URLs are returned to the Query API
5. **Response Generation**: The Query API sends the context to the AI Model
6. **AI Processing**: The AI Model generates a response based on the context
7. **Response Delivery**: The Query API returns the response (with images if applicable) to the User

## Key Interactions

### Multimodal Processing

The interaction between the Document Service and Multimodal Service is a key part of the system's ability to handle documents with images. This sequence shows how:

1. The Document Service delegates multimodal processing to the specialized Multimodal Service
2. The Multimodal Service extracts both text and images
3. Images are stored separately in the Image Storage
4. The Qwen 2.5 VL Model provides enhanced understanding of the images
5. The enhanced content is returned to the Document Service for further processing

### Vector Database Interaction

The Vector Database serves as the central knowledge repository in the system:

1. During document upload, embeddings are stored in the Vector Database
2. During querying, the Vector Database is searched for relevant content
3. The Vector Database returns content that semantically matches the user's query

## Implementation Notes

This sequence diagram represents the logical flow of interactions between components. In the actual implementation:

1. Some interactions may be asynchronous
2. Error handling and retry logic are implemented at various stages
3. Caching mechanisms may be used to improve performance
4. Authentication and authorization checks occur at API boundaries