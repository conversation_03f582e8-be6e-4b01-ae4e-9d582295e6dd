# Worksheet Question Type Distribution

This document describes the enhanced worksheet creation functionality that supports specifying the number of questions per question type.

## Overview

The worksheet creation endpoint now supports specifying the exact number of questions for each question type. This allows for more precise control over the generated worksheet content.

## Usage

When creating a worksheet, you can now include a `count` field in the options array for each question type option:

```json
{
  "title": "Math Worksheet - Mixed Question Types",
  "options": [
    {
      "key": "grade",
      "value": "p5"
    },
    {
      "key": "topic",
      "value": "mathematics"
    },
    {
      "key": "level",
      "value": "medium"
    },
    {
      "key": "language",
      "value": "english"
    },
    {
      "key": "question_count",
      "value": "10"
    },
    {
      "key": "question_type",
      "value": "multiple_choices",
      "count": 5
    },
    {
      "key": "question_type",
      "value": "single_choice",
      "count": 3
    },
    {
      "key": "question_type",
      "value": "fill_blank",
      "count": 2
    }
  ]
}
```

## API Endpoint

```
POST /worksheets
```

### Request Body

The `CreateWorksheetDto` has been enhanced to support the `count` field in the `WorksheetOptionDto`:

```typescript
export class WorksheetOptionDto {
  key: string;
  value: string;
  text?: string;
  count?: number; // Number of questions for this option (used with exerciseType)
}

export class CreateWorksheetDto {
  title: string;
  description?: string;
  options: WorksheetOptionDto[];
}
```

### Response

The response format remains the same as the standard worksheet creation endpoint.

## Implementation Details

When a worksheet is created with question type distribution:

1. The system stores the count value for each question type option
2. During worksheet generation, the system builds an exercise type distribution object
3. This distribution is passed to the prompt service to generate the appropriate number of questions for each type
4. The total number of questions should match the sum of all question type counts

## Notes

- If no count is specified for question types, the system will generate questions with default distribution
- The sum of all question type counts should match the total question count specified in the options
- If the counts don't match the total, the system will adjust the results to match the requested total
