# Query Processing

## Overview

The query processing system is responsible for handling user queries, retrieving relevant document chunks, and generating AI responses based on the retrieved content.

## Components

### DocumentsService (Query Methods)

The `DocumentsService` includes query methods that:
- Accept user queries
- Search the vector database for relevant content
- Filter results by relevance
- Format and return AI responses

### VectorDBService (Search Methods)

The `VectorDBService` provides search functionality that:
- Converts query text to vector embeddings
- Searches the vector database for similar vectors
- Returns relevant document chunks
- Includes metadata with search results

### AI Response Generation

The AI response generation process:
- Uses retrieved document chunks as context
- Formats prompts for the AI model
- Generates comprehensive responses
- Includes references to images when relevant

## Query Processing Flow

1. Query Submission:
   - User submits a query through the API
   - Query is received by the `DocumentsController`
   - Query is passed to the `DocumentsService`

2. Vector Search:
   - Query is converted to a vector embedding
   - Vector database is searched for similar vectors
   - Relevant document chunks are retrieved
   - Results are filtered by relevance score

3. Context Preparation:
   - Retrieved document chunks are formatted as context
   - Special handling is applied for examination content
   - Image references are included when available

4. Response Generation:
   - Context is provided to the AI model
   - AI generates a response based on the context
   - Response is formatted according to query type
   - Image references are included in the response

5. Response Delivery:
   - Formatted response is returned to the user
   - Images can be retrieved through the image API
   - Frontend can display both text and images

## Optimization

The query processing system includes several optimizations:
- Relevance filtering to remove low-quality matches
- Special handling for examination queries
- Customized prompts for different types of content
- Inclusion of image references for visual content

## Example Query

```
GET /documents/query?query=How do I solve quadratic equations?&similarityTopK=5
```

This query would:
1. Convert "How do I solve quadratic equations?" to a vector
2. Find the 5 most similar document chunks
3. Use these chunks as context for the AI
4. Generate a response explaining quadratic equations
5. Include any relevant images or diagrams

## Response Format

The response includes:
- The AI-generated answer
- References to source documents
- URLs for any relevant images
- Metadata about the query processing
