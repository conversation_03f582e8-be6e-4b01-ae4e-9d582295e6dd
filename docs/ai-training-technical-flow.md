# AI Training Technical Flow

This diagram provides a detailed technical view of the system components and their interactions, showing the specific methods and data flow between different parts of the system.

```mermaid
flowchart TD
    subgraph "Document Processing"
        Upload["/documents/upload API"] --> DocService["DocumentsService"]
        DocService --> ExtractText["extractText()"]
        ExtractText --> PDFReader["PDFReader (text-only)"]
        ExtractText --> MultimodalService["MultimodalService (text+images)"]

        PDFReader --> TextChunks["Text Chunks"]
        MultimodalService --> ProcessPDF["processPdfWithMultimodalAI()"]
        ProcessPDF --> PDFtoText["pdftotext (text extraction)"]
        ProcessPDF --> PDFtoPPM["pdftoppm (image extraction)"]

        PDFtoText --> ExtractedText["Extracted Text"]
        PDFtoPPM --> ExtractedImages["Extracted Images"]

        ExtractedText --> ProcessWith<PERSON>wen["processWithQwen()"]
        ExtractedImages --> Process<PERSON>ith<PERSON>wen

        ProcessWithQwen --> QwenAPI["Qwen 2.5 VL Model API"]
        QwenAPI --> EnhancedContent["Enhanced Content with Image Analysis"]

        TextChunks --> ProcessDocs["processDocumentsWithImagesAndExercises()"]
        EnhancedContent --> ProcessDocs

        ProcessDocs --> Metadata["Add Metadata (exercise format, image references)"]
        Metadata --> CreateIndex["VectorStoreIndex.fromDocuments()"]
    end

    subgraph "Vector Storage"
        CreateIndex --> Embeddings["Generate Embeddings (GeminiEmbedding)"]
        Embeddings --> VectorDB["Vector Database (Pinecone/Qdrant)"]
        VectorDB --> StoredVectors["Stored Document Vectors"]
    end

    subgraph "Query Processing"
        Query["/documents/query API"] --> QueryService["queryDocuments()"]
        QueryService --> RetrieveVectors["Retrieve Similar Vectors"]
        RetrieveVectors --> VectorDB
        VectorDB --> RelevantChunks["Relevant Document Chunks"]
        RelevantChunks --> FilterRelevance["Filter by Relevance Score"]
        FilterRelevance --> GenerateResponse["Generate AI Response"]
        GenerateResponse --> LLM["LLM (Gemini/OpenAI)"]
        LLM --> FinalResponse["Response with Text and Image References"]

        FinalResponse --> ImageEndpoint["/api/files/images/:filename API"]
        ImageEndpoint --> ServeImages["Serve Images to User"]
    end

    classDef api fill:#f96,stroke:#333,stroke-width:2px;
    classDef service fill:#9cf,stroke:#333,stroke-width:1px;
    classDef process fill:#9fc,stroke:#333,stroke-width:1px;
    classDef data fill:#fcf,stroke:#333,stroke-width:1px;
    classDef storage fill:#ff9,stroke:#333,stroke-width:1px;

    class Upload,Query,ImageEndpoint api;
    class DocService,MultimodalService,QueryService service;
    class ExtractText,ProcessPDF,ProcessWithQwen,ProcessDocs,CreateIndex,RetrieveVectors,FilterRelevance,GenerateResponse process;
    class TextChunks,ExtractedText,ExtractedImages,EnhancedContent,Metadata,RelevantChunks,FinalResponse data;
    class VectorDB,StoredVectors,Embeddings storage;
```

## Technical Components

### Document Processing

This section shows the detailed flow of document processing:

1. **API Entry Point**: The `/documents/upload` API endpoint receives document uploads
2. **Service Layer**: The `DocumentsService` handles the document processing logic
3. **Text Extraction**: The `extractText()` method determines the processing path:
   - For text-only processing: Uses `PDFReader` from LlamaIndex
   - For multimodal processing: Uses the `MultimodalService`
4. **Multimodal Processing**:
   - `processPdfWithMultimodalAI()` coordinates the multimodal processing
   - `pdftotext` extracts text content from PDFs
   - `pdftoppm` extracts images from PDFs
   - `processWithQwen()` sends text and images to the Qwen 2.5 VL model
   - The Qwen model returns enhanced content with image analysis
5. **Content Processing**:
   - `processDocumentsWithImagesAndExercises()` processes both text-only and multimodal content
   - Metadata is added for exercise formats and image references
   - `VectorStoreIndex.fromDocuments()` creates the vector index

### Vector Storage

This section shows how document vectors are stored:

1. **Embedding Generation**: The `GeminiEmbedding` model generates vector embeddings
2. **Vector Database**: Embeddings are stored in Pinecone or Qdrant
3. **Storage**: Document vectors are stored with their metadata

### Query Processing

This section shows how queries are processed:

1. **API Entry Point**: The `/documents/query` API endpoint receives user queries
2. **Query Service**: The `queryDocuments()` method handles query processing
3. **Vector Retrieval**: Similar vectors are retrieved from the vector database
4. **Relevance Filtering**: Results are filtered by relevance score
5. **Response Generation**: An AI response is generated using the retrieved content
6. **LLM Processing**: The LLM (Gemini or OpenAI) generates the final response
7. **Image Serving**: Images are served through the `/api/files/images/:filename` API

## Implementation Details

The technical flow is implemented across several modules:

- **DocumentsModule**: Handles document processing and querying
- **MultimodalModule**: Provides multimodal processing capabilities
- **VectorDBModule**: Manages vector database interactions
- **FilesModule**: Handles file storage and retrieval, including images

The system uses dependency injection to manage the relationships between these components, allowing for flexible configuration and testing.