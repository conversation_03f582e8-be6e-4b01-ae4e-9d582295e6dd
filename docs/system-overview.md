# System Overview

## Introduction

The EduSG backend system is designed to provide AI-powered educational assistance for students and teachers in Singapore. The system can process various types of educational content, including curriculum documents and examination materials, and use this information to answer user queries.

## Key Components

### 1. Document Processing System

The document processing system is responsible for:
- Accepting document uploads (PDF, etc.)
- Extracting text and images from documents
- Processing and analyzing document content
- Storing document embeddings in a vector database

### 2. Multimodal Processing

The multimodal processing system enhances document understanding by:
- Extracting images from PDF documents
- Using the Qwen 2.5 VL model to analyze images
- Combining text and image understanding
- Providing enhanced context for AI responses

### 3. Vector Database

The vector database (Pinecone/Qdrant) stores:
- Document embeddings for efficient retrieval
- Metadata about documents (type, content, etc.)
- References to images and other media

### 4. Query Processing

The query processing system:
- Accepts user questions
- Retrieves relevant document chunks
- Provides context to the AI model
- Generates comprehensive responses

### 5. AI Models

The system uses several AI models:
- Gemini Embedding for text embedding
- Qwen 2.5 VL for multimodal understanding
- OpenAI/Gemini for response generation

## System Architecture

The system follows a modular architecture with:
- NestJS modules for different functionalities
- Service-oriented design for separation of concerns
- RESTful APIs for client interaction
- Asynchronous processing for handling large documents

## Data Flow

1. Documents are uploaded through the API
2. Documents are processed and stored
3. Users submit queries through the API
4. Relevant document chunks are retrieved
5. AI generates responses based on retrieved content
6. Responses are returned to the user

For more detailed information, see the specific documentation files for each component.
