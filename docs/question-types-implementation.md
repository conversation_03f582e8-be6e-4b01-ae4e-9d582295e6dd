# Question Types Implementation

## Overview

This document describes the implementation of question types in the EduSG system, with a focus on the recently added support for Fill Blank and Creative Writing question types.

## Supported Question Types

The system now supports the following question types:

### 1. Multiple Choice (`multiple_choice`)
- **Frontend Label**: "Multiple Choices" or "Multiple Choice"
- **Backend Type**: `multiple_choice`
- **Description**: Questions with 5-7 options where 2 or more answers can be correct

### 2. Single Choice (`single_choice`) 
- **Frontend Label**: "Single Choice"
- **Backend Type**: `single_choice`
- **Description**: Questions with exactly 4 options where only one answer is correct

### 3. Fill in the Blank (`fill_blank`)
- **Frontend Label**: "Fill Blank"
- **Backend Type**: `fill_blank`
- **Description**: Questions where students fill in missing words or phrases
- **Validation**: Case-insensitive comparison with trimmed whitespace

### 4. Creative Writing (`creative_writing`)
- **Frontend Label**: "Creative Writing"
- **Backend Type**: `creative_writing`
- **Description**: Open-ended questions that encourage original thinking and expression
- **Validation**: Marked as correct if the student provides any non-empty answer

### 5. Open Ended (`open_ended`)
- **Frontend Label**: "Open Ended"
- **Backend Type**: `open_ended`
- **Description**: Open-ended questions for detailed responses

### 6. Matching (`matching`)
- **Frontend Label**: "Matching"
- **Backend Type**: `matching`
- **Description**: Questions that require matching items between two lists

## Implementation Details

### Label Mapping

The system uses a mapping function to convert frontend question type labels to backend formats:

```typescript
private mapQuestionTypeLabel(label: string): string {
  const mappings: Record<string, string> = {
    'Fill Blank': 'fill_blank',
    'Creative Writing': 'creative_writing',
    'Multiple Choices': 'multiple_choice',
    'Single Choice': 'single_choice',
    'Multiple Choice': 'multiple_choice',
    'Open Ended': 'open_ended',
    'Matching': 'matching'
  };

  return mappings[label] || label.toLowerCase().replace(/\s+/g, '_');
}
```

### Key Components Updated

1. **CreativeWritingStrategy**: New strategy class providing instructions for creative writing questions
2. **ExerciseTypeFactory**: Updated to recognize and handle all question types including Fill Blank and Creative Writing
3. **WorksheetService**: Added question type mapping in `convertNewFormatToOptions()`
4. **QueueConsumer**: Added question type mapping in `process()` method
5. **ExamResultService**: Updated to handle scoring of Fill Blank and Creative Writing questions
6. **BuildPromptService**: Updated comments to clarify expected type names

### Prompt Instructions

#### Fill Blank Strategy
- Ensures blanks test key concepts or vocabulary
- Provides clear context clues in surrounding text
- Limits number of blanks per sentence
- Ensures only one correct answer per blank
- Strategic placement of blanks to test understanding

#### Creative Writing Strategy
- Encourages original thinking and personal expression
- Provides clear prompts within subject area
- Includes writing guidelines appropriate for grade level
- Sets reasonable word count expectations
- Allows multiple correct approaches and interpretations

### Validation and Scoring

- **Fill Blank**: Case-insensitive string comparison with trimmed whitespace
- **Creative Writing**: Considered correct if student provides any non-empty response

## Configuration

Question types are seeded in the database through `OptionsService.seedInitialData()`:

```typescript
{
  key: 'question_type',
  label: 'Question Type',
  description: 'What type of questions do you want to practice?',
  values: [
    'Fill Blank',
    'Single Choice',
    'Multiple Choices',
    'Creative Writing',
  ],
}
```

## Testing

To verify the implementation works correctly, you can run the test script:

```bash
node test-question-types.js
```

This will verify that:
1. Question type mapping works correctly
2. Exercise type detection functions properly

## Future Enhancements

1. **AI-Based Grading**: For Creative Writing questions, implement AI-based scoring for more nuanced evaluation
2. **Partial Credit**: For Fill Blank questions, implement partial credit for partially correct answers
3. **Advanced Matching**: Enhance matching questions with more complex matching patterns
4. **Rich Text Support**: Allow rich text formatting in Creative Writing questions
