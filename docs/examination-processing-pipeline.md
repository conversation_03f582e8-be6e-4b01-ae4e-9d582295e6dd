# Examination Processing Pipeline

This diagram illustrates the detailed flow of how examination PDFs are processed in the system, including both multimodal and standard processing paths.

```mermaid
graph LR
    subgraph "Examination PDF Processing"
        A[Examination PDF] --> B[PDF Upload]
        B --> C{USE_MULTIMODAL_PROCESSING?}

        C -->|Yes| D[Multimodal Processing]
        C -->|No| E[Standard Processing]

        D --> F[Extract Text with pdftotext]
        D --> G[Extract Images with pdftoppm]

        F --> H[Text Content]
        G --> I[Page Images]

        H --> J[Qwen 2.5 VL Analysis]
        I --> J

        J --> K[Enhanced Content with Image Understanding]

        E --> L[Text-only Content]

        K --> M[Document Processing]
        L --> M

        M --> N[Exercise Format Detection]
        M --> O[Image Reference Detection]

        N --> P[Add Exercise Metadata]
        O --> Q[Add Image Metadata]

        P --> R[Vector Embedding]
        Q --> R

        R --> S[Vector Storage]
    end

    subgraph "Query Processing"
        T[User Query] --> U[Vector Search]
        U --> V[Retrieve Relevant Content]
        V --> W{Contains Images?}

        W -->|Yes| X[Include Image References]
        W -->|No| Y[Text-only Response]

        X --> Z[Generate Response with Images]
        Y --> AA[Generate Text Response]

        Z --> BB[Final Response]
        AA --> BB
    end

    S -.-> V

    classDef input fill:#f96,stroke:#333,stroke-width:2px;
    classDef process fill:#9cf,stroke:#333,stroke-width:1px;
    classDef decision fill:#fcf,stroke:#333,stroke-width:1px;
    classDef output fill:#9fc,stroke:#333,stroke-width:1px;
    classDef storage fill:#ff9,stroke:#333,stroke-width:1px;

    class A,T input;
    class B,D,E,F,G,J,M,N,O,R,U,Z,AA process;
    class C,W decision;
    class H,I,K,L,P,Q,V,X,Y,BB output;
    class S storage;
```

## Process Description

### Examination PDF Processing

1. **Input**: The process begins with an examination PDF document
2. **Decision Point**: The system checks if multimodal processing is enabled
3. **Multimodal Path**:
   - Text is extracted using `pdftotext`
   - Images are extracted using `pdftoppm`
   - The Qwen 2.5 VL model analyzes both text and images
   - Enhanced content is generated with image understanding
4. **Standard Path**:
   - Only text content is extracted
5. **Common Processing**:
   - Exercise formats are detected
   - Image references are identified
   - Metadata is added to the content
   - Vector embeddings are generated
   - Content is stored in the vector database

### Query Processing

1. **User Query**: The user submits a question
2. **Vector Search**: The system searches for relevant content
3. **Content Retrieval**: Relevant document chunks are retrieved
4. **Decision Point**: The system checks if the content contains images
5. **Response Generation**:
   - For content with images: Image references are included
   - For text-only content: A text response is generated
6. **Final Response**: The complete response is returned to the user

## Implementation

This pipeline is implemented in the `MultimodalService` and `DocumentsService` classes. The multimodal processing requires the `poppler-utils` package to be installed on the system for PDF processing.