# Multimodal Processing

## Overview

The multimodal processing system enhances document understanding by processing both text and images from PDF documents. This is particularly valuable for educational content like examinations, which often contain diagrams, charts, and other visual elements.

## Components

### MultimodalService

The `MultimodalService` is responsible for:
- Extracting text from PDF documents using `pdftotext`
- Extracting images from PDF documents using `pdftoppm`
- Processing images with the Qwen 2.5 VL model
- Combining text and image understanding

### Qwen 2.5 VL Model

The Qwen 2.5 VL (Vision-Language) model:
- Processes both text and images
- Understands the relationship between text and visual content
- Provides enhanced descriptions of visual elements
- Improves understanding of diagrams, charts, and other visual content

### Image Storage and Retrieval

The system:
- Stores extracted images in a configurable location
- Provides an API endpoint for retrieving images
- References images in document metadata
- Includes image references in AI responses

## Configuration

The multimodal processing system can be configured through environment variables:

```
# Multimodal Processing
USE_MULTIMODAL_PROCESSING=true
IMAGE_STORAGE_PATH=./uploads/images
OPENROUTER_API_KEY=your_openrouter_api_key
```

## System Requirements

The multimodal processing system requires:
- `poppler-utils` for PDF processing (install with `apt-get install poppler-utils` or `brew install poppler`)
- Access to the OpenRouter API for the Qwen 2.5 VL model
- Sufficient storage space for extracted images

## Process Flow

1. PDF document is uploaded
2. If multimodal processing is enabled:
   - Text is extracted using `pdftotext`
   - Images are extracted using `pdftoppm`
   - Images are stored in the configured location
   - Text and images are processed with the Qwen 2.5 VL model
   - Enhanced content is stored in the vector database
3. When a user queries the system:
   - Relevant content is retrieved
   - Image references are included in the response
   - The frontend can display both text and images

## Benefits

The multimodal processing system provides several benefits:
- Better understanding of visual content in documents
- More comprehensive responses to user queries
- Improved handling of examination content with diagrams
- Enhanced educational experience for users

## Limitations

Current limitations include:
- Dependency on external tools (`poppler-utils`)
- API rate limits for the Qwen 2.5 VL model
- Storage requirements for extracted images
- Processing time for large documents with many images
