# API Documentation

## Overview

The EduSG backend provides a comprehensive RESTful API for interacting with the system. The API is documented using OpenAPI/Swagger, which provides an interactive interface for exploring and testing the API endpoints.

## Accessing the API Documentation

When the application is running, you can access the interactive API documentation at:

```
http://localhost:3000/api
```

This interactive documentation allows you to:

1. <PERSON>rowse all available endpoints
2. See request and response schemas
3. Test endpoints directly from the browser
4. View authentication requirements

## Authentication

Most API endpoints require authentication. To authenticate:

1. Use the `/auth/sign-in` endpoint to obtain a JWT token
2. Include the token in the `Authorization` header of subsequent requests:
   ```
   Authorization: Bearer your_jwt_token
   ```

## Key API Endpoints

### Documents API

- `POST /documents/upload` - Upload and process documents
- `GET /documents/query` - Query processed documents
- `POST /documents/batch-query` - Process multiple queries in parallel

### Authentication API

- `POST /auth/sign-up` - Register a new user
- `POST /auth/sign-in` - Authenticate and receive a JWT token

### User API

- `GET /user/me` - Get current user information
- `GET /user` - List all users (admin only)
- `POST /user` - Create a new user (admin only)

### Prompt API

- `POST /prompt/exercise` - Generate educational exercises
- `POST /prompt/generate` - Generate educational content

### Options API

- `GET /options/subjects/by-topic/{topicId}` - Get subjects by topic ID
- `GET /options/subjects/hierarchy` - Get complete subject hierarchy

### Worksheet API

- `POST /worksheets` - Create a new worksheet (now supports question type distribution)
- `GET /worksheets` - Get all worksheets
- `GET /worksheets/{id}` - Get worksheet by ID

## API Versioning

The API currently does not use explicit versioning in the URL path. Future versions may introduce versioning if significant changes are made.

## Rate Limiting

The API may implement rate limiting to prevent abuse. Please design your applications to handle 429 (Too Many Requests) responses gracefully.

## Error Handling

The API returns standard HTTP status codes:

- 200: Success
- 400: Bad Request (invalid input)
- 401: Unauthorized (authentication required)
- 403: Forbidden (insufficient permissions)
- 404: Not Found
- 500: Internal Server Error

Error responses include a JSON body with error details:

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request"
}
```

## For More Information

For detailed information about specific endpoints, request/response schemas, and authentication requirements, please refer to the interactive OpenAPI documentation at `http://localhost:3000/api`.
