# AI Training Flow Diagram

This diagram provides an overview of the three main flows in the system: document upload, examination upload with multimodal processing, and user query processing.

```mermaid
graph TD
    subgraph "Document Upload Flow"
        A1[User] -->|Uploads curriculum documents| B1[Document Upload API]
        B1 -->|Process documents| C1[Text Extraction]
        C1 -->|Extract text content| D1[Text Chunking]
        D1 -->|Create manageable chunks| E1[Text Embedding]
        E1 -->|Generate vector embeddings| F1[Vector Database]
        F1 -->|Store document vectors| G1[Documents available for AI reference]
    end

    subgraph "Examination Upload Flow"
        A2[User] -->|Uploads examination files| B2[Document Upload API]
        B2 -->|Process with multimodal| C2[Multimodal Service]
        C2 -->|Extract text| D2[Text Extraction]
        C2 -->|Extract images| E2[Image Extraction]
        D2 --> F2[Text Processing]
        E2 --> G2[Qwen 2.5 VL Model]
        G2 -->|Analyze images| H2[Enhanced Understanding]
        F2 --> H2
        H2 -->|Generate embeddings| I2[Vector Database]
        I2 -->|Store examination vectors| J2[Examination content available for AI reference]
    end

    subgraph "Query Flow"
        A3[User] -->|Submits query| B3[Query API]
        B3 -->|Search for relevant content| C3[Vector Search]
        C3 -->|Retrieve similar vectors| D3[Vector Database]
        D3 -->|Return relevant chunks| E3[Content Retrieval]
        E3 -->|Provide context| F3[AI Response Generation]
        F3 -->|Generate response| G3[Response to User]
        E3 -->|Include image references| H3[Image Retrieval]
        H3 -->|Add images to response| G3
    end

    G1 -.-> D3
    J2 -.-> D3

    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef tertiary fill:#bfb,stroke:#333,stroke-width:1px;

    class A1,A2,A3 primary;
    class B1,C1,D1,E1,F1,G1 secondary;
    class B2,C2,D2,E2,F2,G2,H2,I2,J2 secondary;
    class B3,C3,D3,E3,F3,G3,H3 tertiary;
```

## Flow Description

### Document Upload Flow

This flow handles the processing of standard curriculum documents:

1. User uploads curriculum documents through the Document Upload API
2. The system extracts text content from the documents
3. The text is chunked into manageable pieces
4. Vector embeddings are generated for each chunk
5. The embeddings are stored in the vector database
6. The documents become available for AI reference

### Examination Upload Flow

This flow handles the processing of examination documents with multimodal content:

1. User uploads examination files through the Document Upload API
2. The Multimodal Service processes the documents
3. Both text and images are extracted from the documents
4. The Qwen 2.5 VL model analyzes the images
5. Text processing is performed on the extracted text
6. Enhanced understanding is generated by combining text and image analysis
7. Vector embeddings are generated for the enhanced content
8. The embeddings are stored in the vector database
9. The examination content becomes available for AI reference

### Query Flow

This flow handles user queries and response generation:

1. User submits a query through the Query API
2. The system performs a vector search to find relevant content
3. Similar vectors are retrieved from the vector database
4. Relevant content chunks are returned
5. The content is used to provide context for AI response generation
6. If the content includes image references, they are retrieved
7. The AI generates a response based on the context
8. The response, potentially including images, is returned to the user

## Note on "AI Training"

It's important to note that this system doesn't "train" AI models in the traditional sense. Instead, it creates a searchable knowledge base of document content that the AI can reference when answering questions. This approach is known as Retrieval-Augmented Generation (RAG).