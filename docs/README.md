# EduSG Backend Documentation

Welcome to the EduSG Backend documentation. This documentation provides comprehensive information about the EduSG backend system, including its architecture, components, and usage.

## Table of Contents

### Overview
- [System Overview](system-overview.md) - High-level overview of the system architecture and components

### Core Functionality
- [Document Processing](document-processing.md) - How documents are processed and stored
- [Multimodal Processing](multimodal-processing.md) - Processing documents with both text and images
- [Query Processing](query-processing.md) - How user queries are processed and answered
- [Subject Hierarchy API](subject-hierarchy-api.md) - API for retrieving subject hierarchy information
- [Worksheet Question Distribution](worksheet-question-distribution.md) - Enhanced worksheet creation with question type distribution

### Diagrams
- [AI Training Flow Diagram](ai-training-flow-diagram.md) - Overview of document processing and AI reference flows
- [Technical Flow Diagram](ai-training-technical-flow.md) - Detailed technical diagram of system components
- [User-Focused Flow](ai-training-user-flow.md) - Simplified diagram for non-technical users
- [Sequence Diagram](ai-training-sequence.md) - Time-based flow of document processing and querying
- [Examination Processing Pipeline](examination-processing-pipeline.md) - Detailed flow of examination document processing
- [Processing Comparison](processing-comparison.md) - Comparison of traditional vs. multimodal processing

### Reference
- [API Documentation](api-documentation.md) - API overview and OpenAPI/Swagger reference
- [Installation Guide](installation-guide.md) - How to install and configure the system

## Key Concepts

### Document Processing

The system processes uploaded documents (PDFs, etc.) by:
1. Extracting text and images
2. Chunking content into manageable pieces
3. Generating vector embeddings
4. Storing in a vector database for retrieval

### Multimodal Processing

For documents with visual elements (like examinations), the system:
1. Extracts both text and images
2. Uses the Qwen 2.5 VL model to analyze images
3. Combines text and image understanding
4. Provides enhanced responses with visual context

### AI Reference System

The system doesn't "train" AI models in the traditional sense. Instead, it:
1. Creates a searchable knowledge base of document content
2. Retrieves relevant content when users ask questions
3. Provides this content as context to the AI model
4. Generates responses based on the retrieved context

## Getting Started

To get started with the EduSG backend system:

1. Follow the [Installation Guide](installation-guide.md) to set up the system
2. Upload some documents to build your knowledge base
3. Use the API to query the system and get AI-powered responses

## Contributing

To contribute to the documentation:

1. Create or edit markdown files in the `docs` directory
2. Update the table of contents in this README.md file
3. Submit a pull request with your changes
