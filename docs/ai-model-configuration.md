# AI Model Configuration

This document describes the environment variable-based AI model configuration system implemented to centralize model selection and improve security.

## Overview

The application now uses environment variables to configure AI models for different tasks, removing the need for user-selectable models in the UI/API. This approach provides better security, consistency across environments, and easier deployment management.

## Environment Variables

### Core Configuration

- `OPENAI_API_KEY`: API key for OpenRouter/OpenAI services
- `OPENAI_MODEL`: Default fallback model for all AI operations

### Task-Specific Models

The following environment variables allow you to specify different models for specific tasks:

- `NARRATIVE_STRUCTURE_MODEL`: Model used for extracting narrative structures from examination formats
- `QUESTION_GENERATION_MODEL`: Model used for generating educational questions
- `IMAGE_GENERATION_MODEL`: Model used for generating SVG images and diagrams
- `MULTIMODAL_MODEL`: Model used for multimodal processing (text + image analysis)

### Legacy Variables (Backward Compatibility)

- `LLM_MODEL`: Used for document processing and vector database operations
- `SVG_GENERATION_MODEL`: Legacy variable for image generation (superseded by IMAGE_GENERATION_MODEL)
- `EMBEDDING_MODEL`: Used for text embeddings

## Fallback Logic

The system implements a hierarchical fallback mechanism:

1. **Task-specific model** (e.g., `NARRATIVE_STRUCTURE_MODEL`)
2. **General model** (`OPENAI_MODEL`)
3. **Default model** (`gpt-4.1-nano`)

For example, when generating questions:
1. First checks `QUESTION_GENERATION_MODEL`
2. If not set, uses `OPENAI_MODEL`
3. If neither is set, uses `gpt-4.1-nano`

## Configuration Examples

### Development Environment (.env)
```bash
# Core configuration
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=google/gemini-2.5-flash-preview

# Task-specific models
NARRATIVE_STRUCTURE_MODEL=google/gemini-2.5-flash-preview
QUESTION_GENERATION_MODEL=google/gemini-2.5-flash-preview
IMAGE_GENERATION_MODEL=anthropic/claude-3.7-sonnet
MULTIMODAL_MODEL=qwen/qwen2.5-vl-72b-instruct

# Legacy models (for backward compatibility)
LLM_MODEL=google/gemini-2.0-flash-001
SVG_GENERATION_MODEL=google/gemini-2.5-pro-preview-03-25
```

### Production Environment
```bash
# Use more powerful models for production
OPENAI_MODEL=gpt-4-turbo
NARRATIVE_STRUCTURE_MODEL=gpt-4-turbo
QUESTION_GENERATION_MODEL=gpt-4-turbo
IMAGE_GENERATION_MODEL=gpt-4-turbo
```

### Cost-Optimized Environment
```bash
# Use cheaper models for development/testing
OPENAI_MODEL=gpt-3.5-turbo
NARRATIVE_STRUCTURE_MODEL=gpt-3.5-turbo
QUESTION_GENERATION_MODEL=gpt-3.5-turbo
IMAGE_GENERATION_MODEL=gpt-3.5-turbo
```

## Implementation Details

### ModelConfigService

The `ModelConfigService` centralizes model selection logic:

```typescript
// Get model for narrative structure extraction
const model = this.modelConfigService.getNarrativeStructureModel();

// Get model for question generation
const model = this.modelConfigService.getQuestionGenerationModel();

// Get model for image generation
const model = this.modelConfigService.getImageGenerationModel();
```

### Updated Services

The following services have been updated to use the new configuration system:

- `NarrativeStructureExtractorService`: Uses `NARRATIVE_STRUCTURE_MODEL`
- `QuestionGeneratorService`: Uses `QUESTION_GENERATION_MODEL`
- `GenImageService`: Uses `IMAGE_GENERATION_MODEL`
- `DocumentsService`: Uses `LLM_MODEL` (with fallback logic)
- `MultimodalService`: Uses `MULTIMODAL_MODEL`

## Migration Guide

### From User-Selectable Models

1. Remove any model selection UI components
2. Remove model parameters from API endpoints
3. Remove model fields from DTOs and interfaces
4. Update validation logic to remove model-related checks
5. Set appropriate environment variables for your deployment

### Environment Variable Setup

1. Copy the new variables from `example.env` to your `.env` file
2. Set appropriate model values for your environment
3. Test the application to ensure all AI operations work correctly
4. Update your deployment scripts to include the new environment variables

## Benefits

1. **Security**: No model selection exposed to users
2. **Consistency**: Same models used across all requests in an environment
3. **Flexibility**: Easy to change models per environment without code changes
4. **Cost Control**: Administrators can control which models are used
5. **Deployment**: Simplified deployment with environment-specific configurations

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**: Ensure all required variables are set
2. **Invalid Model Names**: Verify model names are correct for your provider
3. **API Key Issues**: Ensure `OPENAI_API_KEY` is valid and has access to specified models
4. **Fallback Behavior**: Check logs to see which model is actually being used

### Debugging

Enable debug logging to see which models are being selected:

```bash
# The application logs which model is being used for each operation
# Look for log messages like: "Making AI request with model: gpt-4-turbo"
```
