# Document Processing

## Overview

The document processing system is responsible for processing uploaded documents, extracting their content, and storing them in a format that can be efficiently queried by the AI system.

## Components

### DocumentsService

The `DocumentsService` is the main component responsible for:
- Processing uploaded documents
- Extracting text content
- Chunking text into manageable pieces
- Generating embeddings
- Storing document vectors in the vector database

### VectorDBService

The `VectorDBService` provides:
- Integration with vector databases (Pinecone/Qdrant)
- Storage of document embeddings
- Retrieval of relevant document chunks
- Management of vector collections

### EmbeddingService

The `EmbeddingService` is responsible for:
- Generating embeddings for document chunks
- Using the Gemini Embedding model
- Converting text to vector representations

## Document Processing Flow

1. Document Upload:
   - User uploads a document through the API
   - Document is received by the `DocumentsController`
   - Document is passed to the `DocumentsService`

2. Text Extraction:
   - For PDF documents, the `PDFReader` extracts text
   - For multimodal processing, the `MultimodalService` extracts text and images
   - Text is chunked into manageable pieces

3. Content Analysis:
   - Document chunks are analyzed for special content (exercises, images, etc.)
   - Metadata is added to document chunks
   - For examination content, special processing is applied

4. Vector Generation:
   - Document chunks are converted to vector embeddings
   - Embeddings are stored in the vector database
   - Metadata is preserved for retrieval

5. Indexing:
   - Documents are indexed for efficient retrieval
   - Index is stored in the vector database
   - Document becomes available for querying

## Configuration

The document processing system can be configured through environment variables:

```
# Vector Database
VECTOR_DB_TYPE=pinecone
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX=your_pinecone_index

# Embedding Model
EMBEDDING_MODEL=gemini

# Document Processing
USE_MULTIMODAL_PROCESSING=true
```

## Supported Document Types

Currently, the system supports:
- PDF documents

Future support is planned for:
- Word documents
- PowerPoint presentations
- HTML content
- Plain text files

## Optimization

The document processing system includes several optimizations:
- Caching of processed documents
- Configurable chunking parameters
- Relevance filtering for query results
- Special handling for examination content
