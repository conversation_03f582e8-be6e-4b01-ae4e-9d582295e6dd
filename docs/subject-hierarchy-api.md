# Subject Hierarchy API

This document describes the API endpoints for retrieving subject hierarchy information.

## Endpoints

### 1. Query Parent Subjects by Topic

Retrieves all parent subjects that belong to a specified topic ID.

```
GET /options/subjects/by-topic/{topicId}
```

#### Parameters

- `topicId` (path parameter): The ID of the topic to filter by
- `type` (query parameter, optional): Filter by subject type ('parent' or 'child')

#### Responses

- `200 OK`: Subjects retrieved successfully
- `404 Not Found`: Topic not found

#### Example

```
GET /options/subjects/by-topic/123e4567-e89b-12d3-a456-426614174000?type=parent
```

Response:
```json
[
  {
    "id": "456e4567-e89b-12d3-a456-426614174000",
    "name": "Fractions",
    "description": "Operations with fractions including division and multiplication",
    "type": "parent",
    "parentId": null,
    "topics": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "label": "Mathematics",
        "value": "mathematics",
        "order": 0,
        "optionTypeId": "789e4567-e89b-12d3-a456-426614174000"
      }
    ],
    "grades": [
      {
        "id": "abc-4567-e89b-12d3-a456-426614174000",
        "label": "P5",
        "value": "p5",
        "order": 4,
        "optionTypeId": "def-4567-e89b-12d3-a456-426614174000"
      }
    ]
  }
]
```

### 2. Query Complete Subject Hierarchy

Returns a tree structure with topics at the top level, containing parent subjects and their child subjects.

```
GET /options/subjects/hierarchy
```

#### Parameters

- `topicId` (query parameter, optional): Filter by topic ID
- `gradeId` (query parameter, optional): Filter by grade ID

#### Responses

- `200 OK`: Subject hierarchy retrieved successfully
- `404 Not Found`: Topic not found (if topicId is provided but not found)

#### Example

```
GET /options/subjects/hierarchy?topicId=123e4567-e89b-12d3-a456-426614174000&gradeId=abc-4567-e89b-12d3-a456-426614174000
```

Response:
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Mathematics",
    "type": "topic",
    "children": [
      {
        "id": "456e4567-e89b-12d3-a456-426614174000",
        "name": "Fractions",
        "type": "parent",
        "description": "Operations with fractions including division and multiplication",
        "children": [
          {
            "id": "789e4567-e89b-12d3-a456-426614174000",
            "name": "Dividing a whole number by a proper fraction",
            "type": "lesson",
            "description": "Understanding and calculating division with fractions",
            "children": []
          }
        ]
      }
    ]
  }
]
```

## Data Models

### Subject

```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "type": "parent | child",
  "parentId": "string | null",
  "topics": [
    {
      "id": "string",
      "label": "string",
      "value": "string",
      "order": 0,
      "optionTypeId": "string"
    }
  ],
  "grades": [
    {
      "id": "string",
      "label": "string",
      "value": "string",
      "order": 0,
      "optionTypeId": "string"
    }
  ]
}
```

### SubjectHierarchyItem

```json
{
  "id": "string",
  "name": "string",
  "type": "topic | parent | lesson",
  "description": "string",
  "children": [
    {
      "id": "string",
      "name": "string",
      "type": "parent | lesson",
      "description": "string",
      "children": []
    }
  ]
}
```
