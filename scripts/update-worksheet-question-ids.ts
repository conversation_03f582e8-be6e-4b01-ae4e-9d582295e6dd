#!/usr/bin/env ts-node

import * as mongoose from 'mongoose';
import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

// Load environment variables (if dotenv is available)
try {
  require('dotenv').config();
} catch (e) {
  // dotenv not available, use process.env directly
}

interface Logger {
  log: (message: string) => void;
  warn: (message: string) => void;
  error: (message: string, stack?: string) => void;
  debug: (message: string) => void;
}

const logger: Logger = {
  log: (message: string) => console.log(`[WorksheetQuestionIdsMigration] ${message}`),
  warn: (message: string) => console.warn(`[WorksheetQuestionIdsMigration] ${message}`),
  error: (message: string, stack?: string) => {
    console.error(`[WorksheetQuestionIdsMigration] ${message}`);
    if (stack) console.error(stack);
  },
  debug: (message: string) => console.log(`[WorksheetQuestionIdsMigration] ${message}`)
};

// MongoDB Schema
const WorksheetPromptResultSchema = new mongoose.Schema({
  worksheetId: { type: String, required: true },
  promptResult: { type: Object, required: true },
  currentQuestionCount: { type: Number, default: 0 },
  totalQuestionCount: { type: Number, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, { timestamps: true });

// PostgreSQL Entity (simplified)
interface Worksheet {
  id: string;
  questionIds?: string[];
  questionMetadata?: any;
  maxQuestions?: number;
}

class WorksheetQuestionIdsMigration {
  private mongoConnection: mongoose.Connection;
  private pgDataSource: DataSource;
  private WorksheetPromptResultModel: mongoose.Model<any>;

  async initialize(): Promise<void> {
    logger.log('Initializing database connections...');

    // Initialize MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/edusg';
    await mongoose.connect(mongoUri);
    this.mongoConnection = mongoose.connection;
    this.WorksheetPromptResultModel = mongoose.model('WorksheetPromptResult', WorksheetPromptResultSchema);
    logger.log('✅ MongoDB connected');

    // Initialize PostgreSQL
    this.pgDataSource = new DataSource({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      username: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      database: process.env.DB_NAME || 'edusg',
      synchronize: false,
      logging: false,
    });

    await this.pgDataSource.initialize();
    logger.log('✅ PostgreSQL connected');
  }

  async cleanup(): Promise<void> {
    if (this.mongoConnection) {
      await mongoose.disconnect();
      logger.log('MongoDB disconnected');
    }
    if (this.pgDataSource && this.pgDataSource.isInitialized) {
      await this.pgDataSource.destroy();
      logger.log('PostgreSQL disconnected');
    }
  }

  async dryRun(): Promise<void> {
    logger.log('Starting dry run for worksheet question IDs migration...');

    try {
      const promptResults = await this.WorksheetPromptResultModel.find({
        'promptResult.result': { $exists: true, $ne: [] }
      }).exec();

      logger.log(`Found ${promptResults.length} worksheet prompt results`);

      let needsUpdateCount = 0;
      let alreadyHasIdsCount = 0;
      let noQuestionsCount = 0;

      for (const promptResult of promptResults) {
        const worksheetId = promptResult.worksheetId;
        const questions = promptResult.promptResult?.result || [];

        if (!Array.isArray(questions) || questions.length === 0) {
          noQuestionsCount++;
          continue;
        }

        const hasIds = questions.every(q => q && q.id);
        if (hasIds) {
          alreadyHasIdsCount++;
        } else {
          needsUpdateCount++;
          logger.log(`Worksheet ${worksheetId}: ${questions.length} questions need IDs`);
        }
      }

      logger.log(`Dry run results:`);
      logger.log(`- Total worksheets: ${promptResults.length}`);
      logger.log(`- Need updates: ${needsUpdateCount}`);
      logger.log(`- Already have IDs: ${alreadyHasIdsCount}`);
      logger.log(`- No questions: ${noQuestionsCount}`);

    } catch (error) {
      logger.error(`Dry run failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  async run(): Promise<void> {
    logger.log('Starting worksheet question IDs migration...');

    try {
      const promptResults = await this.WorksheetPromptResultModel.find({
        'promptResult.result': { $exists: true, $ne: [] }
      }).limit(3).exec(); // Test with just 3 worksheets first

      logger.log(`Found ${promptResults.length} worksheet prompt results to process (testing with first 3)`);

      if (promptResults.length === 0) {
        logger.log('No worksheets found that need processing. Migration completed.');
        return;
      }

      let processedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;
      const errorDetails: string[] = [];

      for (const promptResult of promptResults) {
        try {
          const result = await this.processWorksheetPromptResult(promptResult);
          if (result.skipped) {
            skippedCount++;
          } else {
            processedCount++;
          }

          if ((processedCount + skippedCount) % 10 === 0) {
            logger.log(`Progress: ${processedCount + skippedCount}/${promptResults.length} worksheets (${processedCount} processed, ${skippedCount} skipped)...`);
          }
        } catch (error) {
          errorCount++;
          const errorMsg = `Worksheet ${promptResult.worksheetId}: ${error.message}`;
          errorDetails.push(errorMsg);
          logger.error(errorMsg, error.stack);
        }
      }

      logger.log(
        `Migration completed! Processed: ${processedCount}, Skipped: ${skippedCount}, Errors: ${errorCount}, Total: ${promptResults.length}`
      );

      if (errorCount > 0) {
        logger.warn('Errors encountered during migration:');
        errorDetails.forEach(error => logger.warn(`- ${error}`));
      }

    } catch (error) {
      logger.error(`Migration failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async processWorksheetPromptResult(promptResult: any): Promise<{ skipped: boolean }> {
    const worksheetId = promptResult.worksheetId;
    const questions = promptResult.promptResult?.result || [];

    if (!Array.isArray(questions) || questions.length === 0) {
      logger.warn(`No questions found for worksheet ${worksheetId}`);
      return { skipped: true };
    }

    // Check if questions already have IDs
    const hasIds = questions.every(q => q && q.id);
    if (hasIds) {
      logger.debug(`Worksheet ${worksheetId} already has question IDs, skipping...`);
      return { skipped: true };
    }

    logger.log(`Processing worksheet ${worksheetId} with ${questions.length} questions`);

    // Generate IDs for questions that don't have them
    const updatedQuestions = questions.map((question, index) => {
      if (!question) {
        logger.warn(`Null question found at index ${index} for worksheet ${worksheetId}`);
        return question;
      }

      // If question already has an ID, keep it
      if (question.id) {
        return question;
      }

      // Generate new UUID for the question
      const questionId = uuidv4();
      logger.debug(`Generated ID ${questionId} for question ${index + 1} in worksheet ${worksheetId}`);

      return {
        ...question,
        id: questionId
      };
    });

    // Extract question IDs for PostgreSQL
    const questionIds = updatedQuestions
      .filter(q => q && q.id)
      .map(q => q.id);

    // Update MongoDB with questions that have IDs
    await this.WorksheetPromptResultModel.findOneAndUpdate(
      { worksheetId },
      {
        'promptResult.result': updatedQuestions
      }
    ).exec();

    logger.debug(`Updated MongoDB for worksheet ${worksheetId} with ${questionIds.length} question IDs`);

    // Update PostgreSQL worksheet with question IDs
    await this.updatePostgreSQLWorksheet(worksheetId, questionIds);

    logger.log(`Successfully processed worksheet ${worksheetId}`);
    return { skipped: false };
  }

  private async updatePostgreSQLWorksheet(worksheetId: string, questionIds: string[]): Promise<void> {
    try {
      // Check if worksheet exists in PostgreSQL and get current questionIds
      const result = await this.pgDataSource.query(
        'SELECT id, "maxQuestions", "questionIds", "questionMetadata" FROM worksheets WHERE id = $1',
        [worksheetId]
      );

      if (result.length === 0) {
        logger.warn(`Worksheet ${worksheetId} not found in PostgreSQL, skipping...`);
        return;
      }

      const worksheet = result[0];
      logger.debug(`Before update - Worksheet ${worksheetId}: questionIds = ${JSON.stringify(worksheet.questionIds)}`);

      // Create question metadata
      const questionMetadata = {
        lastQuestionUpdate: new Date(),
        questionVersion: 1,
        hasUnsavedChanges: false,
        collaborators: [],
        lockStatus: {
          isLocked: false
        }
      };

      // Update the worksheet with question IDs and metadata
      const updateResult = await this.pgDataSource.query(
        `UPDATE worksheets
         SET "questionIds" = $1,
             "questionMetadata" = $2,
             "maxQuestions" = $3,
             "updatedAt" = NOW()
         WHERE id = $4
         RETURNING "questionIds", "questionMetadata"`,
        [
          JSON.stringify(questionIds),
          JSON.stringify(questionMetadata),
          Math.max(worksheet.maxQuestions || 100, questionIds.length),
          worksheetId
        ]
      );

      if (updateResult.length > 0) {
        logger.debug(`After update - Worksheet ${worksheetId}: questionIds = ${JSON.stringify(updateResult[0].questionIds)}`);
        logger.debug(`Updated PostgreSQL worksheet ${worksheetId} with ${questionIds.length} question IDs`);
      } else {
        logger.error(`Update failed for worksheet ${worksheetId} - no rows affected`);
      }

    } catch (error) {
      logger.error(`Error updating PostgreSQL for worksheet ${worksheetId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}

async function bootstrap() {
  const migration = new WorksheetQuestionIdsMigration();

  try {
    await migration.initialize();

    // Parse command line arguments
    const args = process.argv.slice(2);
    const isDryRun = args.includes('--dry-run');
    const isForced = args.includes('--force');

    if (isDryRun) {
      logger.log('Running in DRY RUN mode - no changes will be made');
      await migration.dryRun();
    } else if (isForced) {
      logger.log('Running migration - this will update your database');
      await migration.run();
    } else {
      logger.warn('Usage:');
      logger.warn('  npm run script:update-worksheet-ids -- --dry-run    # Preview changes');
      logger.warn('  npm run script:update-worksheet-ids -- --force      # Apply changes');
      logger.warn('');
      logger.warn('This script will:');
      logger.warn('1. Add unique IDs to questions in MongoDB promptResult.result arrays');
      logger.warn('2. Update PostgreSQL worksheets with corresponding questionIds arrays');
      logger.warn('3. Ensure both databases stay in sync for question ordering');
      process.exit(1);
    }

    await migration.cleanup();
    logger.log('Migration completed successfully');
    process.exit(0);

  } catch (error) {
    logger.error(`Migration failed: ${error.message}`, error.stack);
    await migration.cleanup();
    process.exit(1);
  }
}

bootstrap();
