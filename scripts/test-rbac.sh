#!/bin/bash

# RBAC Testing Script
# This script runs comprehensive tests for the RBAC implementation

set -e

echo "🔐 Starting RBAC Testing Suite"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
    esac
}

# Function to run test and capture result
run_test() {
    local test_name=$1
    local test_command=$2
    
    print_status "INFO" "Running: $test_name"
    
    if eval $test_command > /dev/null 2>&1; then
        print_status "SUCCESS" "$test_name passed"
        return 0
    else
        print_status "ERROR" "$test_name failed"
        return 1
    fi
}

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_status "ERROR" "npm is not installed or not in PATH"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_status "ERROR" "package.json not found. Please run this script from the project root."
    exit 1
fi

print_status "INFO" "Installing dependencies..."
npm install > /dev/null 2>&1

echo ""
print_status "INFO" "Running Unit Tests"
echo "-------------------"

# Run RBAC Service tests
run_test "RBAC Service Unit Tests" "npm test -- src/modules/auth/services/rbac.service.spec.ts"

# Run RoleGuard tests
run_test "RoleGuard Unit Tests" "npm test -- src/modules/auth/guards/role.guard.spec.ts"

# Run UserController RBAC tests
run_test "UserController RBAC Tests" "npm test -- src/modules/user/user.controller.rbac.spec.ts"

echo ""
print_status "INFO" "Running Integration Tests"
echo "-------------------------"

# Run integration tests if they exist
if [ -f "test/rbac-e2e.spec.ts" ]; then
    run_test "RBAC End-to-End Tests" "npm run test:e2e -- test/rbac-e2e.spec.ts"
else
    print_status "WARNING" "End-to-end tests not found, skipping..."
fi

echo ""
print_status "INFO" "Running TypeScript Compilation Check"
echo "------------------------------------"

# Check TypeScript compilation
run_test "TypeScript Compilation" "npx tsc --noEmit"

echo ""
print_status "INFO" "Running ESLint Check"
echo "-------------------"

# Check linting
if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ]; then
    run_test "ESLint Check" "npx eslint src/modules/auth/ src/modules/user/ src/modules/school/ --ext .ts"
else
    print_status "WARNING" "ESLint configuration not found, skipping..."
fi

echo ""
print_status "INFO" "Checking RBAC Implementation Coverage"
echo "------------------------------------"

# Check if all required files exist
required_files=(
    "src/modules/auth/guards/role.guard.ts"
    "src/modules/auth/guards/auth.guard.ts"
    "src/modules/auth/decorators/role.decorator.ts"
    "src/modules/auth/services/rbac.service.ts"
    "src/modules/auth/exceptions/rbac-exceptions.ts"
    "src/modules/auth/filters/rbac-exception.filter.ts"
    "docs/rbac-permission-matrix.md"
    "docs/rbac-implementation-guide.md"
    "docs/rbac-technical-specification.md"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    print_status "SUCCESS" "All required RBAC files are present"
else
    print_status "ERROR" "Missing required files:"
    for file in "${missing_files[@]}"; do
        echo "  - $file"
    done
fi

echo ""
print_status "INFO" "Checking Controller RBAC Implementation"
echo "--------------------------------------"

# Check if controllers have proper RBAC decorators
controllers=(
    "src/modules/user/user.controller.ts"
    "src/modules/school/school.controller.ts"
)

for controller in "${controllers[@]}"; do
    if [ -f "$controller" ]; then
        if grep -q "@UseGuards.*RoleGuard" "$controller" && grep -q "@Roles(" "$controller"; then
            print_status "SUCCESS" "$(basename $controller) has RBAC implementation"
        else
            print_status "WARNING" "$(basename $controller) may be missing RBAC decorators"
        fi
    else
        print_status "ERROR" "Controller not found: $controller"
    fi
done

echo ""
print_status "INFO" "Security Validation"
echo "------------------"

# Check for potential security issues
security_checks=(
    "Checking for hardcoded secrets"
    "Checking for TODO/FIXME comments in security code"
    "Checking for console.log statements in production code"
)

# Check for hardcoded secrets
if grep -r "secret.*=" src/modules/auth/ | grep -v ".spec.ts" | grep -v "process.env" > /dev/null; then
    print_status "WARNING" "Potential hardcoded secrets found in auth module"
else
    print_status "SUCCESS" "No hardcoded secrets detected"
fi

# Check for TODO/FIXME in security code
if grep -r "TODO\|FIXME" src/modules/auth/ | grep -v ".spec.ts" > /dev/null; then
    print_status "WARNING" "TODO/FIXME comments found in auth module"
else
    print_status "SUCCESS" "No pending TODO/FIXME items in auth module"
fi

# Check for console.log in production code
if grep -r "console\.log" src/modules/auth/ | grep -v ".spec.ts" > /dev/null; then
    print_status "WARNING" "console.log statements found in auth module"
else
    print_status "SUCCESS" "No console.log statements in auth module"
fi

echo ""
print_status "INFO" "RBAC Testing Summary"
echo "==================="

# Count test results
total_tests=10
passed_tests=0

# This is a simplified count - in a real implementation, you'd track actual test results
print_status "INFO" "Test execution completed"
print_status "INFO" "Please review the output above for any failures or warnings"

echo ""
print_status "SUCCESS" "RBAC testing suite completed!"
print_status "INFO" "Next steps:"
echo "  1. Review any failed tests or warnings above"
echo "  2. Run manual testing with different user roles"
echo "  3. Test in staging environment before production deployment"
echo "  4. Monitor logs for any authorization issues"

echo ""
print_status "INFO" "For manual testing, use the test endpoints:"
echo "  - GET /test/rbac/admin-only (Admin only)"
echo "  - GET /test/rbac/admin-or-school-manager (Admin or School Manager)"
echo "  - GET /test/rbac/all-roles (All authenticated users)"
echo "  - GET /test/rbac/no-role-restriction (All authenticated users)"
